#!/usr/bin/env python3
"""
查詢代理 - 專門處理 HTS 條目查詢和基礎信息檢索
"""

import os
import sys
import json
from typing import Dict, List, Any, Optional

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.agents.base_agent import BaseAgent
from src.models.hts import HTSSection, HTSChapter, HTSHeading, HTSSubheading, Country
from src.prompts.professional_prompts import ProfessionalPrompts, ResponseTemplates, QualityEnhancers
import logging

logger = logging.getLogger(__name__)

class QueryAgent(BaseAgent):
    """查詢代理 - 專門處理 HTS 條目查詢和基礎信息檢索"""
    
    def __init__(self):
        super().__init__(
            name="query_agent",
            description="專門處理 HTS 條目查詢、商品分類查找和基礎關稅信息檢索",
            model="gpt-3.5-turbo"
        )
        
        # 添加專門的工具
        self.add_tool("search_hts", self._search_hts_database)
        self.add_tool("get_hts_detail", self._get_hts_detail)
        self.add_tool("search_by_keyword", self._search_by_keyword)
        self.add_tool("get_chapter_info", self._get_chapter_info)
    
    def get_system_prompt(self) -> str:
        """獲取查詢代理的專業系統提示詞"""
        return ProfessionalPrompts.get_query_agent_prompt()
    
    def process_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理查詢請求"""
        try:
            # 分析查詢類型
            query_type = self._analyze_query_type(query)
            
            # 根據查詢類型執行不同的處理邏輯
            if query_type == "hts_search":
                result = self._handle_hts_search(query, context)
            elif query_type == "product_classification":
                result = self._handle_product_classification(query, context)
            elif query_type == "tariff_inquiry":
                result = self._handle_tariff_inquiry(query, context)
            else:
                result = self._handle_general_query(query, context)
            
            # 記錄交互
            self.log_interaction(query, result.get('response', ''), {
                'query_type': query_type,
                'context': context
            })
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 查詢代理處理錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': f"抱歉，處理您的查詢時發生錯誤：{e}"
            }
    
    def _analyze_query_type(self, query: str) -> str:
        """分析查詢類型"""
        query_lower = query.lower()
        
        if any(keyword in query_lower for keyword in ['hts', '條目', '編號', '代碼']):
            return "hts_search"
        elif any(keyword in query_lower for keyword in ['分類', '歸類', '屬於', '什麼類別']):
            return "product_classification"
        elif any(keyword in query_lower for keyword in ['稅率', '關稅', '多少', '費用']):
            return "tariff_inquiry"
        else:
            return "general_query"
    
    def _handle_hts_search(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理 HTS 搜尋查詢"""
        # 提取關鍵字
        keywords = self._extract_keywords(query)
        
        # 搜尋資料庫
        search_results = self._search_hts_database(keywords)
        
        if search_results:
            response = self._format_hts_search_response(search_results, query)
            return {
                'success': True,
                'response': response,
                'data': search_results,
                'query_type': 'hts_search'
            }
        else:
            return {
                'success': True,
                'response': f"很抱歉，沒有找到與「{query}」相關的 HTS 條目。請嘗試使用更具體的商品描述或關鍵字。",
                'data': [],
                'query_type': 'hts_search'
            }
    
    def _handle_product_classification(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理商品分類查詢"""
        keywords = self._extract_keywords(query)
        search_results = self._search_hts_database(keywords)
        
        if search_results:
            response = self._format_classification_response(search_results, query)
            return {
                'success': True,
                'response': response,
                'data': search_results,
                'query_type': 'product_classification'
            }
        else:
            response = f"""根據您的查詢「{query}」，我無法在資料庫中找到完全匹配的 HTS 條目。

建議您：
1. 提供更詳細的商品描述
2. 包含商品的材質、用途、製造方式等信息
3. 參考類似商品的分類

如需專業的商品分類建議，建議諮詢海關經紀人或貿易專家。"""
            
            return {
                'success': True,
                'response': response,
                'data': [],
                'query_type': 'product_classification'
            }
    
    def _handle_tariff_inquiry(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理關稅查詢"""
        keywords = self._extract_keywords(query)
        search_results = self._search_hts_database(keywords)
        
        if search_results:
            response = self._format_tariff_response(search_results, query)
            return {
                'success': True,
                'response': response,
                'data': search_results,
                'query_type': 'tariff_inquiry'
            }
        else:
            return {
                'success': True,
                'response': f"無法找到與「{query}」相關的關稅信息。請提供更具體的商品描述或 HTS 條目編號。",
                'data': [],
                'query_type': 'tariff_inquiry'
            }
    
    def _handle_general_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理一般查詢"""
        # 嘗試關鍵字搜尋
        keywords = self._extract_keywords(query)
        search_results = self._search_hts_database(keywords)
        
        if search_results:
            response = f"""根據您的查詢「{query}」，我找到了以下相關的 HTS 條目：

{self._format_search_results(search_results)}

如果您需要更具體的信息，請告訴我您想了解的具體方面。"""
        else:
            response = f"""關於您的查詢「{query}」，我沒有找到直接相關的 HTS 條目。

我可以幫您：
1. 搜尋特定的 HTS 條目
2. 查詢商品的關稅分類
3. 提供關稅稅率信息
4. 解釋 HTS 系統的結構

請提供更多詳細信息，我會盡力協助您。"""
        
        return {
            'success': True,
            'response': response,
            'data': search_results,
            'query_type': 'general_query'
        }
    
    def _extract_keywords(self, query: str) -> List[str]:
        """從查詢中提取關鍵字"""
        # 簡單的關鍵字提取邏輯
        import re
        
        # 移除常見的停用詞
        stop_words = {'的', '是', '在', '有', '和', '或', '但', '如果', '什麼', '怎麼', '多少', '哪個'}
        
        # 提取中英文詞彙
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', query)
        keywords = [word.lower() for word in words if word.lower() not in stop_words and len(word) > 1]
        
        return keywords[:5]  # 限制關鍵字數量
    
    def _search_hts_database(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """搜尋 HTS 資料庫"""
        try:
            from flask import current_app
            from src.models.hts import db
            
            if not current_app:
                # 如果沒有 Flask 上下文，返回模擬數據
                return self._get_mock_search_results(keywords)
            
            results = []
            
            # 搜尋子標題
            for keyword in keywords:
                subheadings = HTSSubheading.query.filter(
                    HTSSubheading.description.ilike(f'%{keyword}%')
                ).limit(5).all()
                
                for subheading in subheadings:
                    result = {
                        'id': subheading.id,
                        'description': subheading.description,
                        'unit_of_quantity': subheading.unit_of_quantity,
                        'general_rate': subheading.general_rate,
                        'special_rate': subheading.special_rate,
                        'column_2_rate': subheading.column_2_rate,
                        'heading': {
                            'id': subheading.heading.id,
                            'description': subheading.heading.description
                        } if subheading.heading else None,
                        'chapter': {
                            'id': subheading.heading.chapter.id,
                            'title': subheading.heading.chapter.title
                        } if subheading.heading and subheading.heading.chapter else None
                    }
                    results.append(result)
            
            return results[:10]  # 限制結果數量
            
        except Exception as e:
            logger.error(f"資料庫搜尋錯誤: {e}")
            return self._get_mock_search_results(keywords)
    
    def _get_mock_search_results(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """獲取模擬搜尋結果"""
        mock_results = [
            {
                'id': '0101.21.00',
                'description': 'Horses: Pure bred breeding animals',
                'unit_of_quantity': 'No.',
                'general_rate': 'Free',
                'special_rate': 'Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)',
                'column_2_rate': 'Free',
                'heading': {'id': '0101', 'description': 'Live horses, asses, mules and hinnies'},
                'chapter': {'id': 1, 'title': 'Live Animals'}
            },
            {
                'id': '8471.30.01',
                'description': 'Portable automatic data processing machines, weighing not more than 10 kg',
                'unit_of_quantity': 'No.',
                'general_rate': 'Free',
                'special_rate': 'Free',
                'column_2_rate': '35%',
                'heading': {'id': '8471', 'description': 'Automatic data processing machines and units thereof'},
                'chapter': {'id': 84, 'title': 'Nuclear Reactors, Boilers, Machinery and Mechanical Appliances'}
            }
        ]
        
        # 根據關鍵字過濾結果
        if keywords:
            filtered_results = []
            for result in mock_results:
                for keyword in keywords:
                    if keyword.lower() in result['description'].lower():
                        filtered_results.append(result)
                        break
            return filtered_results
        
        return mock_results
    
    def _format_hts_search_response(self, results: List[Dict[str, Any]], query: str) -> str:
        """格式化 HTS 搜尋回應 - 使用專業模板"""
        if not results:
            return self._format_no_results_response(query)

        # 使用主要結果創建專業回應
        main_result = results[0]

        # 準備模板數據
        template_data = {
            'hts_code': main_result['id'],
            'description': main_result['description'],
            'unit': main_result['unit_of_quantity'],
            'general_rate': main_result['general_rate'],
            'special_rate': main_result.get('special_rate', 'N/A'),
            'column2_rate': main_result.get('column_2_rate', 'N/A'),
            'trade_agreements': self._get_applicable_trade_agreements(main_result),
            'classification_notes': self._get_classification_notes(main_result),
            'required_documents': self._get_required_documents(main_result),
            'professional_advice': self._get_professional_advice(main_result, query)
        }

        # 使用專業模板
        response = ResponseTemplates.hts_classification_template().format(**template_data)

        # 如果有多個結果，添加其他選項
        if len(results) > 1:
            response += "\n**🔍 其他可能的分類選項：**\n"
            for i, result in enumerate(results[1:4], 2):  # 顯示2-4個額外選項
                response += f"• **HTS {result['id']}**: {result['description']}\n"
                response += f"  稅率: {result['general_rate']}\n\n"

        # 添加專業背景和合規提醒
        response = QualityEnhancers.add_professional_context(response, "hts_classification")
        response = QualityEnhancers.add_compliance_reminder(response)

        return response
    
    def _format_classification_response(self, results: List[Dict[str, Any]], query: str) -> str:
        """格式化商品分類回應"""
        response = f"根據您的商品「{query}」，可能的 HTS 分類如下：\n\n"
        
        for i, result in enumerate(results[:3], 1):
            response += f"**選項 {i}: HTS {result['id']}**\n"
            response += f"   分類描述：{result['description']}\n"
            response += f"   適用稅率：{result['general_rate']}\n"
            if result.get('chapter'):
                response += f"   章節：第 {result['chapter']['id']} 章 - {result['chapter']['title']}\n"
            response += "\n"
        
        response += "**重要提醒：**\n"
        response += "- 最終的 HTS 分類需要根據商品的具體特性確定\n"
        response += "- 建議諮詢專業的海關經紀人或貿易專家\n"
        response += "- 可以向美國海關提交預先裁定申請以獲得官方確認\n"
        
        return response
    
    def _format_tariff_response(self, results: List[Dict[str, Any]], query: str) -> str:
        """格式化關稅查詢回應"""
        response = f"關於「{query}」的關稅信息：\n\n"
        
        for i, result in enumerate(results[:3], 1):
            response += f"**HTS {result['id']}**\n"
            response += f"   商品：{result['description']}\n"
            response += f"   一般稅率：{result['general_rate']}\n"
            response += f"   特殊稅率：{result['special_rate']}\n"
            response += f"   第二欄稅率：{result['column_2_rate']}\n"
            response += f"   計量單位：{result['unit_of_quantity']}\n\n"
        
        response += "**稅率說明：**\n"
        response += "- 一般稅率：適用於大多數國家\n"
        response += "- 特殊稅率：適用於有貿易協定的國家\n"
        response += "- 第二欄稅率：適用於特定情況或國家\n"
        
        return response
    
    def _format_search_results(self, results: List[Dict[str, Any]]) -> str:
        """格式化搜尋結果"""
        if not results:
            return "沒有找到相關結果。"
        
        formatted = ""
        for result in results[:5]:
            formatted += f"• HTS {result['id']}: {result['description']}\n"
            formatted += f"  稅率: {result['general_rate']}\n\n"
        
        return formatted
    
    def _get_hts_detail(self, hts_id: str) -> Dict[str, Any]:
        """獲取 HTS 條目詳細信息"""
        # 這個方法可以被其他代理調用
        results = self._search_hts_database([hts_id])
        return results[0] if results else None
    
    def _search_by_keyword(self, keyword: str) -> List[Dict[str, Any]]:
        """按關鍵字搜尋"""
        return self._search_hts_database([keyword])
    
    def _get_chapter_info(self, chapter_id: int) -> Dict[str, Any]:
        """獲取章節信息"""
        # 模擬章節信息
        chapters = {
            1: {"id": 1, "title": "Live Animals", "section": "Live Animals; Animal Products"},
            84: {"id": 84, "title": "Nuclear Reactors, Boilers, Machinery and Mechanical Appliances", "section": "Machinery and Mechanical Appliances; Electrical Equipment"}
        }
        return chapters.get(chapter_id, {})

    def _format_no_results_response(self, query: str) -> str:
        """格式化無結果回應"""
        return f"""**🔍 查詢結果**

很抱歉，沒有找到與「{query}」直接相關的 HTS 條目。

**💡 建議改進查詢：**
- 提供更具體的商品描述（材質、用途、製造方式）
- 使用英文商品名稱或行業術語
- 描述商品的主要功能和特性
- 提供商品的技術規格或標準

**🔧 替代查詢方式：**
- 搜尋商品的主要材質（如：steel, plastic, cotton）
- 查找商品的用途類別（如：machinery, textile, food）
- 參考類似商品的分類

**📞 專業協助：**
如需專業的 HTS 分類協助，建議：
- 諮詢持牌海關經紀人
- 向美國海關申請預先裁定（Prior Ruling）
- 聯繫貿易合規專家

---
*準確的 HTS 分類對於合規進口至關重要，建議在不確定時尋求專業協助。*"""

    def _get_applicable_trade_agreements(self, result: Dict[str, Any]) -> str:
        """獲取適用的貿易協定"""
        special_rate = result.get('special_rate', '')

        agreements = []
        if 'A+' in special_rate:
            agreements.append("• **GSP (一般化優惠制度)** - 適用於指定發展中國家")
        if 'CA' in special_rate:
            agreements.append("• **USMCA (美墨加協定)** - 適用於加拿大和墨西哥")
        if 'AU' in special_rate:
            agreements.append("• **美澳自由貿易協定** - 適用於澳洲")
        if 'SG' in special_rate:
            agreements.append("• **美新自由貿易協定** - 適用於新加坡")
        if 'KR' in special_rate:
            agreements.append("• **美韓自由貿易協定** - 適用於韓國")

        if not agreements:
            return "• 目前無特殊優惠稅率協定適用"

        return "\n".join(agreements)

    def _get_classification_notes(self, result: Dict[str, Any]) -> str:
        """獲取分類注意事項"""
        hts_code = result['id']

        # 基於 HTS 代碼提供相關注意事項
        if hts_code.startswith('01'):
            return """• 活體動物需要符合動植物檢疫要求
• 需要 USDA 進口許可證
• 可能需要隔離檢疫期
• 運輸條件必須符合動物福利標準"""
        elif hts_code.startswith('84'):
            return """• 機械設備可能需要 CE 標誌或 UL 認證
• 某些設備需要 FCC 認證（如含電子組件）
• 注意是否涉及出口管制清單
• 考慮是否需要安裝和維修服務"""
        elif hts_code.startswith('87'):
            return """• 車輛需要符合 DOT 和 EPA 標準
• 可能需要 NHTSA 合規證明
• 注意排放標準要求
• 考慮州級註冊要求"""
        else:
            return """• 確認商品是否需要特殊許可或認證
• 檢查是否涉及反傾銷或反補貼稅
• 注意原產地標記要求
• 確認包裝和標籤合規性"""

    def _get_required_documents(self, result: Dict[str, Any]) -> str:
        """獲取所需文件"""
        return """• **商業發票** (Commercial Invoice) - 詳細商品描述和價值
• **裝箱單** (Packing List) - 包裝詳情和重量
• **提單/空運單** (Bill of Lading/Air Waybill) - 運輸文件
• **原產地證明** (Certificate of Origin) - 如適用優惠稅率
• **進口許可證** (Import License) - 如商品受管制
• **檢驗證書** (Inspection Certificate) - 如適用特殊要求"""

    def _get_professional_advice(self, result: Dict[str, Any], query: str) -> str:
        """獲取專業建議"""
        hts_code = result['id']
        general_rate = result.get('general_rate', '')

        advice = []

        # 稅率相關建議
        if general_rate == 'Free':
            advice.append("✅ 此商品享有免稅待遇，有利於降低進口成本")
        elif '%' in general_rate:
            advice.append(f"💰 從價稅率為 {general_rate}，稅額將根據商品價值計算")
        elif '¢' in general_rate:
            advice.append(f"📏 從量稅率為 {general_rate}，稅額將根據重量或數量計算")

        # 分類相關建議
        if len(hts_code.split('.')) > 1:
            advice.append("🎯 建議進行詳細的商品技術分析以確認精確分類")

        # 合規建議
        advice.append("📋 建議建立標準作業程序以確保持續合規")
        advice.append("🔄 定期檢視分類的準確性，特別是在商品規格變更時")

        return "\n".join([f"• {item}" for item in advice])
