# TARIFFED 專案分析

## 專案目標

TARIFFED 專案旨在透過 AI 代理處理關稅相關問題，並提供一個 Web 應用程式來展示結果。其核心目標是：

1.  使用 AI 代理處理關稅相關問題，例如：
    *   美國主要進口國是哪些？
    *   關稅政策變化對平均關稅率的潛在影響？
    *   國內生產的替代商品有哪些？
2.  建立一個自定義的 SQL 資料庫，包含美國協調關稅表 (HTS) 和所有國家的潛在關稅率。
3.  開發一個 Web 應用程式，讓使用者可以與 AI 代理互動並獲取問題的答案。

## 原始技術棧 (需替換)

原始專案主要基於 Microsoft Azure 生態系統，使用了以下技術：

*   **AI 代理服務**：Azure AI Agent Service
*   **搜尋與資料接地**：Grounding with Bing Search
*   **後端框架**：.NET 9 Framework, .NET Aspire
*   **前端框架**：Blazor (基於 .NET 的 Web UI 框架)
*   **資料庫**：SQL Server 2022
*   **開發工具**：Visual Studio 2022, Azure CLI
*   **容器化**：Docker Desktop

## 核心功能與替代方案思考

由於使用者要求避免使用 Azure 相關技術，我需要為每個核心功能尋找替代方案：

### 1. AI 代理與自然語言處理 (NLP)

*   **原始方案**：Azure AI Agent Service, gpt-4o
*   **替代方案思考**：
    *   **大型語言模型 (LLM)**：可以考慮使用開源的 LLM 模型（例如 Llama 2, Mistral, Gemma 等）並在本地部署，或者使用非 Azure 的雲端 LLM 服務（例如 OpenAI API, Anthropic Claude API）。由於專案目標是處理關稅相關問題，可能需要對模型進行微調或使用 RAG (Retrieval Augmented Generation) 技術。
    *   **代理框架**：Python 生態系統中有許多成熟的代理框架，例如 LangChain, LlamaIndex 等，可以用來構建和協調 AI 代理。

### 2. 資料庫與資料管理

*   **原始方案**：SQL Server 2022
*   **替代方案思考**：
    *   **關聯式資料庫**：PostgreSQL 或 MySQL 是常見的開源替代方案，具有良好的社群支持和豐富的功能。SQLite 也可以考慮用於輕量級應用。
    *   **資料導入**：原始專案提到 `init.sql` 腳本用於初始化資料庫並填充數據。我需要研究 HTS 資料的來源和格式，並編寫腳本將其導入到選定的替代資料庫中。

### 3. Web 應用程式 (前後端)

*   **原始方案**：Blazor (前端), .NET Aspire (後端服務協調)
*   **替代方案思考**：
    *   **後端**：Python 的 Flask 或 FastAPI 是輕量級且功能強大的 Web 框架，非常適合構建 RESTful API。Node.js 的 Express 也是一個選擇。
    *   **前端**：React, Vue.js 或 Angular 是主流的 JavaScript 前端框架，可以提供豐富的使用者介面。純 HTML/CSS/JavaScript 也可以，但開發效率可能較低。
    *   **API 服務**：需要設計 RESTful API 接口，讓前端可以與後端 AI 代理和資料庫進行通訊。

### 4. 搜尋與資料接地 (Grounding)

*   **原始方案**：Grounding with Bing Search
*   **替代方案思考**：
    *   **自定義搜尋**：如果需要從外部來源獲取實時資訊（例如替代商品、新聞），可以考慮使用 Google Custom Search API 或其他非微軟的搜尋 API。或者，如果資料是靜態的，可以將相關資訊預先載入到資料庫中。
    *   **RAG (Retrieval Augmented Generation)**：結合內部資料庫（HTS 資料）和外部搜尋結果，為 LLM 提供更豐富的上下文資訊，以生成更準確的答案。

## 專案架構初步設想

基於上述分析，我初步設想的專案架構如下：

*   **前端**：React (或 Vue.js)
*   **後端**：Python Flask/FastAPI (提供 RESTful API)
*   **AI 代理**：基於 LangChain/LlamaIndex 構建，使用開源 LLM 或非 Azure 的雲端 LLM 服務
*   **資料庫**：PostgreSQL (儲存 HTS 資料)
*   **資料導入**：Python 腳本

## 下一步計畫

在下一階段，我將根據這些初步設想，進一步細化系統架構，包括資料庫模型設計、API 接口定義、AI 代理的具體實現方式等。同時，我需要找到可靠的 HTS 資料來源，以便後續的資料導入工作。

