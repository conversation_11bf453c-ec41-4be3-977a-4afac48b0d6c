#!/usr/bin/env python3
"""
圖表建議服務 - 根據數據類型和查詢內容智能推薦最適合的圖表類型
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
import re

logger = logging.getLogger(__name__)

class ChartRecommendationService:
    """圖表建議服務"""
    
    def __init__(self):
        """初始化圖表建議服務"""
        self.chart_rules = {
            # 時間序列數據
            'time_series': {
                'charts': ['line', 'area', 'bar'],
                'primary': 'line',
                'keywords': ['趨勢', '時間', '月份', '年份', '季度', '變化', 'trend', 'time', 'monthly', 'yearly'],
                'description': '適合展示數據隨時間的變化趨勢'
            },
            
            # 比較數據
            'comparison': {
                'charts': ['bar', 'column', 'radar'],
                'primary': 'bar',
                'keywords': ['比較', '對比', '差異', '排名', 'compare', 'comparison', 'vs', 'versus'],
                'description': '適合比較不同類別或項目之間的差異'
            },
            
            # 組成/份額數據
            'composition': {
                'charts': ['pie', 'donut', 'treemap', 'stacked_bar'],
                'primary': 'pie',
                'keywords': ['份額', '比例', '組成', '構成', '佔比', 'share', 'proportion', 'composition', 'percentage'],
                'description': '適合展示各部分在整體中的比例關係'
            },
            
            # 分佈數據
            'distribution': {
                'charts': ['histogram', 'box', 'violin'],
                'primary': 'histogram',
                'keywords': ['分佈', '分布', '頻率', '統計', 'distribution', 'frequency', 'statistics'],
                'description': '適合展示數據的分佈特徵和統計特性'
            },
            
            # 關係數據
            'relationship': {
                'charts': ['scatter', 'bubble', 'heatmap'],
                'primary': 'scatter',
                'keywords': ['關係', '相關', '關聯', '散點', 'relationship', 'correlation', 'scatter'],
                'description': '適合展示變量之間的關係和相關性'
            },
            
            # 地理數據
            'geographic': {
                'charts': ['map', 'choropleth'],
                'primary': 'map',
                'keywords': ['地圖', '地理', '國家', '地區', '區域', 'map', 'geographic', 'country', 'region'],
                'description': '適合展示地理分佈和區域差異'
            },
            
            # 層級數據
            'hierarchical': {
                'charts': ['treemap', 'sunburst', 'sankey'],
                'primary': 'treemap',
                'keywords': ['層級', '分類', '類別', '結構', 'hierarchy', 'category', 'structure'],
                'description': '適合展示層級結構和分類關係'
            }
        }
        
        self.data_type_patterns = {
            'temporal': r'\b(年|月|日|季度|週|時間|date|time|year|month|day|quarter|week)\b',
            'monetary': r'\$([\d,]+\.?\d*)|(\d+\.?\d*)\s*(美元|USD|dollar)',
            'percentage': r'\d+\.?\d*%',
            'country': r'\b(中國|美國|日本|德國|韓國|加拿大|墨西哥|China|USA|Japan|Germany|Korea|Canada|Mexico)\b',
            'hts_code': r'\b\d{4}\.\d{2}\.\d{2}\b'
        }
        
        logger.info("📊 圖表建議服務已初始化")
    
    def recommend_charts(self, query: str, data: List[Dict[str, Any]] = None, 
                        context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        根據查詢和數據推薦最適合的圖表類型
        
        Args:
            query: 用戶查詢
            data: 數據樣本
            context: 查詢上下文
            
        Returns:
            圖表建議字典
        """
        try:
            # 分析查詢意圖
            query_analysis = self._analyze_query_intent(query)
            
            # 分析數據特徵
            data_analysis = self._analyze_data_characteristics(data) if data else {}
            
            # 生成圖表建議
            recommendations = self._generate_recommendations(query_analysis, data_analysis, context)
            
            return {
                'success': True,
                'query': query,
                'recommendations': recommendations,
                'query_analysis': query_analysis,
                'data_analysis': data_analysis,
                'timestamp': self._get_timestamp()
            }
            
        except Exception as e:
            logger.error(f"❌ 圖表建議錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'query': query
            }
    
    def get_chart_config_template(self, chart_type: str, data: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        獲取特定圖表類型的配置模板
        
        Args:
            chart_type: 圖表類型
            data: 數據樣本
            
        Returns:
            圖表配置模板
        """
        try:
            templates = {
                'line': self._get_line_chart_template(),
                'bar': self._get_bar_chart_template(),
                'pie': self._get_pie_chart_template(),
                'area': self._get_area_chart_template(),
                'scatter': self._get_scatter_chart_template(),
                'histogram': self._get_histogram_chart_template(),
                'heatmap': self._get_heatmap_chart_template(),
                'treemap': self._get_treemap_chart_template()
            }
            
            template = templates.get(chart_type, templates['bar'])
            
            # 如果有數據，自動填充部分配置
            if data:
                template = self._customize_template_with_data(template, data)
            
            return template
            
        except Exception as e:
            logger.error(f"❌ 獲取圖表模板錯誤: {e}")
            return self._get_default_chart_template()
    
    def _analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """分析查詢意圖"""
        query_lower = query.lower()
        
        analysis = {
            'intent_types': [],
            'data_types': [],
            'keywords_found': [],
            'suggested_visualizations': []
        }
        
        # 檢查意圖類型
        for intent_type, rules in self.chart_rules.items():
            for keyword in rules['keywords']:
                if keyword.lower() in query_lower:
                    analysis['intent_types'].append(intent_type)
                    analysis['keywords_found'].append(keyword)
                    break
        
        # 檢查數據類型
        for data_type, pattern in self.data_type_patterns.items():
            if re.search(pattern, query, re.IGNORECASE):
                analysis['data_types'].append(data_type)
        
        # 去重
        analysis['intent_types'] = list(set(analysis['intent_types']))
        analysis['data_types'] = list(set(analysis['data_types']))
        
        return analysis
    
    def _analyze_data_characteristics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析數據特徵"""
        if not data:
            return {}
        
        analysis = {
            'record_count': len(data),
            'field_count': len(data[0]) if data else 0,
            'field_types': {},
            'has_temporal_data': False,
            'has_numerical_data': False,
            'has_categorical_data': False,
            'data_distribution': 'unknown'
        }
        
        if not data:
            return analysis
        
        # 分析字段類型
        sample_record = data[0]
        for field, value in sample_record.items():
            if isinstance(value, (int, float)):
                analysis['field_types'][field] = 'numerical'
                analysis['has_numerical_data'] = True
            elif isinstance(value, str):
                if re.match(r'\d{4}-\d{2}-\d{2}', str(value)):
                    analysis['field_types'][field] = 'temporal'
                    analysis['has_temporal_data'] = True
                else:
                    analysis['field_types'][field] = 'categorical'
                    analysis['has_categorical_data'] = True
            else:
                analysis['field_types'][field] = 'other'
        
        # 判斷數據分佈
        if analysis['record_count'] > 50:
            analysis['data_distribution'] = 'large'
        elif analysis['record_count'] > 10:
            analysis['data_distribution'] = 'medium'
        else:
            analysis['data_distribution'] = 'small'
        
        return analysis
    
    def _generate_recommendations(self, query_analysis: Dict[str, Any], 
                                data_analysis: Dict[str, Any], 
                                context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """生成圖表建議"""
        recommendations = []
        
        # 基於查詢意圖的建議
        for intent_type in query_analysis.get('intent_types', []):
            if intent_type in self.chart_rules:
                rule = self.chart_rules[intent_type]
                
                recommendation = {
                    'chart_type': rule['primary'],
                    'confidence': 0.8,
                    'reason': f"查詢意圖匹配：{rule['description']}",
                    'alternatives': rule['charts'][1:3],  # 前2個替代選項
                    'category': intent_type
                }
                
                recommendations.append(recommendation)
        
        # 基於數據特徵的建議
        if data_analysis:
            data_recommendations = self._get_data_based_recommendations(data_analysis)
            recommendations.extend(data_recommendations)
        
        # 如果沒有明確的建議，提供默認建議
        if not recommendations:
            recommendations = self._get_default_recommendations(query_analysis, data_analysis)
        
        # 排序和去重
        recommendations = self._rank_and_deduplicate_recommendations(recommendations)
        
        return recommendations[:5]  # 返回前5個建議
    
    def _get_data_based_recommendations(self, data_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基於數據特徵生成建議"""
        recommendations = []
        
        # 時間序列數據
        if data_analysis.get('has_temporal_data', False):
            recommendations.append({
                'chart_type': 'line',
                'confidence': 0.9,
                'reason': '檢測到時間序列數據，線圖最適合展示趨勢',
                'alternatives': ['area', 'bar'],
                'category': 'time_series'
            })
        
        # 數值數據比較
        if data_analysis.get('has_numerical_data', False) and data_analysis.get('has_categorical_data', False):
            recommendations.append({
                'chart_type': 'bar',
                'confidence': 0.8,
                'reason': '數值和分類數據組合，柱狀圖適合比較',
                'alternatives': ['column', 'line'],
                'category': 'comparison'
            })
        
        # 大量數據的分佈
        if data_analysis.get('record_count', 0) > 50 and data_analysis.get('has_numerical_data', False):
            recommendations.append({
                'chart_type': 'histogram',
                'confidence': 0.7,
                'reason': '大量數值數據，直方圖適合展示分佈',
                'alternatives': ['box', 'scatter'],
                'category': 'distribution'
            })
        
        return recommendations
    
    def _get_default_recommendations(self, query_analysis: Dict[str, Any], 
                                   data_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """獲取默認建議"""
        return [
            {
                'chart_type': 'bar',
                'confidence': 0.6,
                'reason': '通用選擇，適合大多數比較場景',
                'alternatives': ['line', 'pie'],
                'category': 'general'
            },
            {
                'chart_type': 'line',
                'confidence': 0.5,
                'reason': '適合展示數據變化趨勢',
                'alternatives': ['area', 'bar'],
                'category': 'general'
            }
        ]
    
    def _rank_and_deduplicate_recommendations(self, recommendations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """排序和去重建議"""
        # 去重（基於圖表類型）
        seen_types = set()
        unique_recommendations = []
        
        for rec in recommendations:
            if rec['chart_type'] not in seen_types:
                unique_recommendations.append(rec)
                seen_types.add(rec['chart_type'])
        
        # 按信心度排序
        unique_recommendations.sort(key=lambda x: x['confidence'], reverse=True)
        
        return unique_recommendations
    
    def _get_line_chart_template(self) -> Dict[str, Any]:
        """線圖模板"""
        return {
            'type': 'line',
            'title': '趨勢分析',
            'description': '展示數據隨時間的變化趨勢',
            'best_for': ['時間序列', '趨勢分析', '連續數據'],
            'config': {
                'responsive': True,
                'plugins': {
                    'legend': {'position': 'top'},
                    'tooltip': {'mode': 'index', 'intersect': False}
                },
                'scales': {
                    'x': {'display': True, 'title': {'display': True, 'text': 'X 軸'}},
                    'y': {'display': True, 'title': {'display': True, 'text': 'Y 軸'}}
                },
                'elements': {
                    'line': {'tension': 0.4},
                    'point': {'radius': 4}
                }
            }
        }
    
    def _get_bar_chart_template(self) -> Dict[str, Any]:
        """柱狀圖模板"""
        return {
            'type': 'bar',
            'title': '數據比較',
            'description': '比較不同類別的數值大小',
            'best_for': ['分類比較', '排名展示', '數值對比'],
            'config': {
                'responsive': True,
                'plugins': {
                    'legend': {'position': 'top'}
                },
                'scales': {
                    'x': {'display': True, 'title': {'display': True, 'text': '類別'}},
                    'y': {'beginAtZero': True, 'title': {'display': True, 'text': '數值'}}
                }
            }
        }
    
    def _get_pie_chart_template(self) -> Dict[str, Any]:
        """圓餅圖模板"""
        return {
            'type': 'pie',
            'title': '組成分析',
            'description': '展示各部分在整體中的比例',
            'best_for': ['比例展示', '組成分析', '份額分佈'],
            'config': {
                'responsive': True,
                'plugins': {
                    'legend': {'position': 'right'},
                    'tooltip': {
                        'callbacks': {
                            'label': 'function(context) { return context.label + ": " + context.parsed + "%"; }'
                        }
                    }
                }
            }
        }
    
    def _get_area_chart_template(self) -> Dict[str, Any]:
        """區域圖模板"""
        return {
            'type': 'line',
            'title': '趨勢區域圖',
            'description': '強調數據量的變化趨勢',
            'best_for': ['累積趨勢', '數量變化', '填充區域'],
            'config': {
                'responsive': True,
                'plugins': {
                    'legend': {'position': 'top'},
                    'filler': {'propagate': False}
                },
                'elements': {
                    'line': {'tension': 0.4},
                    'point': {'radius': 0}
                },
                'interaction': {
                    'intersect': False
                }
            }
        }
    
    def _get_scatter_chart_template(self) -> Dict[str, Any]:
        """散點圖模板"""
        return {
            'type': 'scatter',
            'title': '關係分析',
            'description': '展示兩個變量之間的關係',
            'best_for': ['相關性分析', '關係展示', '分佈模式'],
            'config': {
                'responsive': True,
                'plugins': {
                    'legend': {'position': 'top'}
                },
                'scales': {
                    'x': {'type': 'linear', 'position': 'bottom', 'title': {'display': True, 'text': 'X 變量'}},
                    'y': {'title': {'display': True, 'text': 'Y 變量'}}
                }
            }
        }
    
    def _get_histogram_chart_template(self) -> Dict[str, Any]:
        """直方圖模板"""
        return {
            'type': 'bar',
            'title': '分佈分析',
            'description': '展示數據的頻率分佈',
            'best_for': ['頻率分佈', '統計分析', '數據分佈'],
            'config': {
                'responsive': True,
                'plugins': {
                    'legend': {'display': False}
                },
                'scales': {
                    'x': {'title': {'display': True, 'text': '數值區間'}},
                    'y': {'beginAtZero': True, 'title': {'display': True, 'text': '頻率'}}
                }
            }
        }
    
    def _get_heatmap_chart_template(self) -> Dict[str, Any]:
        """熱力圖模板"""
        return {
            'type': 'matrix',
            'title': '熱力圖分析',
            'description': '展示二維數據的密度分佈',
            'best_for': ['密度分析', '相關性矩陣', '二維分佈'],
            'config': {
                'responsive': True,
                'plugins': {
                    'legend': {'display': False}
                }
            }
        }
    
    def _get_treemap_chart_template(self) -> Dict[str, Any]:
        """樹狀圖模板"""
        return {
            'type': 'treemap',
            'title': '層級結構圖',
            'description': '展示層級數據的相對大小',
            'best_for': ['層級展示', '比例結構', '分類組成'],
            'config': {
                'responsive': True,
                'plugins': {
                    'legend': {'display': False}
                }
            }
        }
    
    def _get_default_chart_template(self) -> Dict[str, Any]:
        """默認圖表模板"""
        return self._get_bar_chart_template()
    
    def _customize_template_with_data(self, template: Dict[str, Any], 
                                    data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """根據數據自定義模板"""
        if not data:
            return template
        
        # 根據數據字段自動設置軸標題
        sample = data[0]
        fields = list(sample.keys())
        
        if len(fields) >= 2:
            if 'config' in template and 'scales' in template['config']:
                if 'x' in template['config']['scales']:
                    template['config']['scales']['x']['title']['text'] = fields[0]
                if 'y' in template['config']['scales']:
                    template['config']['scales']['y']['title']['text'] = fields[1]
        
        return template
    
    def _get_timestamp(self) -> str:
        """獲取時間戳"""
        from datetime import datetime
        return datetime.now().isoformat()

# 全局圖表建議服務實例
chart_recommendation_service = ChartRecommendationService()
