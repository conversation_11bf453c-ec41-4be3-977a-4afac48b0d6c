function iy(c,s){for(var d=0;d<s.length;d++){const o=s[d];if(typeof o!="string"&&!Array.isArray(o)){for(const y in o)if(y!=="default"&&!(y in c)){const x=Object.getOwnPropertyDescriptor(o,y);x&&Object.defineProperty(c,y,x.get?x:{enumerable:!0,get:()=>o[y]})}}}return Object.freeze(Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}))}(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const y of document.querySelectorAll('link[rel="modulepreload"]'))o(y);new MutationObserver(y=>{for(const x of y)if(x.type==="childList")for(const _ of x.addedNodes)_.tagName==="LINK"&&_.rel==="modulepreload"&&o(_)}).observe(document,{childList:!0,subtree:!0});function d(y){const x={};return y.integrity&&(x.integrity=y.integrity),y.referrerPolicy&&(x.referrerPolicy=y.referrerPolicy),y.crossOrigin==="use-credentials"?x.credentials="include":y.crossOrigin==="anonymous"?x.credentials="omit":x.credentials="same-origin",x}function o(y){if(y.ep)return;y.ep=!0;const x=d(y);fetch(y.href,x)}})();function cy(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c}var Ef={exports:{}},Gn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jd;function fy(){if(jd)return Gn;jd=1;var c=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function d(o,y,x){var _=null;if(x!==void 0&&(_=""+x),y.key!==void 0&&(_=""+y.key),"key"in y){x={};for(var U in y)U!=="key"&&(x[U]=y[U])}else x=y;return y=x.ref,{$$typeof:c,type:o,key:_,ref:y!==void 0?y:null,props:x}}return Gn.Fragment=s,Gn.jsx=d,Gn.jsxs=d,Gn}var Hd;function oy(){return Hd||(Hd=1,Ef.exports=fy()),Ef.exports}var R=oy(),zf={exports:{}},lt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qd;function sy(){if(qd)return lt;qd=1;var c=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),d=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),x=Symbol.for("react.consumer"),_=Symbol.for("react.context"),U=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),j=Symbol.iterator;function B(m){return m===null||typeof m!="object"?null:(m=j&&m[j]||m["@@iterator"],typeof m=="function"?m:null)}var tt={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},P=Object.assign,F={};function L(m,D,G){this.props=m,this.context=D,this.refs=F,this.updater=G||tt}L.prototype.isReactComponent={},L.prototype.setState=function(m,D){if(typeof m!="object"&&typeof m!="function"&&m!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,m,D,"setState")},L.prototype.forceUpdate=function(m){this.updater.enqueueForceUpdate(this,m,"forceUpdate")};function st(){}st.prototype=L.prototype;function Tt(m,D,G){this.props=m,this.context=D,this.refs=F,this.updater=G||tt}var et=Tt.prototype=new st;et.constructor=Tt,P(et,L.prototype),et.isPureReactComponent=!0;var ot=Array.isArray,I={H:null,A:null,T:null,S:null,V:null},bt=Object.prototype.hasOwnProperty;function Mt(m,D,G,H,X,it){return G=it.ref,{$$typeof:c,type:m,key:D,ref:G!==void 0?G:null,props:it}}function Y(m,D){return Mt(m.type,D,void 0,void 0,void 0,m.props)}function Dt(m){return typeof m=="object"&&m!==null&&m.$$typeof===c}function ve(m){var D={"=":"=0",":":"=2"};return"$"+m.replace(/[=:]/g,function(G){return D[G]})}var Jt=/\/+/g;function Rt(m,D){return typeof m=="object"&&m!==null&&m.key!=null?ve(""+m.key):D.toString(36)}function he(){}function ye(m){switch(m.status){case"fulfilled":return m.value;case"rejected":throw m.reason;default:switch(typeof m.status=="string"?m.then(he,he):(m.status="pending",m.then(function(D){m.status==="pending"&&(m.status="fulfilled",m.value=D)},function(D){m.status==="pending"&&(m.status="rejected",m.reason=D)})),m.status){case"fulfilled":return m.value;case"rejected":throw m.reason}}throw m}function _t(m,D,G,H,X){var it=typeof m;(it==="undefined"||it==="boolean")&&(m=null);var W=!1;if(m===null)W=!0;else switch(it){case"bigint":case"string":case"number":W=!0;break;case"object":switch(m.$$typeof){case c:case s:W=!0;break;case A:return W=m._init,_t(W(m._payload),D,G,H,X)}}if(W)return X=X(m),W=H===""?"."+Rt(m,0):H,ot(X)?(G="",W!=null&&(G=W.replace(Jt,"$&/")+"/"),_t(X,D,G,"",function(ne){return ne})):X!=null&&(Dt(X)&&(X=Y(X,G+(X.key==null||m&&m.key===X.key?"":(""+X.key).replace(Jt,"$&/")+"/")+W)),D.push(X)),1;W=0;var dt=H===""?".":H+":";if(ot(m))for(var At=0;At<m.length;At++)H=m[At],it=dt+Rt(H,At),W+=_t(H,D,G,it,X);else if(At=B(m),typeof At=="function")for(m=At.call(m),At=0;!(H=m.next()).done;)H=H.value,it=dt+Rt(H,At++),W+=_t(H,D,G,it,X);else if(it==="object"){if(typeof m.then=="function")return _t(ye(m),D,G,H,X);throw D=String(m),Error("Objects are not valid as a React child (found: "+(D==="[object Object]"?"object with keys {"+Object.keys(m).join(", ")+"}":D)+"). If you meant to render a collection of children, use an array instead.")}return W}function E(m,D,G){if(m==null)return m;var H=[],X=0;return _t(m,H,"","",function(it){return D.call(G,it,X++)}),H}function q(m){if(m._status===-1){var D=m._result;D=D(),D.then(function(G){(m._status===0||m._status===-1)&&(m._status=1,m._result=G)},function(G){(m._status===0||m._status===-1)&&(m._status=2,m._result=G)}),m._status===-1&&(m._status=0,m._result=D)}if(m._status===1)return m._result.default;throw m._result}var C=typeof reportError=="function"?reportError:function(m){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var D=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof m=="object"&&m!==null&&typeof m.message=="string"?String(m.message):String(m),error:m});if(!window.dispatchEvent(D))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",m);return}console.error(m)};function ht(){}return lt.Children={map:E,forEach:function(m,D,G){E(m,function(){D.apply(this,arguments)},G)},count:function(m){var D=0;return E(m,function(){D++}),D},toArray:function(m){return E(m,function(D){return D})||[]},only:function(m){if(!Dt(m))throw Error("React.Children.only expected to receive a single React element child.");return m}},lt.Component=L,lt.Fragment=d,lt.Profiler=y,lt.PureComponent=Tt,lt.StrictMode=o,lt.Suspense=z,lt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=I,lt.__COMPILER_RUNTIME={__proto__:null,c:function(m){return I.H.useMemoCache(m)}},lt.cache=function(m){return function(){return m.apply(null,arguments)}},lt.cloneElement=function(m,D,G){if(m==null)throw Error("The argument must be a React element, but you passed "+m+".");var H=P({},m.props),X=m.key,it=void 0;if(D!=null)for(W in D.ref!==void 0&&(it=void 0),D.key!==void 0&&(X=""+D.key),D)!bt.call(D,W)||W==="key"||W==="__self"||W==="__source"||W==="ref"&&D.ref===void 0||(H[W]=D[W]);var W=arguments.length-2;if(W===1)H.children=G;else if(1<W){for(var dt=Array(W),At=0;At<W;At++)dt[At]=arguments[At+2];H.children=dt}return Mt(m.type,X,void 0,void 0,it,H)},lt.createContext=function(m){return m={$$typeof:_,_currentValue:m,_currentValue2:m,_threadCount:0,Provider:null,Consumer:null},m.Provider=m,m.Consumer={$$typeof:x,_context:m},m},lt.createElement=function(m,D,G){var H,X={},it=null;if(D!=null)for(H in D.key!==void 0&&(it=""+D.key),D)bt.call(D,H)&&H!=="key"&&H!=="__self"&&H!=="__source"&&(X[H]=D[H]);var W=arguments.length-2;if(W===1)X.children=G;else if(1<W){for(var dt=Array(W),At=0;At<W;At++)dt[At]=arguments[At+2];X.children=dt}if(m&&m.defaultProps)for(H in W=m.defaultProps,W)X[H]===void 0&&(X[H]=W[H]);return Mt(m,it,void 0,void 0,null,X)},lt.createRef=function(){return{current:null}},lt.forwardRef=function(m){return{$$typeof:U,render:m}},lt.isValidElement=Dt,lt.lazy=function(m){return{$$typeof:A,_payload:{_status:-1,_result:m},_init:q}},lt.memo=function(m,D){return{$$typeof:g,type:m,compare:D===void 0?null:D}},lt.startTransition=function(m){var D=I.T,G={};I.T=G;try{var H=m(),X=I.S;X!==null&&X(G,H),typeof H=="object"&&H!==null&&typeof H.then=="function"&&H.then(ht,C)}catch(it){C(it)}finally{I.T=D}},lt.unstable_useCacheRefresh=function(){return I.H.useCacheRefresh()},lt.use=function(m){return I.H.use(m)},lt.useActionState=function(m,D,G){return I.H.useActionState(m,D,G)},lt.useCallback=function(m,D){return I.H.useCallback(m,D)},lt.useContext=function(m){return I.H.useContext(m)},lt.useDebugValue=function(){},lt.useDeferredValue=function(m,D){return I.H.useDeferredValue(m,D)},lt.useEffect=function(m,D,G){var H=I.H;if(typeof G=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return H.useEffect(m,D)},lt.useId=function(){return I.H.useId()},lt.useImperativeHandle=function(m,D,G){return I.H.useImperativeHandle(m,D,G)},lt.useInsertionEffect=function(m,D){return I.H.useInsertionEffect(m,D)},lt.useLayoutEffect=function(m,D){return I.H.useLayoutEffect(m,D)},lt.useMemo=function(m,D){return I.H.useMemo(m,D)},lt.useOptimistic=function(m,D){return I.H.useOptimistic(m,D)},lt.useReducer=function(m,D,G){return I.H.useReducer(m,D,G)},lt.useRef=function(m){return I.H.useRef(m)},lt.useState=function(m){return I.H.useState(m)},lt.useSyncExternalStore=function(m,D,G){return I.H.useSyncExternalStore(m,D,G)},lt.useTransition=function(){return I.H.useTransition()},lt.version="19.1.0",lt}var wd;function wf(){return wd||(wd=1,zf.exports=sy()),zf.exports}var w=wf();const Ml=cy(w),am=iy({__proto__:null,default:Ml},[w]);var _f={exports:{}},Bn={},Mf={exports:{}},Of={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gd;function ry(){return Gd||(Gd=1,function(c){function s(E,q){var C=E.length;E.push(q);t:for(;0<C;){var ht=C-1>>>1,m=E[ht];if(0<y(m,q))E[ht]=q,E[C]=m,C=ht;else break t}}function d(E){return E.length===0?null:E[0]}function o(E){if(E.length===0)return null;var q=E[0],C=E.pop();if(C!==q){E[0]=C;t:for(var ht=0,m=E.length,D=m>>>1;ht<D;){var G=2*(ht+1)-1,H=E[G],X=G+1,it=E[X];if(0>y(H,C))X<m&&0>y(it,H)?(E[ht]=it,E[X]=C,ht=X):(E[ht]=H,E[G]=C,ht=G);else if(X<m&&0>y(it,C))E[ht]=it,E[X]=C,ht=X;else break t}}return q}function y(E,q){var C=E.sortIndex-q.sortIndex;return C!==0?C:E.id-q.id}if(c.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var x=performance;c.unstable_now=function(){return x.now()}}else{var _=Date,U=_.now();c.unstable_now=function(){return _.now()-U}}var z=[],g=[],A=1,j=null,B=3,tt=!1,P=!1,F=!1,L=!1,st=typeof setTimeout=="function"?setTimeout:null,Tt=typeof clearTimeout=="function"?clearTimeout:null,et=typeof setImmediate<"u"?setImmediate:null;function ot(E){for(var q=d(g);q!==null;){if(q.callback===null)o(g);else if(q.startTime<=E)o(g),q.sortIndex=q.expirationTime,s(z,q);else break;q=d(g)}}function I(E){if(F=!1,ot(E),!P)if(d(z)!==null)P=!0,bt||(bt=!0,Rt());else{var q=d(g);q!==null&&_t(I,q.startTime-E)}}var bt=!1,Mt=-1,Y=5,Dt=-1;function ve(){return L?!0:!(c.unstable_now()-Dt<Y)}function Jt(){if(L=!1,bt){var E=c.unstable_now();Dt=E;var q=!0;try{t:{P=!1,F&&(F=!1,Tt(Mt),Mt=-1),tt=!0;var C=B;try{e:{for(ot(E),j=d(z);j!==null&&!(j.expirationTime>E&&ve());){var ht=j.callback;if(typeof ht=="function"){j.callback=null,B=j.priorityLevel;var m=ht(j.expirationTime<=E);if(E=c.unstable_now(),typeof m=="function"){j.callback=m,ot(E),q=!0;break e}j===d(z)&&o(z),ot(E)}else o(z);j=d(z)}if(j!==null)q=!0;else{var D=d(g);D!==null&&_t(I,D.startTime-E),q=!1}}break t}finally{j=null,B=C,tt=!1}q=void 0}}finally{q?Rt():bt=!1}}}var Rt;if(typeof et=="function")Rt=function(){et(Jt)};else if(typeof MessageChannel<"u"){var he=new MessageChannel,ye=he.port2;he.port1.onmessage=Jt,Rt=function(){ye.postMessage(null)}}else Rt=function(){st(Jt,0)};function _t(E,q){Mt=st(function(){E(c.unstable_now())},q)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(E){E.callback=null},c.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Y=0<E?Math.floor(1e3/E):5},c.unstable_getCurrentPriorityLevel=function(){return B},c.unstable_next=function(E){switch(B){case 1:case 2:case 3:var q=3;break;default:q=B}var C=B;B=q;try{return E()}finally{B=C}},c.unstable_requestPaint=function(){L=!0},c.unstable_runWithPriority=function(E,q){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var C=B;B=E;try{return q()}finally{B=C}},c.unstable_scheduleCallback=function(E,q,C){var ht=c.unstable_now();switch(typeof C=="object"&&C!==null?(C=C.delay,C=typeof C=="number"&&0<C?ht+C:ht):C=ht,E){case 1:var m=-1;break;case 2:m=250;break;case 5:m=1073741823;break;case 4:m=1e4;break;default:m=5e3}return m=C+m,E={id:A++,callback:q,priorityLevel:E,startTime:C,expirationTime:m,sortIndex:-1},C>ht?(E.sortIndex=C,s(g,E),d(z)===null&&E===d(g)&&(F?(Tt(Mt),Mt=-1):F=!0,_t(I,C-ht))):(E.sortIndex=m,s(z,E),P||tt||(P=!0,bt||(bt=!0,Rt()))),E},c.unstable_shouldYield=ve,c.unstable_wrapCallback=function(E){var q=B;return function(){var C=B;B=q;try{return E.apply(this,arguments)}finally{B=C}}}}(Of)),Of}var Bd;function dy(){return Bd||(Bd=1,Mf.exports=ry()),Mf.exports}var Rf={exports:{}},Ft={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yd;function my(){if(Yd)return Ft;Yd=1;var c=wf();function s(z){var g="https://react.dev/errors/"+z;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var A=2;A<arguments.length;A++)g+="&args[]="+encodeURIComponent(arguments[A])}return"Minified React error #"+z+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(){}var o={d:{f:d,r:function(){throw Error(s(522))},D:d,C:d,L:d,m:d,X:d,S:d,M:d},p:0,findDOMNode:null},y=Symbol.for("react.portal");function x(z,g,A){var j=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:y,key:j==null?null:""+j,children:z,containerInfo:g,implementation:A}}var _=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function U(z,g){if(z==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return Ft.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,Ft.createPortal=function(z,g){var A=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(s(299));return x(z,g,null,A)},Ft.flushSync=function(z){var g=_.T,A=o.p;try{if(_.T=null,o.p=2,z)return z()}finally{_.T=g,o.p=A,o.d.f()}},Ft.preconnect=function(z,g){typeof z=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,o.d.C(z,g))},Ft.prefetchDNS=function(z){typeof z=="string"&&o.d.D(z)},Ft.preinit=function(z,g){if(typeof z=="string"&&g&&typeof g.as=="string"){var A=g.as,j=U(A,g.crossOrigin),B=typeof g.integrity=="string"?g.integrity:void 0,tt=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;A==="style"?o.d.S(z,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:j,integrity:B,fetchPriority:tt}):A==="script"&&o.d.X(z,{crossOrigin:j,integrity:B,fetchPriority:tt,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},Ft.preinitModule=function(z,g){if(typeof z=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var A=U(g.as,g.crossOrigin);o.d.M(z,{crossOrigin:A,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&o.d.M(z)},Ft.preload=function(z,g){if(typeof z=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var A=g.as,j=U(A,g.crossOrigin);o.d.L(z,A,{crossOrigin:j,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},Ft.preloadModule=function(z,g){if(typeof z=="string")if(g){var A=U(g.as,g.crossOrigin);o.d.m(z,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:A,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else o.d.m(z)},Ft.requestFormReset=function(z){o.d.r(z)},Ft.unstable_batchedUpdates=function(z,g){return z(g)},Ft.useFormState=function(z,g,A){return _.H.useFormState(z,g,A)},Ft.useFormStatus=function(){return _.H.useHostTransitionStatus()},Ft.version="19.1.0",Ft}var Xd;function nm(){if(Xd)return Rf.exports;Xd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),Rf.exports=my(),Rf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qd;function vy(){if(Qd)return Bn;Qd=1;var c=dy(),s=wf(),d=nm();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function y(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function x(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function _(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function U(t){if(x(t)!==t)throw Error(o(188))}function z(t){var e=t.alternate;if(!e){if(e=x(t),e===null)throw Error(o(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return U(n),t;if(u===a)return U(n),e;u=u.sibling}throw Error(o(188))}if(l.return!==a.return)l=n,a=u;else{for(var i=!1,f=n.child;f;){if(f===l){i=!0,l=n,a=u;break}if(f===a){i=!0,a=n,l=u;break}f=f.sibling}if(!i){for(f=u.child;f;){if(f===l){i=!0,l=u,a=n;break}if(f===a){i=!0,a=u,l=n;break}f=f.sibling}if(!i)throw Error(o(189))}}if(l.alternate!==a)throw Error(o(190))}if(l.tag!==3)throw Error(o(188));return l.stateNode.current===l?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var A=Object.assign,j=Symbol.for("react.element"),B=Symbol.for("react.transitional.element"),tt=Symbol.for("react.portal"),P=Symbol.for("react.fragment"),F=Symbol.for("react.strict_mode"),L=Symbol.for("react.profiler"),st=Symbol.for("react.provider"),Tt=Symbol.for("react.consumer"),et=Symbol.for("react.context"),ot=Symbol.for("react.forward_ref"),I=Symbol.for("react.suspense"),bt=Symbol.for("react.suspense_list"),Mt=Symbol.for("react.memo"),Y=Symbol.for("react.lazy"),Dt=Symbol.for("react.activity"),ve=Symbol.for("react.memo_cache_sentinel"),Jt=Symbol.iterator;function Rt(t){return t===null||typeof t!="object"?null:(t=Jt&&t[Jt]||t["@@iterator"],typeof t=="function"?t:null)}var he=Symbol.for("react.client.reference");function ye(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===he?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case P:return"Fragment";case L:return"Profiler";case F:return"StrictMode";case I:return"Suspense";case bt:return"SuspenseList";case Dt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case tt:return"Portal";case et:return(t.displayName||"Context")+".Provider";case Tt:return(t._context.displayName||"Context")+".Consumer";case ot:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Mt:return e=t.displayName||null,e!==null?e:ye(t.type)||"Memo";case Y:e=t._payload,t=t._init;try{return ye(t(e))}catch{}}return null}var _t=Array.isArray,E=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=d.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,C={pending:!1,data:null,method:null,action:null},ht=[],m=-1;function D(t){return{current:t}}function G(t){0>m||(t.current=ht[m],ht[m]=null,m--)}function H(t,e){m++,ht[m]=t.current,t.current=e}var X=D(null),it=D(null),W=D(null),dt=D(null);function At(t,e){switch(H(W,e),H(it,t),H(X,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?fd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=fd(e),t=od(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}G(X),H(X,t)}function ne(){G(X),G(it),G(W)}function Pe(t){t.memoizedState!==null&&H(dt,t);var e=X.current,l=od(e,t.type);e!==l&&(H(it,t),H(X,l))}function tl(t){it.current===t&&(G(X),G(it)),dt.current===t&&(G(dt),Cn._currentValue=C)}var el=Object.prototype.hasOwnProperty,si=c.unstable_scheduleCallback,ri=c.unstable_cancelCallback,Gm=c.unstable_shouldYield,Bm=c.unstable_requestPaint,Ne=c.unstable_now,Ym=c.unstable_getCurrentPriorityLevel,Xf=c.unstable_ImmediatePriority,Qf=c.unstable_UserBlockingPriority,Vn=c.unstable_NormalPriority,Xm=c.unstable_LowPriority,Vf=c.unstable_IdlePriority,Qm=c.log,Vm=c.unstable_setDisableYieldValue,Ya=null,ue=null;function ll(t){if(typeof Qm=="function"&&Vm(t),ue&&typeof ue.setStrictMode=="function")try{ue.setStrictMode(Ya,t)}catch{}}var ie=Math.clz32?Math.clz32:Km,Zm=Math.log,Lm=Math.LN2;function Km(t){return t>>>=0,t===0?32:31-(Zm(t)/Lm|0)|0}var Zn=256,Ln=4194304;function Ol(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Kn(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,u=t.suspendedLanes,i=t.pingedLanes;t=t.warmLanes;var f=a&134217727;return f!==0?(a=f&~u,a!==0?n=Ol(a):(i&=f,i!==0?n=Ol(i):l||(l=f&~t,l!==0&&(n=Ol(l))))):(f=a&~u,f!==0?n=Ol(f):i!==0?n=Ol(i):l||(l=a&~t,l!==0&&(n=Ol(l)))),n===0?0:e!==0&&e!==n&&(e&u)===0&&(u=n&-n,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:n}function Xa(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function km(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Zf(){var t=Zn;return Zn<<=1,(Zn&4194048)===0&&(Zn=256),t}function Lf(){var t=Ln;return Ln<<=1,(Ln&62914560)===0&&(Ln=4194304),t}function di(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function Qa(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Jm(t,e,l,a,n,u){var i=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var f=t.entanglements,r=t.expirationTimes,p=t.hiddenUpdates;for(l=i&~l;0<l;){var M=31-ie(l),N=1<<M;f[M]=0,r[M]=-1;var S=p[M];if(S!==null)for(p[M]=null,M=0;M<S.length;M++){var T=S[M];T!==null&&(T.lane&=-536870913)}l&=~N}a!==0&&Kf(t,a,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(i&~e))}function Kf(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-ie(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function kf(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-ie(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function mi(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function vi(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Jf(){var t=q.p;return t!==0?t:(t=window.event,t===void 0?32:Od(t.type))}function $m(t,e){var l=q.p;try{return q.p=t,e()}finally{q.p=l}}var al=Math.random().toString(36).slice(2),$t="__reactFiber$"+al,Pt="__reactProps$"+al,$l="__reactContainer$"+al,hi="__reactEvents$"+al,Wm="__reactListeners$"+al,Fm="__reactHandles$"+al,$f="__reactResources$"+al,Va="__reactMarker$"+al;function yi(t){delete t[$t],delete t[Pt],delete t[hi],delete t[Wm],delete t[Fm]}function Wl(t){var e=t[$t];if(e)return e;for(var l=t.parentNode;l;){if(e=l[$l]||l[$t]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=md(t);t!==null;){if(l=t[$t])return l;t=md(t)}return e}t=l,l=t.parentNode}return null}function Fl(t){if(t=t[$t]||t[$l]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Za(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function Il(t){var e=t[$f];return e||(e=t[$f]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Xt(t){t[Va]=!0}var Wf=new Set,Ff={};function Rl(t,e){Pl(t,e),Pl(t+"Capture",e)}function Pl(t,e){for(Ff[t]=e,t=0;t<e.length;t++)Wf.add(e[t])}var Im=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),If={},Pf={};function Pm(t){return el.call(Pf,t)?!0:el.call(If,t)?!1:Im.test(t)?Pf[t]=!0:(If[t]=!0,!1)}function kn(t,e,l){if(Pm(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Jn(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function qe(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var gi,to;function ta(t){if(gi===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);gi=e&&e[1]||"",to=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+gi+t+to}var bi=!1;function pi(t,e){if(!t||bi)return"";bi=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(N,[])}catch(T){var S=T}Reflect.construct(t,[],N)}else{try{N.call()}catch(T){S=T}t.call(N.prototype)}}else{try{throw Error()}catch(T){S=T}(N=t())&&typeof N.catch=="function"&&N.catch(function(){})}}catch(T){if(T&&S&&typeof T.stack=="string")return[T.stack,S.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),i=u[0],f=u[1];if(i&&f){var r=i.split(`
`),p=f.split(`
`);for(n=a=0;a<r.length&&!r[a].includes("DetermineComponentFrameRoot");)a++;for(;n<p.length&&!p[n].includes("DetermineComponentFrameRoot");)n++;if(a===r.length||n===p.length)for(a=r.length-1,n=p.length-1;1<=a&&0<=n&&r[a]!==p[n];)n--;for(;1<=a&&0<=n;a--,n--)if(r[a]!==p[n]){if(a!==1||n!==1)do if(a--,n--,0>n||r[a]!==p[n]){var M=`
`+r[a].replace(" at new "," at ");return t.displayName&&M.includes("<anonymous>")&&(M=M.replace("<anonymous>",t.displayName)),M}while(1<=a&&0<=n);break}}}finally{bi=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?ta(l):""}function tv(t){switch(t.tag){case 26:case 27:case 5:return ta(t.type);case 16:return ta("Lazy");case 13:return ta("Suspense");case 19:return ta("SuspenseList");case 0:case 15:return pi(t.type,!1);case 11:return pi(t.type.render,!1);case 1:return pi(t.type,!0);case 31:return ta("Activity");default:return""}}function eo(t){try{var e="";do e+=tv(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function ge(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function lo(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function ev(t){var e=lo(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(i){a=""+i,u.call(this,i)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function $n(t){t._valueTracker||(t._valueTracker=ev(t))}function ao(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=lo(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Wn(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var lv=/[\n"\\]/g;function be(t){return t.replace(lv,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Si(t,e,l,a,n,u,i,f){t.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?t.type=i:t.removeAttribute("type"),e!=null?i==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+ge(e)):t.value!==""+ge(e)&&(t.value=""+ge(e)):i!=="submit"&&i!=="reset"||t.removeAttribute("value"),e!=null?xi(t,i,ge(e)):l!=null?xi(t,i,ge(l)):a!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.name=""+ge(f):t.removeAttribute("name")}function no(t,e,l,a,n,u,i,f){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+ge(l):"",e=e!=null?""+ge(e):l,f||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=f?t.checked:!!a,t.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.name=i)}function xi(t,e,l){e==="number"&&Wn(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function ea(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+ge(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function uo(t,e,l){if(e!=null&&(e=""+ge(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+ge(l):""}function io(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(o(92));if(_t(a)){if(1<a.length)throw Error(o(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=ge(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function la(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var av=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function co(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||av.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function fo(t,e,l){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&co(t,n,a)}else for(var u in e)e.hasOwnProperty(u)&&co(t,u,e[u])}function Ti(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var nv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),uv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Fn(t){return uv.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ai=null;function Ei(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var aa=null,na=null;function oo(t){var e=Fl(t);if(e&&(t=e.stateNode)){var l=t[Pt]||null;t:switch(t=e.stateNode,e.type){case"input":if(Si(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+be(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[Pt]||null;if(!n)throw Error(o(90));Si(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&ao(a)}break t;case"textarea":uo(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&ea(t,!!l.multiple,e,!1)}}}var zi=!1;function so(t,e,l){if(zi)return t(e,l);zi=!0;try{var a=t(e);return a}finally{if(zi=!1,(aa!==null||na!==null)&&(qu(),aa&&(e=aa,t=na,na=aa=null,oo(e),t)))for(e=0;e<t.length;e++)oo(t[e])}}function La(t,e){var l=t.stateNode;if(l===null)return null;var a=l[Pt]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(o(231,e,typeof l));return l}var we=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),_i=!1;if(we)try{var Ka={};Object.defineProperty(Ka,"passive",{get:function(){_i=!0}}),window.addEventListener("test",Ka,Ka),window.removeEventListener("test",Ka,Ka)}catch{_i=!1}var nl=null,Mi=null,In=null;function ro(){if(In)return In;var t,e=Mi,l=e.length,a,n="value"in nl?nl.value:nl.textContent,u=n.length;for(t=0;t<l&&e[t]===n[t];t++);var i=l-t;for(a=1;a<=i&&e[l-a]===n[u-a];a++);return In=n.slice(t,1<a?1-a:void 0)}function Pn(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function tu(){return!0}function mo(){return!1}function te(t){function e(l,a,n,u,i){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=i,this.currentTarget=null;for(var f in t)t.hasOwnProperty(f)&&(l=t[f],this[f]=l?l(u):u[f]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?tu:mo,this.isPropagationStopped=mo,this}return A(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=tu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=tu)},persist:function(){},isPersistent:tu}),e}var Nl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},eu=te(Nl),ka=A({},Nl,{view:0,detail:0}),iv=te(ka),Oi,Ri,Ja,lu=A({},ka,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Di,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ja&&(Ja&&t.type==="mousemove"?(Oi=t.screenX-Ja.screenX,Ri=t.screenY-Ja.screenY):Ri=Oi=0,Ja=t),Oi)},movementY:function(t){return"movementY"in t?t.movementY:Ri}}),vo=te(lu),cv=A({},lu,{dataTransfer:0}),fv=te(cv),ov=A({},ka,{relatedTarget:0}),Ni=te(ov),sv=A({},Nl,{animationName:0,elapsedTime:0,pseudoElement:0}),rv=te(sv),dv=A({},Nl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),mv=te(dv),vv=A({},Nl,{data:0}),ho=te(vv),hv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},yv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},gv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function bv(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=gv[t])?!!e[t]:!1}function Di(){return bv}var pv=A({},ka,{key:function(t){if(t.key){var e=hv[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Pn(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?yv[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Di,charCode:function(t){return t.type==="keypress"?Pn(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Pn(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Sv=te(pv),xv=A({},lu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),yo=te(xv),Tv=A({},ka,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Di}),Av=te(Tv),Ev=A({},Nl,{propertyName:0,elapsedTime:0,pseudoElement:0}),zv=te(Ev),_v=A({},lu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Mv=te(_v),Ov=A({},Nl,{newState:0,oldState:0}),Rv=te(Ov),Nv=[9,13,27,32],Ui=we&&"CompositionEvent"in window,$a=null;we&&"documentMode"in document&&($a=document.documentMode);var Dv=we&&"TextEvent"in window&&!$a,go=we&&(!Ui||$a&&8<$a&&11>=$a),bo=" ",po=!1;function So(t,e){switch(t){case"keyup":return Nv.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function xo(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ua=!1;function Uv(t,e){switch(t){case"compositionend":return xo(e);case"keypress":return e.which!==32?null:(po=!0,bo);case"textInput":return t=e.data,t===bo&&po?null:t;default:return null}}function Cv(t,e){if(ua)return t==="compositionend"||!Ui&&So(t,e)?(t=ro(),In=Mi=nl=null,ua=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return go&&e.locale!=="ko"?null:e.data;default:return null}}var jv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function To(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!jv[t.type]:e==="textarea"}function Ao(t,e,l,a){aa?na?na.push(a):na=[a]:aa=a,e=Qu(e,"onChange"),0<e.length&&(l=new eu("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var Wa=null,Fa=null;function Hv(t){ad(t,0)}function au(t){var e=Za(t);if(ao(e))return t}function Eo(t,e){if(t==="change")return e}var zo=!1;if(we){var Ci;if(we){var ji="oninput"in document;if(!ji){var _o=document.createElement("div");_o.setAttribute("oninput","return;"),ji=typeof _o.oninput=="function"}Ci=ji}else Ci=!1;zo=Ci&&(!document.documentMode||9<document.documentMode)}function Mo(){Wa&&(Wa.detachEvent("onpropertychange",Oo),Fa=Wa=null)}function Oo(t){if(t.propertyName==="value"&&au(Fa)){var e=[];Ao(e,Fa,t,Ei(t)),so(Hv,e)}}function qv(t,e,l){t==="focusin"?(Mo(),Wa=e,Fa=l,Wa.attachEvent("onpropertychange",Oo)):t==="focusout"&&Mo()}function wv(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return au(Fa)}function Gv(t,e){if(t==="click")return au(e)}function Bv(t,e){if(t==="input"||t==="change")return au(e)}function Yv(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ce=typeof Object.is=="function"?Object.is:Yv;function Ia(t,e){if(ce(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!el.call(e,n)||!ce(t[n],e[n]))return!1}return!0}function Ro(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function No(t,e){var l=Ro(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Ro(l)}}function Do(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Do(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Uo(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Wn(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Wn(t.document)}return e}function Hi(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Xv=we&&"documentMode"in document&&11>=document.documentMode,ia=null,qi=null,Pa=null,wi=!1;function Co(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;wi||ia==null||ia!==Wn(a)||(a=ia,"selectionStart"in a&&Hi(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Pa&&Ia(Pa,a)||(Pa=a,a=Qu(qi,"onSelect"),0<a.length&&(e=new eu("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=ia)))}function Dl(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var ca={animationend:Dl("Animation","AnimationEnd"),animationiteration:Dl("Animation","AnimationIteration"),animationstart:Dl("Animation","AnimationStart"),transitionrun:Dl("Transition","TransitionRun"),transitionstart:Dl("Transition","TransitionStart"),transitioncancel:Dl("Transition","TransitionCancel"),transitionend:Dl("Transition","TransitionEnd")},Gi={},jo={};we&&(jo=document.createElement("div").style,"AnimationEvent"in window||(delete ca.animationend.animation,delete ca.animationiteration.animation,delete ca.animationstart.animation),"TransitionEvent"in window||delete ca.transitionend.transition);function Ul(t){if(Gi[t])return Gi[t];if(!ca[t])return t;var e=ca[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in jo)return Gi[t]=e[l];return t}var Ho=Ul("animationend"),qo=Ul("animationiteration"),wo=Ul("animationstart"),Qv=Ul("transitionrun"),Vv=Ul("transitionstart"),Zv=Ul("transitioncancel"),Go=Ul("transitionend"),Bo=new Map,Bi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Bi.push("scrollEnd");function _e(t,e){Bo.set(t,e),Rl(e,[t])}var Yo=new WeakMap;function pe(t,e){if(typeof t=="object"&&t!==null){var l=Yo.get(t);return l!==void 0?l:(e={value:t,source:e,stack:eo(e)},Yo.set(t,e),e)}return{value:t,source:e,stack:eo(e)}}var Se=[],fa=0,Yi=0;function nu(){for(var t=fa,e=Yi=fa=0;e<t;){var l=Se[e];Se[e++]=null;var a=Se[e];Se[e++]=null;var n=Se[e];Se[e++]=null;var u=Se[e];if(Se[e++]=null,a!==null&&n!==null){var i=a.pending;i===null?n.next=n:(n.next=i.next,i.next=n),a.pending=n}u!==0&&Xo(l,n,u)}}function uu(t,e,l,a){Se[fa++]=t,Se[fa++]=e,Se[fa++]=l,Se[fa++]=a,Yi|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Xi(t,e,l,a){return uu(t,e,l,a),iu(t)}function oa(t,e){return uu(t,null,null,e),iu(t)}function Xo(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=t.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&e!==null&&(n=31-ie(l),t=u.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),u):null}function iu(t){if(50<zn)throw zn=0,kc=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var sa={};function Lv(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function fe(t,e,l,a){return new Lv(t,e,l,a)}function Qi(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Ge(t,e){var l=t.alternate;return l===null?(l=fe(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Qo(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function cu(t,e,l,a,n,u){var i=0;if(a=t,typeof t=="function")Qi(t)&&(i=1);else if(typeof t=="string")i=kh(t,l,X.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Dt:return t=fe(31,l,e,n),t.elementType=Dt,t.lanes=u,t;case P:return Cl(l.children,n,u,e);case F:i=8,n|=24;break;case L:return t=fe(12,l,e,n|2),t.elementType=L,t.lanes=u,t;case I:return t=fe(13,l,e,n),t.elementType=I,t.lanes=u,t;case bt:return t=fe(19,l,e,n),t.elementType=bt,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case st:case et:i=10;break t;case Tt:i=9;break t;case ot:i=11;break t;case Mt:i=14;break t;case Y:i=16,a=null;break t}i=29,l=Error(o(130,t===null?"null":typeof t,"")),a=null}return e=fe(i,l,e,n),e.elementType=t,e.type=a,e.lanes=u,e}function Cl(t,e,l,a){return t=fe(7,t,a,e),t.lanes=l,t}function Vi(t,e,l){return t=fe(6,t,null,e),t.lanes=l,t}function Zi(t,e,l){return e=fe(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ra=[],da=0,fu=null,ou=0,xe=[],Te=0,jl=null,Be=1,Ye="";function Hl(t,e){ra[da++]=ou,ra[da++]=fu,fu=t,ou=e}function Vo(t,e,l){xe[Te++]=Be,xe[Te++]=Ye,xe[Te++]=jl,jl=t;var a=Be;t=Ye;var n=32-ie(a)-1;a&=~(1<<n),l+=1;var u=32-ie(e)+n;if(30<u){var i=n-n%5;u=(a&(1<<i)-1).toString(32),a>>=i,n-=i,Be=1<<32-ie(e)+n|l<<n|a,Ye=u+t}else Be=1<<u|l<<n|a,Ye=t}function Li(t){t.return!==null&&(Hl(t,1),Vo(t,1,0))}function Ki(t){for(;t===fu;)fu=ra[--da],ra[da]=null,ou=ra[--da],ra[da]=null;for(;t===jl;)jl=xe[--Te],xe[Te]=null,Ye=xe[--Te],xe[Te]=null,Be=xe[--Te],xe[Te]=null}var It=null,Ut=null,vt=!1,ql=null,De=!1,ki=Error(o(519));function wl(t){var e=Error(o(418,""));throw ln(pe(e,t)),ki}function Zo(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[$t]=t,e[Pt]=a,l){case"dialog":ft("cancel",e),ft("close",e);break;case"iframe":case"object":case"embed":ft("load",e);break;case"video":case"audio":for(l=0;l<Mn.length;l++)ft(Mn[l],e);break;case"source":ft("error",e);break;case"img":case"image":case"link":ft("error",e),ft("load",e);break;case"details":ft("toggle",e);break;case"input":ft("invalid",e),no(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),$n(e);break;case"select":ft("invalid",e);break;case"textarea":ft("invalid",e),io(e,a.value,a.defaultValue,a.children),$n(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||cd(e.textContent,l)?(a.popover!=null&&(ft("beforetoggle",e),ft("toggle",e)),a.onScroll!=null&&ft("scroll",e),a.onScrollEnd!=null&&ft("scrollend",e),a.onClick!=null&&(e.onclick=Vu),e=!0):e=!1,e||wl(t)}function Lo(t){for(It=t.return;It;)switch(It.tag){case 5:case 13:De=!1;return;case 27:case 3:De=!0;return;default:It=It.return}}function tn(t){if(t!==It)return!1;if(!vt)return Lo(t),vt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||sf(t.type,t.memoizedProps)),l=!l),l&&Ut&&wl(t),Lo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Ut=Oe(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Ut=null}}else e===27?(e=Ut,Sl(t.type)?(t=vf,vf=null,Ut=t):Ut=e):Ut=It?Oe(t.stateNode.nextSibling):null;return!0}function en(){Ut=It=null,vt=!1}function Ko(){var t=ql;return t!==null&&(ae===null?ae=t:ae.push.apply(ae,t),ql=null),t}function ln(t){ql===null?ql=[t]:ql.push(t)}var Ji=D(null),Gl=null,Xe=null;function ul(t,e,l){H(Ji,e._currentValue),e._currentValue=l}function Qe(t){t._currentValue=Ji.current,G(Ji)}function $i(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function Wi(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var i=n.child;u=u.firstContext;t:for(;u!==null;){var f=u;u=n;for(var r=0;r<e.length;r++)if(f.context===e[r]){u.lanes|=l,f=u.alternate,f!==null&&(f.lanes|=l),$i(u.return,l,t),a||(i=null);break t}u=f.next}}else if(n.tag===18){if(i=n.return,i===null)throw Error(o(341));i.lanes|=l,u=i.alternate,u!==null&&(u.lanes|=l),$i(i,l,t),i=null}else i=n.child;if(i!==null)i.return=n;else for(i=n;i!==null;){if(i===t){i=null;break}if(n=i.sibling,n!==null){n.return=i.return,i=n;break}i=i.return}n=i}}function an(t,e,l,a){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var i=n.alternate;if(i===null)throw Error(o(387));if(i=i.memoizedProps,i!==null){var f=n.type;ce(n.pendingProps.value,i.value)||(t!==null?t.push(f):t=[f])}}else if(n===dt.current){if(i=n.alternate,i===null)throw Error(o(387));i.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Cn):t=[Cn])}n=n.return}t!==null&&Wi(e,t,l,a),e.flags|=262144}function su(t){for(t=t.firstContext;t!==null;){if(!ce(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Bl(t){Gl=t,Xe=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Wt(t){return ko(Gl,t)}function ru(t,e){return Gl===null&&Bl(t),ko(t,e)}function ko(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},Xe===null){if(t===null)throw Error(o(308));Xe=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Xe=Xe.next=e;return l}var Kv=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},kv=c.unstable_scheduleCallback,Jv=c.unstable_NormalPriority,Gt={$$typeof:et,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Fi(){return{controller:new Kv,data:new Map,refCount:0}}function nn(t){t.refCount--,t.refCount===0&&kv(Jv,function(){t.controller.abort()})}var un=null,Ii=0,ma=0,va=null;function $v(t,e){if(un===null){var l=un=[];Ii=0,ma=tf(),va={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Ii++,e.then(Jo,Jo),e}function Jo(){if(--Ii===0&&un!==null){va!==null&&(va.status="fulfilled");var t=un;un=null,ma=0,va=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Wv(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var $o=E.S;E.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&$v(t,e),$o!==null&&$o(t,e)};var Yl=D(null);function Pi(){var t=Yl.current;return t!==null?t:zt.pooledCache}function du(t,e){e===null?H(Yl,Yl.current):H(Yl,e.pool)}function Wo(){var t=Pi();return t===null?null:{parent:Gt._currentValue,pool:t}}var cn=Error(o(460)),Fo=Error(o(474)),mu=Error(o(542)),tc={then:function(){}};function Io(t){return t=t.status,t==="fulfilled"||t==="rejected"}function vu(){}function Po(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(vu,vu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,es(t),t;default:if(typeof e.status=="string")e.then(vu,vu);else{if(t=zt,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,es(t),t}throw fn=e,cn}}var fn=null;function ts(){if(fn===null)throw Error(o(459));var t=fn;return fn=null,t}function es(t){if(t===cn||t===mu)throw Error(o(483))}var il=!1;function ec(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function lc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function cl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function fl(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(yt&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=iu(t),Xo(t,null,l),e}return uu(t,a,e,l),iu(t)}function on(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,kf(t,l)}}function ac(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var i={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=i:u=u.next=i,l=l.next}while(l!==null);u===null?n=u=e:u=u.next=e}else n=u=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var nc=!1;function sn(){if(nc){var t=va;if(t!==null)throw t}}function rn(t,e,l,a){nc=!1;var n=t.updateQueue;il=!1;var u=n.firstBaseUpdate,i=n.lastBaseUpdate,f=n.shared.pending;if(f!==null){n.shared.pending=null;var r=f,p=r.next;r.next=null,i===null?u=p:i.next=p,i=r;var M=t.alternate;M!==null&&(M=M.updateQueue,f=M.lastBaseUpdate,f!==i&&(f===null?M.firstBaseUpdate=p:f.next=p,M.lastBaseUpdate=r))}if(u!==null){var N=n.baseState;i=0,M=p=r=null,f=u;do{var S=f.lane&-536870913,T=S!==f.lane;if(T?(rt&S)===S:(a&S)===S){S!==0&&S===ma&&(nc=!0),M!==null&&(M=M.next={lane:0,tag:f.tag,payload:f.payload,callback:null,next:null});t:{var $=t,k=f;S=e;var xt=l;switch(k.tag){case 1:if($=k.payload,typeof $=="function"){N=$.call(xt,N,S);break t}N=$;break t;case 3:$.flags=$.flags&-65537|128;case 0:if($=k.payload,S=typeof $=="function"?$.call(xt,N,S):$,S==null)break t;N=A({},N,S);break t;case 2:il=!0}}S=f.callback,S!==null&&(t.flags|=64,T&&(t.flags|=8192),T=n.callbacks,T===null?n.callbacks=[S]:T.push(S))}else T={lane:S,tag:f.tag,payload:f.payload,callback:f.callback,next:null},M===null?(p=M=T,r=N):M=M.next=T,i|=S;if(f=f.next,f===null){if(f=n.shared.pending,f===null)break;T=f,f=T.next,T.next=null,n.lastBaseUpdate=T,n.shared.pending=null}}while(!0);M===null&&(r=N),n.baseState=r,n.firstBaseUpdate=p,n.lastBaseUpdate=M,u===null&&(n.shared.lanes=0),yl|=i,t.lanes=i,t.memoizedState=N}}function ls(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function as(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)ls(l[t],e)}var ha=D(null),hu=D(0);function ns(t,e){t=$e,H(hu,t),H(ha,e),$e=t|e.baseLanes}function uc(){H(hu,$e),H(ha,ha.current)}function ic(){$e=hu.current,G(ha),G(hu)}var ol=0,at=null,pt=null,qt=null,yu=!1,ya=!1,Xl=!1,gu=0,dn=0,ga=null,Fv=0;function jt(){throw Error(o(321))}function cc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!ce(t[l],e[l]))return!1;return!0}function fc(t,e,l,a,n,u){return ol=u,at=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,E.H=t===null||t.memoizedState===null?Xs:Qs,Xl=!1,u=l(a,n),Xl=!1,ya&&(u=is(e,l,a,n)),us(t),u}function us(t){E.H=Au;var e=pt!==null&&pt.next!==null;if(ol=0,qt=pt=at=null,yu=!1,dn=0,ga=null,e)throw Error(o(300));t===null||Qt||(t=t.dependencies,t!==null&&su(t)&&(Qt=!0))}function is(t,e,l,a){at=t;var n=0;do{if(ya&&(ga=null),dn=0,ya=!1,25<=n)throw Error(o(301));if(n+=1,qt=pt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}E.H=nh,u=e(l,a)}while(ya);return u}function Iv(){var t=E.H,e=t.useState()[0];return e=typeof e.then=="function"?mn(e):e,t=t.useState()[0],(pt!==null?pt.memoizedState:null)!==t&&(at.flags|=1024),e}function oc(){var t=gu!==0;return gu=0,t}function sc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function rc(t){if(yu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}yu=!1}ol=0,qt=pt=at=null,ya=!1,dn=gu=0,ga=null}function ee(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return qt===null?at.memoizedState=qt=t:qt=qt.next=t,qt}function wt(){if(pt===null){var t=at.alternate;t=t!==null?t.memoizedState:null}else t=pt.next;var e=qt===null?at.memoizedState:qt.next;if(e!==null)qt=e,pt=t;else{if(t===null)throw at.alternate===null?Error(o(467)):Error(o(310));pt=t,t={memoizedState:pt.memoizedState,baseState:pt.baseState,baseQueue:pt.baseQueue,queue:pt.queue,next:null},qt===null?at.memoizedState=qt=t:qt=qt.next=t}return qt}function dc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function mn(t){var e=dn;return dn+=1,ga===null&&(ga=[]),t=Po(ga,t,e),e=at,(qt===null?e.memoizedState:qt.next)===null&&(e=e.alternate,E.H=e===null||e.memoizedState===null?Xs:Qs),t}function bu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return mn(t);if(t.$$typeof===et)return Wt(t)}throw Error(o(438,String(t)))}function mc(t){var e=null,l=at.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=at.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=dc(),at.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=ve;return e.index++,l}function Ve(t,e){return typeof e=="function"?e(t):e}function pu(t){var e=wt();return vc(e,pt,t)}function vc(t,e,l){var a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=l;var n=t.baseQueue,u=a.pending;if(u!==null){if(n!==null){var i=n.next;n.next=u.next,u.next=i}e.baseQueue=n=u,a.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var f=i=null,r=null,p=e,M=!1;do{var N=p.lane&-536870913;if(N!==p.lane?(rt&N)===N:(ol&N)===N){var S=p.revertLane;if(S===0)r!==null&&(r=r.next={lane:0,revertLane:0,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null}),N===ma&&(M=!0);else if((ol&S)===S){p=p.next,S===ma&&(M=!0);continue}else N={lane:0,revertLane:p.revertLane,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null},r===null?(f=r=N,i=u):r=r.next=N,at.lanes|=S,yl|=S;N=p.action,Xl&&l(u,N),u=p.hasEagerState?p.eagerState:l(u,N)}else S={lane:N,revertLane:p.revertLane,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null},r===null?(f=r=S,i=u):r=r.next=S,at.lanes|=N,yl|=N;p=p.next}while(p!==null&&p!==e);if(r===null?i=u:r.next=f,!ce(u,t.memoizedState)&&(Qt=!0,M&&(l=va,l!==null)))throw l;t.memoizedState=u,t.baseState=i,t.baseQueue=r,a.lastRenderedState=u}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function hc(t){var e=wt(),l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,u=e.memoizedState;if(n!==null){l.pending=null;var i=n=n.next;do u=t(u,i.action),i=i.next;while(i!==n);ce(u,e.memoizedState)||(Qt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,a]}function cs(t,e,l){var a=at,n=wt(),u=vt;if(u){if(l===void 0)throw Error(o(407));l=l()}else l=e();var i=!ce((pt||n).memoizedState,l);i&&(n.memoizedState=l,Qt=!0),n=n.queue;var f=ss.bind(null,a,n,t);if(vn(2048,8,f,[t]),n.getSnapshot!==e||i||qt!==null&&qt.memoizedState.tag&1){if(a.flags|=2048,ba(9,Su(),os.bind(null,a,n,l,e),null),zt===null)throw Error(o(349));u||(ol&124)!==0||fs(a,e,l)}return l}function fs(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=at.updateQueue,e===null?(e=dc(),at.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function os(t,e,l,a){e.value=l,e.getSnapshot=a,rs(e)&&ds(t)}function ss(t,e,l){return l(function(){rs(e)&&ds(t)})}function rs(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!ce(t,l)}catch{return!0}}function ds(t){var e=oa(t,2);e!==null&&me(e,t,2)}function yc(t){var e=ee();if(typeof t=="function"){var l=t;if(t=l(),Xl){ll(!0);try{l()}finally{ll(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ve,lastRenderedState:t},e}function ms(t,e,l,a){return t.baseState=l,vc(t,pt,typeof a=="function"?a:Ve)}function Pv(t,e,l,a,n){if(Tu(t))throw Error(o(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){u.listeners.push(i)}};E.T!==null?l(!0):u.isTransition=!1,a(u),l=e.pending,l===null?(u.next=e.pending=u,vs(e,u)):(u.next=l.next,e.pending=l.next=u)}}function vs(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var u=E.T,i={};E.T=i;try{var f=l(n,a),r=E.S;r!==null&&r(i,f),hs(t,e,f)}catch(p){gc(t,e,p)}finally{E.T=u}}else try{u=l(n,a),hs(t,e,u)}catch(p){gc(t,e,p)}}function hs(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){ys(t,e,a)},function(a){return gc(t,e,a)}):ys(t,e,l)}function ys(t,e,l){e.status="fulfilled",e.value=l,gs(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,vs(t,l)))}function gc(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,gs(e),e=e.next;while(e!==a)}t.action=null}function gs(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function bs(t,e){return e}function ps(t,e){if(vt){var l=zt.formState;if(l!==null){t:{var a=at;if(vt){if(Ut){e:{for(var n=Ut,u=De;n.nodeType!==8;){if(!u){n=null;break e}if(n=Oe(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Ut=Oe(n.nextSibling),a=n.data==="F!";break t}}wl(a)}a=!1}a&&(e=l[0])}}return l=ee(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:bs,lastRenderedState:e},l.queue=a,l=Gs.bind(null,at,a),a.dispatch=l,a=yc(!1),u=Tc.bind(null,at,!1,a.queue),a=ee(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=Pv.bind(null,at,n,u,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Ss(t){var e=wt();return xs(e,pt,t)}function xs(t,e,l){if(e=vc(t,e,bs)[0],t=pu(Ve)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=mn(e)}catch(i){throw i===cn?mu:i}else a=e;e=wt();var n=e.queue,u=n.dispatch;return l!==e.memoizedState&&(at.flags|=2048,ba(9,Su(),th.bind(null,n,l),null)),[a,u,t]}function th(t,e){t.action=e}function Ts(t){var e=wt(),l=pt;if(l!==null)return xs(e,l,t);wt(),e=e.memoizedState,l=wt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function ba(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=at.updateQueue,e===null&&(e=dc(),at.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function Su(){return{destroy:void 0,resource:void 0}}function As(){return wt().memoizedState}function xu(t,e,l,a){var n=ee();a=a===void 0?null:a,at.flags|=t,n.memoizedState=ba(1|e,Su(),l,a)}function vn(t,e,l,a){var n=wt();a=a===void 0?null:a;var u=n.memoizedState.inst;pt!==null&&a!==null&&cc(a,pt.memoizedState.deps)?n.memoizedState=ba(e,u,l,a):(at.flags|=t,n.memoizedState=ba(1|e,u,l,a))}function Es(t,e){xu(8390656,8,t,e)}function zs(t,e){vn(2048,8,t,e)}function _s(t,e){return vn(4,2,t,e)}function Ms(t,e){return vn(4,4,t,e)}function Os(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Rs(t,e,l){l=l!=null?l.concat([t]):null,vn(4,4,Os.bind(null,e,t),l)}function bc(){}function Ns(t,e){var l=wt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&cc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function Ds(t,e){var l=wt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&cc(e,a[1]))return a[0];if(a=t(),Xl){ll(!0);try{t()}finally{ll(!1)}}return l.memoizedState=[a,e],a}function pc(t,e,l){return l===void 0||(ol&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=jr(),at.lanes|=t,yl|=t,l)}function Us(t,e,l,a){return ce(l,e)?l:ha.current!==null?(t=pc(t,l,a),ce(t,e)||(Qt=!0),t):(ol&42)===0?(Qt=!0,t.memoizedState=l):(t=jr(),at.lanes|=t,yl|=t,e)}function Cs(t,e,l,a,n){var u=q.p;q.p=u!==0&&8>u?u:8;var i=E.T,f={};E.T=f,Tc(t,!1,e,l);try{var r=n(),p=E.S;if(p!==null&&p(f,r),r!==null&&typeof r=="object"&&typeof r.then=="function"){var M=Wv(r,a);hn(t,e,M,de(t))}else hn(t,e,a,de(t))}catch(N){hn(t,e,{then:function(){},status:"rejected",reason:N},de())}finally{q.p=u,E.T=i}}function eh(){}function Sc(t,e,l,a){if(t.tag!==5)throw Error(o(476));var n=js(t).queue;Cs(t,n,e,C,l===null?eh:function(){return Hs(t),l(a)})}function js(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:C,baseState:C,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ve,lastRenderedState:C},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ve,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Hs(t){var e=js(t).next.queue;hn(t,e,{},de())}function xc(){return Wt(Cn)}function qs(){return wt().memoizedState}function ws(){return wt().memoizedState}function lh(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=de();t=cl(l);var a=fl(e,t,l);a!==null&&(me(a,e,l),on(a,e,l)),e={cache:Fi()},t.payload=e;return}e=e.return}}function ah(t,e,l){var a=de();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Tu(t)?Bs(e,l):(l=Xi(t,e,l,a),l!==null&&(me(l,t,a),Ys(l,e,a)))}function Gs(t,e,l){var a=de();hn(t,e,l,a)}function hn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Tu(t))Bs(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var i=e.lastRenderedState,f=u(i,l);if(n.hasEagerState=!0,n.eagerState=f,ce(f,i))return uu(t,e,n,0),zt===null&&nu(),!1}catch{}finally{}if(l=Xi(t,e,n,a),l!==null)return me(l,t,a),Ys(l,e,a),!0}return!1}function Tc(t,e,l,a){if(a={lane:2,revertLane:tf(),action:a,hasEagerState:!1,eagerState:null,next:null},Tu(t)){if(e)throw Error(o(479))}else e=Xi(t,l,a,2),e!==null&&me(e,t,2)}function Tu(t){var e=t.alternate;return t===at||e!==null&&e===at}function Bs(t,e){ya=yu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Ys(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,kf(t,l)}}var Au={readContext:Wt,use:bu,useCallback:jt,useContext:jt,useEffect:jt,useImperativeHandle:jt,useLayoutEffect:jt,useInsertionEffect:jt,useMemo:jt,useReducer:jt,useRef:jt,useState:jt,useDebugValue:jt,useDeferredValue:jt,useTransition:jt,useSyncExternalStore:jt,useId:jt,useHostTransitionStatus:jt,useFormState:jt,useActionState:jt,useOptimistic:jt,useMemoCache:jt,useCacheRefresh:jt},Xs={readContext:Wt,use:bu,useCallback:function(t,e){return ee().memoizedState=[t,e===void 0?null:e],t},useContext:Wt,useEffect:Es,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,xu(4194308,4,Os.bind(null,e,t),l)},useLayoutEffect:function(t,e){return xu(4194308,4,t,e)},useInsertionEffect:function(t,e){xu(4,2,t,e)},useMemo:function(t,e){var l=ee();e=e===void 0?null:e;var a=t();if(Xl){ll(!0);try{t()}finally{ll(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=ee();if(l!==void 0){var n=l(e);if(Xl){ll(!0);try{l(e)}finally{ll(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=ah.bind(null,at,t),[a.memoizedState,t]},useRef:function(t){var e=ee();return t={current:t},e.memoizedState=t},useState:function(t){t=yc(t);var e=t.queue,l=Gs.bind(null,at,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:bc,useDeferredValue:function(t,e){var l=ee();return pc(l,t,e)},useTransition:function(){var t=yc(!1);return t=Cs.bind(null,at,t.queue,!0,!1),ee().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=at,n=ee();if(vt){if(l===void 0)throw Error(o(407));l=l()}else{if(l=e(),zt===null)throw Error(o(349));(rt&124)!==0||fs(a,e,l)}n.memoizedState=l;var u={value:l,getSnapshot:e};return n.queue=u,Es(ss.bind(null,a,u,t),[t]),a.flags|=2048,ba(9,Su(),os.bind(null,a,u,l,e),null),l},useId:function(){var t=ee(),e=zt.identifierPrefix;if(vt){var l=Ye,a=Be;l=(a&~(1<<32-ie(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=gu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=Fv++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:xc,useFormState:ps,useActionState:ps,useOptimistic:function(t){var e=ee();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=Tc.bind(null,at,!0,l),l.dispatch=e,[t,e]},useMemoCache:mc,useCacheRefresh:function(){return ee().memoizedState=lh.bind(null,at)}},Qs={readContext:Wt,use:bu,useCallback:Ns,useContext:Wt,useEffect:zs,useImperativeHandle:Rs,useInsertionEffect:_s,useLayoutEffect:Ms,useMemo:Ds,useReducer:pu,useRef:As,useState:function(){return pu(Ve)},useDebugValue:bc,useDeferredValue:function(t,e){var l=wt();return Us(l,pt.memoizedState,t,e)},useTransition:function(){var t=pu(Ve)[0],e=wt().memoizedState;return[typeof t=="boolean"?t:mn(t),e]},useSyncExternalStore:cs,useId:qs,useHostTransitionStatus:xc,useFormState:Ss,useActionState:Ss,useOptimistic:function(t,e){var l=wt();return ms(l,pt,t,e)},useMemoCache:mc,useCacheRefresh:ws},nh={readContext:Wt,use:bu,useCallback:Ns,useContext:Wt,useEffect:zs,useImperativeHandle:Rs,useInsertionEffect:_s,useLayoutEffect:Ms,useMemo:Ds,useReducer:hc,useRef:As,useState:function(){return hc(Ve)},useDebugValue:bc,useDeferredValue:function(t,e){var l=wt();return pt===null?pc(l,t,e):Us(l,pt.memoizedState,t,e)},useTransition:function(){var t=hc(Ve)[0],e=wt().memoizedState;return[typeof t=="boolean"?t:mn(t),e]},useSyncExternalStore:cs,useId:qs,useHostTransitionStatus:xc,useFormState:Ts,useActionState:Ts,useOptimistic:function(t,e){var l=wt();return pt!==null?ms(l,pt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:mc,useCacheRefresh:ws},pa=null,yn=0;function Eu(t){var e=yn;return yn+=1,pa===null&&(pa=[]),Po(pa,t,e)}function gn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function zu(t,e){throw e.$$typeof===j?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Vs(t){var e=t._init;return e(t._payload)}function Zs(t){function e(h,v){if(t){var b=h.deletions;b===null?(h.deletions=[v],h.flags|=16):b.push(v)}}function l(h,v){if(!t)return null;for(;v!==null;)e(h,v),v=v.sibling;return null}function a(h){for(var v=new Map;h!==null;)h.key!==null?v.set(h.key,h):v.set(h.index,h),h=h.sibling;return v}function n(h,v){return h=Ge(h,v),h.index=0,h.sibling=null,h}function u(h,v,b){return h.index=b,t?(b=h.alternate,b!==null?(b=b.index,b<v?(h.flags|=67108866,v):b):(h.flags|=67108866,v)):(h.flags|=1048576,v)}function i(h){return t&&h.alternate===null&&(h.flags|=67108866),h}function f(h,v,b,O){return v===null||v.tag!==6?(v=Vi(b,h.mode,O),v.return=h,v):(v=n(v,b),v.return=h,v)}function r(h,v,b,O){var Q=b.type;return Q===P?M(h,v,b.props.children,O,b.key):v!==null&&(v.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===Y&&Vs(Q)===v.type)?(v=n(v,b.props),gn(v,b),v.return=h,v):(v=cu(b.type,b.key,b.props,null,h.mode,O),gn(v,b),v.return=h,v)}function p(h,v,b,O){return v===null||v.tag!==4||v.stateNode.containerInfo!==b.containerInfo||v.stateNode.implementation!==b.implementation?(v=Zi(b,h.mode,O),v.return=h,v):(v=n(v,b.children||[]),v.return=h,v)}function M(h,v,b,O,Q){return v===null||v.tag!==7?(v=Cl(b,h.mode,O,Q),v.return=h,v):(v=n(v,b),v.return=h,v)}function N(h,v,b){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=Vi(""+v,h.mode,b),v.return=h,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case B:return b=cu(v.type,v.key,v.props,null,h.mode,b),gn(b,v),b.return=h,b;case tt:return v=Zi(v,h.mode,b),v.return=h,v;case Y:var O=v._init;return v=O(v._payload),N(h,v,b)}if(_t(v)||Rt(v))return v=Cl(v,h.mode,b,null),v.return=h,v;if(typeof v.then=="function")return N(h,Eu(v),b);if(v.$$typeof===et)return N(h,ru(h,v),b);zu(h,v)}return null}function S(h,v,b,O){var Q=v!==null?v.key:null;if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return Q!==null?null:f(h,v,""+b,O);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case B:return b.key===Q?r(h,v,b,O):null;case tt:return b.key===Q?p(h,v,b,O):null;case Y:return Q=b._init,b=Q(b._payload),S(h,v,b,O)}if(_t(b)||Rt(b))return Q!==null?null:M(h,v,b,O,null);if(typeof b.then=="function")return S(h,v,Eu(b),O);if(b.$$typeof===et)return S(h,v,ru(h,b),O);zu(h,b)}return null}function T(h,v,b,O,Q){if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return h=h.get(b)||null,f(v,h,""+O,Q);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case B:return h=h.get(O.key===null?b:O.key)||null,r(v,h,O,Q);case tt:return h=h.get(O.key===null?b:O.key)||null,p(v,h,O,Q);case Y:var ut=O._init;return O=ut(O._payload),T(h,v,b,O,Q)}if(_t(O)||Rt(O))return h=h.get(b)||null,M(v,h,O,Q,null);if(typeof O.then=="function")return T(h,v,b,Eu(O),Q);if(O.$$typeof===et)return T(h,v,b,ru(v,O),Q);zu(v,O)}return null}function $(h,v,b,O){for(var Q=null,ut=null,K=v,J=v=0,Zt=null;K!==null&&J<b.length;J++){K.index>J?(Zt=K,K=null):Zt=K.sibling;var mt=S(h,K,b[J],O);if(mt===null){K===null&&(K=Zt);break}t&&K&&mt.alternate===null&&e(h,K),v=u(mt,v,J),ut===null?Q=mt:ut.sibling=mt,ut=mt,K=Zt}if(J===b.length)return l(h,K),vt&&Hl(h,J),Q;if(K===null){for(;J<b.length;J++)K=N(h,b[J],O),K!==null&&(v=u(K,v,J),ut===null?Q=K:ut.sibling=K,ut=K);return vt&&Hl(h,J),Q}for(K=a(K);J<b.length;J++)Zt=T(K,h,J,b[J],O),Zt!==null&&(t&&Zt.alternate!==null&&K.delete(Zt.key===null?J:Zt.key),v=u(Zt,v,J),ut===null?Q=Zt:ut.sibling=Zt,ut=Zt);return t&&K.forEach(function(zl){return e(h,zl)}),vt&&Hl(h,J),Q}function k(h,v,b,O){if(b==null)throw Error(o(151));for(var Q=null,ut=null,K=v,J=v=0,Zt=null,mt=b.next();K!==null&&!mt.done;J++,mt=b.next()){K.index>J?(Zt=K,K=null):Zt=K.sibling;var zl=S(h,K,mt.value,O);if(zl===null){K===null&&(K=Zt);break}t&&K&&zl.alternate===null&&e(h,K),v=u(zl,v,J),ut===null?Q=zl:ut.sibling=zl,ut=zl,K=Zt}if(mt.done)return l(h,K),vt&&Hl(h,J),Q;if(K===null){for(;!mt.done;J++,mt=b.next())mt=N(h,mt.value,O),mt!==null&&(v=u(mt,v,J),ut===null?Q=mt:ut.sibling=mt,ut=mt);return vt&&Hl(h,J),Q}for(K=a(K);!mt.done;J++,mt=b.next())mt=T(K,h,J,mt.value,O),mt!==null&&(t&&mt.alternate!==null&&K.delete(mt.key===null?J:mt.key),v=u(mt,v,J),ut===null?Q=mt:ut.sibling=mt,ut=mt);return t&&K.forEach(function(uy){return e(h,uy)}),vt&&Hl(h,J),Q}function xt(h,v,b,O){if(typeof b=="object"&&b!==null&&b.type===P&&b.key===null&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case B:t:{for(var Q=b.key;v!==null;){if(v.key===Q){if(Q=b.type,Q===P){if(v.tag===7){l(h,v.sibling),O=n(v,b.props.children),O.return=h,h=O;break t}}else if(v.elementType===Q||typeof Q=="object"&&Q!==null&&Q.$$typeof===Y&&Vs(Q)===v.type){l(h,v.sibling),O=n(v,b.props),gn(O,b),O.return=h,h=O;break t}l(h,v);break}else e(h,v);v=v.sibling}b.type===P?(O=Cl(b.props.children,h.mode,O,b.key),O.return=h,h=O):(O=cu(b.type,b.key,b.props,null,h.mode,O),gn(O,b),O.return=h,h=O)}return i(h);case tt:t:{for(Q=b.key;v!==null;){if(v.key===Q)if(v.tag===4&&v.stateNode.containerInfo===b.containerInfo&&v.stateNode.implementation===b.implementation){l(h,v.sibling),O=n(v,b.children||[]),O.return=h,h=O;break t}else{l(h,v);break}else e(h,v);v=v.sibling}O=Zi(b,h.mode,O),O.return=h,h=O}return i(h);case Y:return Q=b._init,b=Q(b._payload),xt(h,v,b,O)}if(_t(b))return $(h,v,b,O);if(Rt(b)){if(Q=Rt(b),typeof Q!="function")throw Error(o(150));return b=Q.call(b),k(h,v,b,O)}if(typeof b.then=="function")return xt(h,v,Eu(b),O);if(b.$$typeof===et)return xt(h,v,ru(h,b),O);zu(h,b)}return typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint"?(b=""+b,v!==null&&v.tag===6?(l(h,v.sibling),O=n(v,b),O.return=h,h=O):(l(h,v),O=Vi(b,h.mode,O),O.return=h,h=O),i(h)):l(h,v)}return function(h,v,b,O){try{yn=0;var Q=xt(h,v,b,O);return pa=null,Q}catch(K){if(K===cn||K===mu)throw K;var ut=fe(29,K,null,h.mode);return ut.lanes=O,ut.return=h,ut}finally{}}}var Sa=Zs(!0),Ls=Zs(!1),Ae=D(null),Ue=null;function sl(t){var e=t.alternate;H(Bt,Bt.current&1),H(Ae,t),Ue===null&&(e===null||ha.current!==null||e.memoizedState!==null)&&(Ue=t)}function Ks(t){if(t.tag===22){if(H(Bt,Bt.current),H(Ae,t),Ue===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ue=t)}}else rl()}function rl(){H(Bt,Bt.current),H(Ae,Ae.current)}function Ze(t){G(Ae),Ue===t&&(Ue=null),G(Bt)}var Bt=D(0);function _u(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||mf(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Ac(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:A({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Ec={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=de(),n=cl(a);n.payload=e,l!=null&&(n.callback=l),e=fl(t,n,a),e!==null&&(me(e,t,a),on(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=de(),n=cl(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=fl(t,n,a),e!==null&&(me(e,t,a),on(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=de(),a=cl(l);a.tag=2,e!=null&&(a.callback=e),e=fl(t,a,l),e!==null&&(me(e,t,l),on(e,t,l))}};function ks(t,e,l,a,n,u,i){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,u,i):e.prototype&&e.prototype.isPureReactComponent?!Ia(l,a)||!Ia(n,u):!0}function Js(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&Ec.enqueueReplaceState(e,e.state,null)}function Ql(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=A({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var Mu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function $s(t){Mu(t)}function Ws(t){console.error(t)}function Fs(t){Mu(t)}function Ou(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function Is(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function zc(t,e,l){return l=cl(l),l.tag=3,l.payload={element:null},l.callback=function(){Ou(t,e)},l}function Ps(t){return t=cl(t),t.tag=3,t}function tr(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;t.payload=function(){return n(u)},t.callback=function(){Is(e,l,a)}}var i=l.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(t.callback=function(){Is(e,l,a),typeof n!="function"&&(gl===null?gl=new Set([this]):gl.add(this));var f=a.stack;this.componentDidCatch(a.value,{componentStack:f!==null?f:""})})}function uh(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&an(e,l,n,!0),l=Ae.current,l!==null){switch(l.tag){case 13:return Ue===null?$c():l.alternate===null&&Ct===0&&(Ct=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===tc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),Fc(t,a,n)),!1;case 22:return l.flags|=65536,a===tc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),Fc(t,a,n)),!1}throw Error(o(435,l.tag))}return Fc(t,a,n),$c(),!1}if(vt)return e=Ae.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==ki&&(t=Error(o(422),{cause:a}),ln(pe(t,l)))):(a!==ki&&(e=Error(o(423),{cause:a}),ln(pe(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=pe(a,l),n=zc(t.stateNode,a,n),ac(t,n),Ct!==4&&(Ct=2)),!1;var u=Error(o(520),{cause:a});if(u=pe(u,l),En===null?En=[u]:En.push(u),Ct!==4&&(Ct=2),e===null)return!0;a=pe(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=zc(l.stateNode,a,t),ac(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(gl===null||!gl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=Ps(n),tr(n,t,l,a),ac(l,n),!1}l=l.return}while(l!==null);return!1}var er=Error(o(461)),Qt=!1;function Lt(t,e,l,a){e.child=t===null?Ls(e,null,l,a):Sa(e,t.child,l,a)}function lr(t,e,l,a,n){l=l.render;var u=e.ref;if("ref"in a){var i={};for(var f in a)f!=="ref"&&(i[f]=a[f])}else i=a;return Bl(e),a=fc(t,e,l,i,u,n),f=oc(),t!==null&&!Qt?(sc(t,e,n),Le(t,e,n)):(vt&&f&&Li(e),e.flags|=1,Lt(t,e,a,n),e.child)}function ar(t,e,l,a,n){if(t===null){var u=l.type;return typeof u=="function"&&!Qi(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,nr(t,e,u,a,n)):(t=cu(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Cc(t,n)){var i=u.memoizedProps;if(l=l.compare,l=l!==null?l:Ia,l(i,a)&&t.ref===e.ref)return Le(t,e,n)}return e.flags|=1,t=Ge(u,a),t.ref=e.ref,t.return=e,e.child=t}function nr(t,e,l,a,n){if(t!==null){var u=t.memoizedProps;if(Ia(u,a)&&t.ref===e.ref)if(Qt=!1,e.pendingProps=a=u,Cc(t,n))(t.flags&131072)!==0&&(Qt=!0);else return e.lanes=t.lanes,Le(t,e,n)}return _c(t,e,l,a,n)}function ur(t,e,l){var a=e.pendingProps,n=a.children,u=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~a}else e.childLanes=0,e.child=null;return ir(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&du(e,u!==null?u.cachePool:null),u!==null?ns(e,u):uc(),Ks(e);else return e.lanes=e.childLanes=536870912,ir(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(du(e,u.cachePool),ns(e,u),rl(),e.memoizedState=null):(t!==null&&du(e,null),uc(),rl());return Lt(t,e,n,l),e.child}function ir(t,e,l,a){var n=Pi();return n=n===null?null:{parent:Gt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&du(e,null),uc(),Ks(e),t!==null&&an(t,e,a,!0),null}function Ru(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(o(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function _c(t,e,l,a,n){return Bl(e),l=fc(t,e,l,a,void 0,n),a=oc(),t!==null&&!Qt?(sc(t,e,n),Le(t,e,n)):(vt&&a&&Li(e),e.flags|=1,Lt(t,e,l,n),e.child)}function cr(t,e,l,a,n,u){return Bl(e),e.updateQueue=null,l=is(e,a,l,n),us(t),a=oc(),t!==null&&!Qt?(sc(t,e,u),Le(t,e,u)):(vt&&a&&Li(e),e.flags|=1,Lt(t,e,l,u),e.child)}function fr(t,e,l,a,n){if(Bl(e),e.stateNode===null){var u=sa,i=l.contextType;typeof i=="object"&&i!==null&&(u=Wt(i)),u=new l(a,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Ec,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=a,u.state=e.memoizedState,u.refs={},ec(e),i=l.contextType,u.context=typeof i=="object"&&i!==null?Wt(i):sa,u.state=e.memoizedState,i=l.getDerivedStateFromProps,typeof i=="function"&&(Ac(e,l,i,a),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(i=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),i!==u.state&&Ec.enqueueReplaceState(u,u.state,null),rn(e,a,u,n),sn(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){u=e.stateNode;var f=e.memoizedProps,r=Ql(l,f);u.props=r;var p=u.context,M=l.contextType;i=sa,typeof M=="object"&&M!==null&&(i=Wt(M));var N=l.getDerivedStateFromProps;M=typeof N=="function"||typeof u.getSnapshotBeforeUpdate=="function",f=e.pendingProps!==f,M||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f||p!==i)&&Js(e,u,a,i),il=!1;var S=e.memoizedState;u.state=S,rn(e,a,u,n),sn(),p=e.memoizedState,f||S!==p||il?(typeof N=="function"&&(Ac(e,l,N,a),p=e.memoizedState),(r=il||ks(e,l,r,a,S,p,i))?(M||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=p),u.props=a,u.state=p,u.context=i,a=r):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{u=e.stateNode,lc(t,e),i=e.memoizedProps,M=Ql(l,i),u.props=M,N=e.pendingProps,S=u.context,p=l.contextType,r=sa,typeof p=="object"&&p!==null&&(r=Wt(p)),f=l.getDerivedStateFromProps,(p=typeof f=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i!==N||S!==r)&&Js(e,u,a,r),il=!1,S=e.memoizedState,u.state=S,rn(e,a,u,n),sn();var T=e.memoizedState;i!==N||S!==T||il||t!==null&&t.dependencies!==null&&su(t.dependencies)?(typeof f=="function"&&(Ac(e,l,f,a),T=e.memoizedState),(M=il||ks(e,l,M,a,S,T,r)||t!==null&&t.dependencies!==null&&su(t.dependencies))?(p||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,T,r),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,T,r)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&S===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&S===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=T),u.props=a,u.state=T,u.context=r,a=M):(typeof u.componentDidUpdate!="function"||i===t.memoizedProps&&S===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||i===t.memoizedProps&&S===t.memoizedState||(e.flags|=1024),a=!1)}return u=a,Ru(t,e),a=(e.flags&128)!==0,u||a?(u=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&a?(e.child=Sa(e,t.child,null,n),e.child=Sa(e,null,l,n)):Lt(t,e,l,n),e.memoizedState=u.state,t=e.child):t=Le(t,e,n),t}function or(t,e,l,a){return en(),e.flags|=256,Lt(t,e,l,a),e.child}var Mc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Oc(t){return{baseLanes:t,cachePool:Wo()}}function Rc(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=Ee),t}function sr(t,e,l){var a=e.pendingProps,n=!1,u=(e.flags&128)!==0,i;if((i=u)||(i=t!==null&&t.memoizedState===null?!1:(Bt.current&2)!==0),i&&(n=!0,e.flags&=-129),i=(e.flags&32)!==0,e.flags&=-33,t===null){if(vt){if(n?sl(e):rl(),vt){var f=Ut,r;if(r=f){t:{for(r=f,f=De;r.nodeType!==8;){if(!f){f=null;break t}if(r=Oe(r.nextSibling),r===null){f=null;break t}}f=r}f!==null?(e.memoizedState={dehydrated:f,treeContext:jl!==null?{id:Be,overflow:Ye}:null,retryLane:536870912,hydrationErrors:null},r=fe(18,null,null,0),r.stateNode=f,r.return=e,e.child=r,It=e,Ut=null,r=!0):r=!1}r||wl(e)}if(f=e.memoizedState,f!==null&&(f=f.dehydrated,f!==null))return mf(f)?e.lanes=32:e.lanes=536870912,null;Ze(e)}return f=a.children,a=a.fallback,n?(rl(),n=e.mode,f=Nu({mode:"hidden",children:f},n),a=Cl(a,n,l,null),f.return=e,a.return=e,f.sibling=a,e.child=f,n=e.child,n.memoizedState=Oc(l),n.childLanes=Rc(t,i,l),e.memoizedState=Mc,a):(sl(e),Nc(e,f))}if(r=t.memoizedState,r!==null&&(f=r.dehydrated,f!==null)){if(u)e.flags&256?(sl(e),e.flags&=-257,e=Dc(t,e,l)):e.memoizedState!==null?(rl(),e.child=t.child,e.flags|=128,e=null):(rl(),n=a.fallback,f=e.mode,a=Nu({mode:"visible",children:a.children},f),n=Cl(n,f,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Sa(e,t.child,null,l),a=e.child,a.memoizedState=Oc(l),a.childLanes=Rc(t,i,l),e.memoizedState=Mc,e=n);else if(sl(e),mf(f)){if(i=f.nextSibling&&f.nextSibling.dataset,i)var p=i.dgst;i=p,a=Error(o(419)),a.stack="",a.digest=i,ln({value:a,source:null,stack:null}),e=Dc(t,e,l)}else if(Qt||an(t,e,l,!1),i=(l&t.childLanes)!==0,Qt||i){if(i=zt,i!==null&&(a=l&-l,a=(a&42)!==0?1:mi(a),a=(a&(i.suspendedLanes|l))!==0?0:a,a!==0&&a!==r.retryLane))throw r.retryLane=a,oa(t,a),me(i,t,a),er;f.data==="$?"||$c(),e=Dc(t,e,l)}else f.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=r.treeContext,Ut=Oe(f.nextSibling),It=e,vt=!0,ql=null,De=!1,t!==null&&(xe[Te++]=Be,xe[Te++]=Ye,xe[Te++]=jl,Be=t.id,Ye=t.overflow,jl=e),e=Nc(e,a.children),e.flags|=4096);return e}return n?(rl(),n=a.fallback,f=e.mode,r=t.child,p=r.sibling,a=Ge(r,{mode:"hidden",children:a.children}),a.subtreeFlags=r.subtreeFlags&65011712,p!==null?n=Ge(p,n):(n=Cl(n,f,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,f=t.child.memoizedState,f===null?f=Oc(l):(r=f.cachePool,r!==null?(p=Gt._currentValue,r=r.parent!==p?{parent:p,pool:p}:r):r=Wo(),f={baseLanes:f.baseLanes|l,cachePool:r}),n.memoizedState=f,n.childLanes=Rc(t,i,l),e.memoizedState=Mc,a):(sl(e),l=t.child,t=l.sibling,l=Ge(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(i=e.deletions,i===null?(e.deletions=[t],e.flags|=16):i.push(t)),e.child=l,e.memoizedState=null,l)}function Nc(t,e){return e=Nu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Nu(t,e){return t=fe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Dc(t,e,l){return Sa(e,t.child,null,l),t=Nc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function rr(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),$i(t.return,e,l)}function Uc(t,e,l,a,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function dr(t,e,l){var a=e.pendingProps,n=a.revealOrder,u=a.tail;if(Lt(t,e,a.children,l),a=Bt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&rr(t,l,e);else if(t.tag===19)rr(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(H(Bt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&_u(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),Uc(e,!1,n,l,u);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&_u(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}Uc(e,!0,l,null,u);break;case"together":Uc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Le(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),yl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(an(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,l=Ge(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=Ge(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Cc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&su(t)))}function ih(t,e,l){switch(e.tag){case 3:At(e,e.stateNode.containerInfo),ul(e,Gt,t.memoizedState.cache),en();break;case 27:case 5:Pe(e);break;case 4:At(e,e.stateNode.containerInfo);break;case 10:ul(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(sl(e),e.flags|=128,null):(l&e.child.childLanes)!==0?sr(t,e,l):(sl(e),t=Le(t,e,l),t!==null?t.sibling:null);sl(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(an(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return dr(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),H(Bt,Bt.current),a)break;return null;case 22:case 23:return e.lanes=0,ur(t,e,l);case 24:ul(e,Gt,t.memoizedState.cache)}return Le(t,e,l)}function mr(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Qt=!0;else{if(!Cc(t,l)&&(e.flags&128)===0)return Qt=!1,ih(t,e,l);Qt=(t.flags&131072)!==0}else Qt=!1,vt&&(e.flags&1048576)!==0&&Vo(e,ou,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Qi(a)?(t=Ql(a,t),e.tag=1,e=fr(null,e,a,t,l)):(e.tag=0,e=_c(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===ot){e.tag=11,e=lr(null,e,a,t,l);break t}else if(n===Mt){e.tag=14,e=ar(null,e,a,t,l);break t}}throw e=ye(a)||a,Error(o(306,e,""))}}return e;case 0:return _c(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=Ql(a,e.pendingProps),fr(t,e,a,n,l);case 3:t:{if(At(e,e.stateNode.containerInfo),t===null)throw Error(o(387));a=e.pendingProps;var u=e.memoizedState;n=u.element,lc(t,e),rn(e,a,null,l);var i=e.memoizedState;if(a=i.cache,ul(e,Gt,a),a!==u.cache&&Wi(e,[Gt],l,!0),sn(),a=i.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:i.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=or(t,e,a,l);break t}else if(a!==n){n=pe(Error(o(424)),e),ln(n),e=or(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ut=Oe(t.firstChild),It=e,vt=!0,ql=null,De=!0,l=Ls(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(en(),a===n){e=Le(t,e,l);break t}Lt(t,e,a,l)}e=e.child}return e;case 26:return Ru(t,e),t===null?(l=gd(e.type,null,e.pendingProps,null))?e.memoizedState=l:vt||(l=e.type,t=e.pendingProps,a=Zu(W.current).createElement(l),a[$t]=e,a[Pt]=t,kt(a,l,t),Xt(a),e.stateNode=a):e.memoizedState=gd(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Pe(e),t===null&&vt&&(a=e.stateNode=vd(e.type,e.pendingProps,W.current),It=e,De=!0,n=Ut,Sl(e.type)?(vf=n,Ut=Oe(a.firstChild)):Ut=n),Lt(t,e,e.pendingProps.children,l),Ru(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&vt&&((n=a=Ut)&&(a=jh(a,e.type,e.pendingProps,De),a!==null?(e.stateNode=a,It=e,Ut=Oe(a.firstChild),De=!1,n=!0):n=!1),n||wl(e)),Pe(e),n=e.type,u=e.pendingProps,i=t!==null?t.memoizedProps:null,a=u.children,sf(n,u)?a=null:i!==null&&sf(n,i)&&(e.flags|=32),e.memoizedState!==null&&(n=fc(t,e,Iv,null,null,l),Cn._currentValue=n),Ru(t,e),Lt(t,e,a,l),e.child;case 6:return t===null&&vt&&((t=l=Ut)&&(l=Hh(l,e.pendingProps,De),l!==null?(e.stateNode=l,It=e,Ut=null,t=!0):t=!1),t||wl(e)),null;case 13:return sr(t,e,l);case 4:return At(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Sa(e,null,a,l):Lt(t,e,a,l),e.child;case 11:return lr(t,e,e.type,e.pendingProps,l);case 7:return Lt(t,e,e.pendingProps,l),e.child;case 8:return Lt(t,e,e.pendingProps.children,l),e.child;case 12:return Lt(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,ul(e,e.type,a.value),Lt(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,Bl(e),n=Wt(n),a=a(n),e.flags|=1,Lt(t,e,a,l),e.child;case 14:return ar(t,e,e.type,e.pendingProps,l);case 15:return nr(t,e,e.type,e.pendingProps,l);case 19:return dr(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=Nu(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=Ge(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return ur(t,e,l);case 24:return Bl(e),a=Wt(Gt),t===null?(n=Pi(),n===null&&(n=zt,u=Fi(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),e.memoizedState={parent:a,cache:n},ec(e),ul(e,Gt,n)):((t.lanes&l)!==0&&(lc(t,e),rn(e,null,null,l),sn()),n=t.memoizedState,u=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),ul(e,Gt,a)):(a=u.cache,ul(e,Gt,a),a!==n.cache&&Wi(e,[Gt],l,!0))),Lt(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function Ke(t){t.flags|=4}function vr(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Td(e)){if(e=Ae.current,e!==null&&((rt&4194048)===rt?Ue!==null:(rt&62914560)!==rt&&(rt&536870912)===0||e!==Ue))throw fn=tc,Fo;t.flags|=8192}}function Du(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Lf():536870912,t.lanes|=e,Ea|=e)}function bn(t,e){if(!vt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Nt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function ch(t,e,l){var a=e.pendingProps;switch(Ki(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Nt(e),null;case 1:return Nt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Qe(Gt),ne(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(tn(e)?Ke(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Ko())),Nt(e),null;case 26:return l=e.memoizedState,t===null?(Ke(e),l!==null?(Nt(e),vr(e,l)):(Nt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(Ke(e),Nt(e),vr(e,l)):(Nt(e),e.flags&=-16777217):(t.memoizedProps!==a&&Ke(e),Nt(e),e.flags&=-16777217),null;case 27:tl(e),l=W.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&Ke(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Nt(e),null}t=X.current,tn(e)?Zo(e):(t=vd(n,a,l),e.stateNode=t,Ke(e))}return Nt(e),null;case 5:if(tl(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&Ke(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Nt(e),null}if(t=X.current,tn(e))Zo(e);else{switch(n=Zu(W.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[$t]=e,t[Pt]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(kt(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Ke(e)}}return Nt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&Ke(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(o(166));if(t=W.current,tn(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=It,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[$t]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||cd(t.nodeValue,l)),t||wl(e)}else t=Zu(t).createTextNode(a),t[$t]=e,e.stateNode=t}return Nt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=tn(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(o(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(o(317));n[$t]=e}else en(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Nt(e),n=!1}else n=Ko(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(Ze(e),e):(Ze(e),null)}if(Ze(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),Du(e,e.updateQueue),Nt(e),null;case 4:return ne(),t===null&&nf(e.stateNode.containerInfo),Nt(e),null;case 10:return Qe(e.type),Nt(e),null;case 19:if(G(Bt),n=e.memoizedState,n===null)return Nt(e),null;if(a=(e.flags&128)!==0,u=n.rendering,u===null)if(a)bn(n,!1);else{if(Ct!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=_u(t),u!==null){for(e.flags|=128,bn(n,!1),t=u.updateQueue,e.updateQueue=t,Du(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Qo(l,t),l=l.sibling;return H(Bt,Bt.current&1|2),e.child}t=t.sibling}n.tail!==null&&Ne()>ju&&(e.flags|=128,a=!0,bn(n,!1),e.lanes=4194304)}else{if(!a)if(t=_u(u),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,Du(e,t),bn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!vt)return Nt(e),null}else 2*Ne()-n.renderingStartTime>ju&&l!==536870912&&(e.flags|=128,a=!0,bn(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Ne(),e.sibling=null,t=Bt.current,H(Bt,a?t&1|2:t&1),e):(Nt(e),null);case 22:case 23:return Ze(e),ic(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Nt(e),e.subtreeFlags&6&&(e.flags|=8192)):Nt(e),l=e.updateQueue,l!==null&&Du(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&G(Yl),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Qe(Gt),Nt(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function fh(t,e){switch(Ki(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Qe(Gt),ne(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return tl(e),null;case 13:if(Ze(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));en()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return G(Bt),null;case 4:return ne(),null;case 10:return Qe(e.type),null;case 22:case 23:return Ze(e),ic(),t!==null&&G(Yl),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Qe(Gt),null;case 25:return null;default:return null}}function hr(t,e){switch(Ki(e),e.tag){case 3:Qe(Gt),ne();break;case 26:case 27:case 5:tl(e);break;case 4:ne();break;case 13:Ze(e);break;case 19:G(Bt);break;case 10:Qe(e.type);break;case 22:case 23:Ze(e),ic(),t!==null&&G(Yl);break;case 24:Qe(Gt)}}function pn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var u=l.create,i=l.inst;a=u(),i.destroy=a}l=l.next}while(l!==n)}}catch(f){Et(e,e.return,f)}}function dl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){var i=a.inst,f=i.destroy;if(f!==void 0){i.destroy=void 0,n=e;var r=l,p=f;try{p()}catch(M){Et(n,r,M)}}}a=a.next}while(a!==u)}}catch(M){Et(e,e.return,M)}}function yr(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{as(e,l)}catch(a){Et(t,t.return,a)}}}function gr(t,e,l){l.props=Ql(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){Et(t,e,a)}}function Sn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){Et(t,e,n)}}function Ce(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Et(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Et(t,e,n)}else l.current=null}function br(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Et(t,t.return,n)}}function jc(t,e,l){try{var a=t.stateNode;Rh(a,t.type,l,e),a[Pt]=e}catch(n){Et(t,t.return,n)}}function pr(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Sl(t.type)||t.tag===4}function Hc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||pr(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Sl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function qc(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=Vu));else if(a!==4&&(a===27&&Sl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(qc(t,e,l),t=t.sibling;t!==null;)qc(t,e,l),t=t.sibling}function Uu(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&Sl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(Uu(t,e,l),t=t.sibling;t!==null;)Uu(t,e,l),t=t.sibling}function Sr(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);kt(e,a,l),e[$t]=t,e[Pt]=l}catch(u){Et(t,t.return,u)}}var ke=!1,Ht=!1,wc=!1,xr=typeof WeakSet=="function"?WeakSet:Set,Vt=null;function oh(t,e){if(t=t.containerInfo,ff=Wu,t=Uo(t),Hi(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var i=0,f=-1,r=-1,p=0,M=0,N=t,S=null;e:for(;;){for(var T;N!==l||n!==0&&N.nodeType!==3||(f=i+n),N!==u||a!==0&&N.nodeType!==3||(r=i+a),N.nodeType===3&&(i+=N.nodeValue.length),(T=N.firstChild)!==null;)S=N,N=T;for(;;){if(N===t)break e;if(S===l&&++p===n&&(f=i),S===u&&++M===a&&(r=i),(T=N.nextSibling)!==null)break;N=S,S=N.parentNode}N=T}l=f===-1||r===-1?null:{start:f,end:r}}else l=null}l=l||{start:0,end:0}}else l=null;for(of={focusedElem:t,selectionRange:l},Wu=!1,Vt=e;Vt!==null;)if(e=Vt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Vt=t;else for(;Vt!==null;){switch(e=Vt,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var $=Ql(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate($,u),a.__reactInternalSnapshotBeforeUpdate=t}catch(k){Et(l,l.return,k)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)df(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":df(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,Vt=t;break}Vt=e.return}}function Tr(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:ml(t,l),a&4&&pn(5,l);break;case 1:if(ml(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(i){Et(l,l.return,i)}else{var n=Ql(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(i){Et(l,l.return,i)}}a&64&&yr(l),a&512&&Sn(l,l.return);break;case 3:if(ml(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{as(t,e)}catch(i){Et(l,l.return,i)}}break;case 27:e===null&&a&4&&Sr(l);case 26:case 5:ml(t,l),e===null&&a&4&&br(l),a&512&&Sn(l,l.return);break;case 12:ml(t,l);break;case 13:ml(t,l),a&4&&zr(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=bh.bind(null,l),qh(t,l))));break;case 22:if(a=l.memoizedState!==null||ke,!a){e=e!==null&&e.memoizedState!==null||Ht,n=ke;var u=Ht;ke=a,(Ht=e)&&!u?vl(t,l,(l.subtreeFlags&8772)!==0):ml(t,l),ke=n,Ht=u}break;case 30:break;default:ml(t,l)}}function Ar(t){var e=t.alternate;e!==null&&(t.alternate=null,Ar(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&yi(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ot=null,le=!1;function Je(t,e,l){for(l=l.child;l!==null;)Er(t,e,l),l=l.sibling}function Er(t,e,l){if(ue&&typeof ue.onCommitFiberUnmount=="function")try{ue.onCommitFiberUnmount(Ya,l)}catch{}switch(l.tag){case 26:Ht||Ce(l,e),Je(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Ht||Ce(l,e);var a=Ot,n=le;Sl(l.type)&&(Ot=l.stateNode,le=!1),Je(t,e,l),Rn(l.stateNode),Ot=a,le=n;break;case 5:Ht||Ce(l,e);case 6:if(a=Ot,n=le,Ot=null,Je(t,e,l),Ot=a,le=n,Ot!==null)if(le)try{(Ot.nodeType===9?Ot.body:Ot.nodeName==="HTML"?Ot.ownerDocument.body:Ot).removeChild(l.stateNode)}catch(u){Et(l,e,u)}else try{Ot.removeChild(l.stateNode)}catch(u){Et(l,e,u)}break;case 18:Ot!==null&&(le?(t=Ot,dd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),wn(t)):dd(Ot,l.stateNode));break;case 4:a=Ot,n=le,Ot=l.stateNode.containerInfo,le=!0,Je(t,e,l),Ot=a,le=n;break;case 0:case 11:case 14:case 15:Ht||dl(2,l,e),Ht||dl(4,l,e),Je(t,e,l);break;case 1:Ht||(Ce(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&gr(l,e,a)),Je(t,e,l);break;case 21:Je(t,e,l);break;case 22:Ht=(a=Ht)||l.memoizedState!==null,Je(t,e,l),Ht=a;break;default:Je(t,e,l)}}function zr(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{wn(t)}catch(l){Et(e,e.return,l)}}function sh(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new xr),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new xr),e;default:throw Error(o(435,t.tag))}}function Gc(t,e){var l=sh(t);e.forEach(function(a){var n=ph.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function oe(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=t,i=e,f=i;t:for(;f!==null;){switch(f.tag){case 27:if(Sl(f.type)){Ot=f.stateNode,le=!1;break t}break;case 5:Ot=f.stateNode,le=!1;break t;case 3:case 4:Ot=f.stateNode.containerInfo,le=!0;break t}f=f.return}if(Ot===null)throw Error(o(160));Er(u,i,n),Ot=null,le=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)_r(e,t),e=e.sibling}var Me=null;function _r(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:oe(e,t),se(t),a&4&&(dl(3,t,t.return),pn(3,t),dl(5,t,t.return));break;case 1:oe(e,t),se(t),a&512&&(Ht||l===null||Ce(l,l.return)),a&64&&ke&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Me;if(oe(e,t),se(t),a&512&&(Ht||l===null||Ce(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Va]||u[$t]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),kt(u,a,l),u[$t]=t,Xt(u),a=u;break t;case"link":var i=Sd("link","href",n).get(a+(l.href||""));if(i){for(var f=0;f<i.length;f++)if(u=i[f],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){i.splice(f,1);break e}}u=n.createElement(a),kt(u,a,l),n.head.appendChild(u);break;case"meta":if(i=Sd("meta","content",n).get(a+(l.content||""))){for(f=0;f<i.length;f++)if(u=i[f],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){i.splice(f,1);break e}}u=n.createElement(a),kt(u,a,l),n.head.appendChild(u);break;default:throw Error(o(468,a))}u[$t]=t,Xt(u),a=u}t.stateNode=a}else xd(n,t.type,t.stateNode);else t.stateNode=pd(n,a,t.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?xd(n,t.type,t.stateNode):pd(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&jc(t,t.memoizedProps,l.memoizedProps)}break;case 27:oe(e,t),se(t),a&512&&(Ht||l===null||Ce(l,l.return)),l!==null&&a&4&&jc(t,t.memoizedProps,l.memoizedProps);break;case 5:if(oe(e,t),se(t),a&512&&(Ht||l===null||Ce(l,l.return)),t.flags&32){n=t.stateNode;try{la(n,"")}catch(T){Et(t,t.return,T)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,jc(t,n,l!==null?l.memoizedProps:n)),a&1024&&(wc=!0);break;case 6:if(oe(e,t),se(t),a&4){if(t.stateNode===null)throw Error(o(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(T){Et(t,t.return,T)}}break;case 3:if(ku=null,n=Me,Me=Lu(e.containerInfo),oe(e,t),Me=n,se(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{wn(e.containerInfo)}catch(T){Et(t,t.return,T)}wc&&(wc=!1,Mr(t));break;case 4:a=Me,Me=Lu(t.stateNode.containerInfo),oe(e,t),se(t),Me=a;break;case 12:oe(e,t),se(t);break;case 13:oe(e,t),se(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Zc=Ne()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Gc(t,a)));break;case 22:n=t.memoizedState!==null;var r=l!==null&&l.memoizedState!==null,p=ke,M=Ht;if(ke=p||n,Ht=M||r,oe(e,t),Ht=M,ke=p,se(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||r||ke||Ht||Vl(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){r=l=e;try{if(u=r.stateNode,n)i=u.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{f=r.stateNode;var N=r.memoizedProps.style,S=N!=null&&N.hasOwnProperty("display")?N.display:null;f.style.display=S==null||typeof S=="boolean"?"":(""+S).trim()}}catch(T){Et(r,r.return,T)}}}else if(e.tag===6){if(l===null){r=e;try{r.stateNode.nodeValue=n?"":r.memoizedProps}catch(T){Et(r,r.return,T)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Gc(t,l))));break;case 19:oe(e,t),se(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Gc(t,a)));break;case 30:break;case 21:break;default:oe(e,t),se(t)}}function se(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(pr(a)){l=a;break}a=a.return}if(l==null)throw Error(o(160));switch(l.tag){case 27:var n=l.stateNode,u=Hc(t);Uu(t,u,n);break;case 5:var i=l.stateNode;l.flags&32&&(la(i,""),l.flags&=-33);var f=Hc(t);Uu(t,f,i);break;case 3:case 4:var r=l.stateNode.containerInfo,p=Hc(t);qc(t,p,r);break;default:throw Error(o(161))}}catch(M){Et(t,t.return,M)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Mr(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Mr(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ml(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Tr(t,e.alternate,e),e=e.sibling}function Vl(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:dl(4,e,e.return),Vl(e);break;case 1:Ce(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&gr(e,e.return,l),Vl(e);break;case 27:Rn(e.stateNode);case 26:case 5:Ce(e,e.return),Vl(e);break;case 22:e.memoizedState===null&&Vl(e);break;case 30:Vl(e);break;default:Vl(e)}t=t.sibling}}function vl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,u=e,i=u.flags;switch(u.tag){case 0:case 11:case 15:vl(n,u,l),pn(4,u);break;case 1:if(vl(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(p){Et(a,a.return,p)}if(a=u,n=a.updateQueue,n!==null){var f=a.stateNode;try{var r=n.shared.hiddenCallbacks;if(r!==null)for(n.shared.hiddenCallbacks=null,n=0;n<r.length;n++)ls(r[n],f)}catch(p){Et(a,a.return,p)}}l&&i&64&&yr(u),Sn(u,u.return);break;case 27:Sr(u);case 26:case 5:vl(n,u,l),l&&a===null&&i&4&&br(u),Sn(u,u.return);break;case 12:vl(n,u,l);break;case 13:vl(n,u,l),l&&i&4&&zr(n,u);break;case 22:u.memoizedState===null&&vl(n,u,l),Sn(u,u.return);break;case 30:break;default:vl(n,u,l)}e=e.sibling}}function Bc(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&nn(l))}function Yc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&nn(t))}function je(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Or(t,e,l,a),e=e.sibling}function Or(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:je(t,e,l,a),n&2048&&pn(9,e);break;case 1:je(t,e,l,a);break;case 3:je(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&nn(t)));break;case 12:if(n&2048){je(t,e,l,a),t=e.stateNode;try{var u=e.memoizedProps,i=u.id,f=u.onPostCommit;typeof f=="function"&&f(i,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(r){Et(e,e.return,r)}}else je(t,e,l,a);break;case 13:je(t,e,l,a);break;case 23:break;case 22:u=e.stateNode,i=e.alternate,e.memoizedState!==null?u._visibility&2?je(t,e,l,a):xn(t,e):u._visibility&2?je(t,e,l,a):(u._visibility|=2,xa(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Bc(i,e);break;case 24:je(t,e,l,a),n&2048&&Yc(e.alternate,e);break;default:je(t,e,l,a)}}function xa(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,i=e,f=l,r=a,p=i.flags;switch(i.tag){case 0:case 11:case 15:xa(u,i,f,r,n),pn(8,i);break;case 23:break;case 22:var M=i.stateNode;i.memoizedState!==null?M._visibility&2?xa(u,i,f,r,n):xn(u,i):(M._visibility|=2,xa(u,i,f,r,n)),n&&p&2048&&Bc(i.alternate,i);break;case 24:xa(u,i,f,r,n),n&&p&2048&&Yc(i.alternate,i);break;default:xa(u,i,f,r,n)}e=e.sibling}}function xn(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:xn(l,a),n&2048&&Bc(a.alternate,a);break;case 24:xn(l,a),n&2048&&Yc(a.alternate,a);break;default:xn(l,a)}e=e.sibling}}var Tn=8192;function Ta(t){if(t.subtreeFlags&Tn)for(t=t.child;t!==null;)Rr(t),t=t.sibling}function Rr(t){switch(t.tag){case 26:Ta(t),t.flags&Tn&&t.memoizedState!==null&&$h(Me,t.memoizedState,t.memoizedProps);break;case 5:Ta(t);break;case 3:case 4:var e=Me;Me=Lu(t.stateNode.containerInfo),Ta(t),Me=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Tn,Tn=16777216,Ta(t),Tn=e):Ta(t));break;default:Ta(t)}}function Nr(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function An(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Vt=a,Ur(a,t)}Nr(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Dr(t),t=t.sibling}function Dr(t){switch(t.tag){case 0:case 11:case 15:An(t),t.flags&2048&&dl(9,t,t.return);break;case 3:An(t);break;case 12:An(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Cu(t)):An(t);break;default:An(t)}}function Cu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Vt=a,Ur(a,t)}Nr(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:dl(8,e,e.return),Cu(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,Cu(e));break;default:Cu(e)}t=t.sibling}}function Ur(t,e){for(;Vt!==null;){var l=Vt;switch(l.tag){case 0:case 11:case 15:dl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:nn(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Vt=a;else t:for(l=t;Vt!==null;){a=Vt;var n=a.sibling,u=a.return;if(Ar(a),a===l){Vt=null;break t}if(n!==null){n.return=u,Vt=n;break t}Vt=u}}}var rh={getCacheForType:function(t){var e=Wt(Gt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},dh=typeof WeakMap=="function"?WeakMap:Map,yt=0,zt=null,ct=null,rt=0,gt=0,re=null,hl=!1,Aa=!1,Xc=!1,$e=0,Ct=0,yl=0,Zl=0,Qc=0,Ee=0,Ea=0,En=null,ae=null,Vc=!1,Zc=0,ju=1/0,Hu=null,gl=null,Kt=0,bl=null,za=null,_a=0,Lc=0,Kc=null,Cr=null,zn=0,kc=null;function de(){if((yt&2)!==0&&rt!==0)return rt&-rt;if(E.T!==null){var t=ma;return t!==0?t:tf()}return Jf()}function jr(){Ee===0&&(Ee=(rt&536870912)===0||vt?Zf():536870912);var t=Ae.current;return t!==null&&(t.flags|=32),Ee}function me(t,e,l){(t===zt&&(gt===2||gt===9)||t.cancelPendingCommit!==null)&&(Ma(t,0),pl(t,rt,Ee,!1)),Qa(t,l),((yt&2)===0||t!==zt)&&(t===zt&&((yt&2)===0&&(Zl|=l),Ct===4&&pl(t,rt,Ee,!1)),He(t))}function Hr(t,e,l){if((yt&6)!==0)throw Error(o(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||Xa(t,e),n=a?hh(t,e):Wc(t,e,!0),u=a;do{if(n===0){Aa&&!a&&pl(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!mh(l)){n=Wc(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var i=0;else i=t.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){e=i;t:{var f=t;n=En;var r=f.current.memoizedState.isDehydrated;if(r&&(Ma(f,i).flags|=256),i=Wc(f,i,!1),i!==2){if(Xc&&!r){f.errorRecoveryDisabledLanes|=u,Zl|=u,n=4;break t}u=ae,ae=n,u!==null&&(ae===null?ae=u:ae.push.apply(ae,u))}n=i}if(u=!1,n!==2)continue}}if(n===1){Ma(t,0),pl(t,e,0,!0);break}t:{switch(a=t,u=n,u){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:pl(a,e,Ee,!hl);break t;case 2:ae=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(n=Zc+300-Ne(),10<n)){if(pl(a,e,Ee,!hl),Kn(a,0,!0)!==0)break t;a.timeoutHandle=sd(qr.bind(null,a,l,ae,Hu,Vc,e,Ee,Zl,Ea,hl,u,2,-0,0),n);break t}qr(a,l,ae,Hu,Vc,e,Ee,Zl,Ea,hl,u,0,-0,0)}}break}while(!0);He(t)}function qr(t,e,l,a,n,u,i,f,r,p,M,N,S,T){if(t.timeoutHandle=-1,N=e.subtreeFlags,(N&8192||(N&16785408)===16785408)&&(Un={stylesheets:null,count:0,unsuspend:Jh},Rr(e),N=Wh(),N!==null)){t.cancelPendingCommit=N(Vr.bind(null,t,e,u,l,a,n,i,f,r,M,1,S,T)),pl(t,u,i,!p);return}Vr(t,e,u,l,a,n,i,f,r)}function mh(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!ce(u(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function pl(t,e,l,a){e&=~Qc,e&=~Zl,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var u=31-ie(n),i=1<<u;a[u]=-1,n&=~i}l!==0&&Kf(t,l,e)}function qu(){return(yt&6)===0?(_n(0),!1):!0}function Jc(){if(ct!==null){if(gt===0)var t=ct.return;else t=ct,Xe=Gl=null,rc(t),pa=null,yn=0,t=ct;for(;t!==null;)hr(t.alternate,t),t=t.return;ct=null}}function Ma(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Dh(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Jc(),zt=t,ct=l=Ge(t.current,null),rt=e,gt=0,re=null,hl=!1,Aa=Xa(t,e),Xc=!1,Ea=Ee=Qc=Zl=yl=Ct=0,ae=En=null,Vc=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-ie(a),u=1<<n;e|=t[n],a&=~u}return $e=e,nu(),l}function wr(t,e){at=null,E.H=Au,e===cn||e===mu?(e=ts(),gt=3):e===Fo?(e=ts(),gt=4):gt=e===er?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,re=e,ct===null&&(Ct=1,Ou(t,pe(e,t.current)))}function Gr(){var t=E.H;return E.H=Au,t===null?Au:t}function Br(){var t=E.A;return E.A=rh,t}function $c(){Ct=4,hl||(rt&4194048)!==rt&&Ae.current!==null||(Aa=!0),(yl&134217727)===0&&(Zl&134217727)===0||zt===null||pl(zt,rt,Ee,!1)}function Wc(t,e,l){var a=yt;yt|=2;var n=Gr(),u=Br();(zt!==t||rt!==e)&&(Hu=null,Ma(t,e)),e=!1;var i=Ct;t:do try{if(gt!==0&&ct!==null){var f=ct,r=re;switch(gt){case 8:Jc(),i=6;break t;case 3:case 2:case 9:case 6:Ae.current===null&&(e=!0);var p=gt;if(gt=0,re=null,Oa(t,f,r,p),l&&Aa){i=0;break t}break;default:p=gt,gt=0,re=null,Oa(t,f,r,p)}}vh(),i=Ct;break}catch(M){wr(t,M)}while(!0);return e&&t.shellSuspendCounter++,Xe=Gl=null,yt=a,E.H=n,E.A=u,ct===null&&(zt=null,rt=0,nu()),i}function vh(){for(;ct!==null;)Yr(ct)}function hh(t,e){var l=yt;yt|=2;var a=Gr(),n=Br();zt!==t||rt!==e?(Hu=null,ju=Ne()+500,Ma(t,e)):Aa=Xa(t,e);t:do try{if(gt!==0&&ct!==null){e=ct;var u=re;e:switch(gt){case 1:gt=0,re=null,Oa(t,e,u,1);break;case 2:case 9:if(Io(u)){gt=0,re=null,Xr(e);break}e=function(){gt!==2&&gt!==9||zt!==t||(gt=7),He(t)},u.then(e,e);break t;case 3:gt=7;break t;case 4:gt=5;break t;case 7:Io(u)?(gt=0,re=null,Xr(e)):(gt=0,re=null,Oa(t,e,u,7));break;case 5:var i=null;switch(ct.tag){case 26:i=ct.memoizedState;case 5:case 27:var f=ct;if(!i||Td(i)){gt=0,re=null;var r=f.sibling;if(r!==null)ct=r;else{var p=f.return;p!==null?(ct=p,wu(p)):ct=null}break e}}gt=0,re=null,Oa(t,e,u,5);break;case 6:gt=0,re=null,Oa(t,e,u,6);break;case 8:Jc(),Ct=6;break t;default:throw Error(o(462))}}yh();break}catch(M){wr(t,M)}while(!0);return Xe=Gl=null,E.H=a,E.A=n,yt=l,ct!==null?0:(zt=null,rt=0,nu(),Ct)}function yh(){for(;ct!==null&&!Gm();)Yr(ct)}function Yr(t){var e=mr(t.alternate,t,$e);t.memoizedProps=t.pendingProps,e===null?wu(t):ct=e}function Xr(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=cr(l,e,e.pendingProps,e.type,void 0,rt);break;case 11:e=cr(l,e,e.pendingProps,e.type.render,e.ref,rt);break;case 5:rc(e);default:hr(l,e),e=ct=Qo(e,$e),e=mr(l,e,$e)}t.memoizedProps=t.pendingProps,e===null?wu(t):ct=e}function Oa(t,e,l,a){Xe=Gl=null,rc(e),pa=null,yn=0;var n=e.return;try{if(uh(t,n,e,l,rt)){Ct=1,Ou(t,pe(l,t.current)),ct=null;return}}catch(u){if(n!==null)throw ct=n,u;Ct=1,Ou(t,pe(l,t.current)),ct=null;return}e.flags&32768?(vt||a===1?t=!0:Aa||(rt&536870912)!==0?t=!1:(hl=t=!0,(a===2||a===9||a===3||a===6)&&(a=Ae.current,a!==null&&a.tag===13&&(a.flags|=16384))),Qr(e,t)):wu(e)}function wu(t){var e=t;do{if((e.flags&32768)!==0){Qr(e,hl);return}t=e.return;var l=ch(e.alternate,e,$e);if(l!==null){ct=l;return}if(e=e.sibling,e!==null){ct=e;return}ct=e=t}while(e!==null);Ct===0&&(Ct=5)}function Qr(t,e){do{var l=fh(t.alternate,t);if(l!==null){l.flags&=32767,ct=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){ct=t;return}ct=t=l}while(t!==null);Ct=6,ct=null}function Vr(t,e,l,a,n,u,i,f,r){t.cancelPendingCommit=null;do Gu();while(Kt!==0);if((yt&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(u=e.lanes|e.childLanes,u|=Yi,Jm(t,l,u,i,f,r),t===zt&&(ct=zt=null,rt=0),za=e,bl=t,_a=l,Lc=u,Kc=n,Cr=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Sh(Vn,function(){return Jr(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=E.T,E.T=null,n=q.p,q.p=2,i=yt,yt|=4;try{oh(t,e,l)}finally{yt=i,q.p=n,E.T=a}}Kt=1,Zr(),Lr(),Kr()}}function Zr(){if(Kt===1){Kt=0;var t=bl,e=za,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=E.T,E.T=null;var a=q.p;q.p=2;var n=yt;yt|=4;try{_r(e,t);var u=of,i=Uo(t.containerInfo),f=u.focusedElem,r=u.selectionRange;if(i!==f&&f&&f.ownerDocument&&Do(f.ownerDocument.documentElement,f)){if(r!==null&&Hi(f)){var p=r.start,M=r.end;if(M===void 0&&(M=p),"selectionStart"in f)f.selectionStart=p,f.selectionEnd=Math.min(M,f.value.length);else{var N=f.ownerDocument||document,S=N&&N.defaultView||window;if(S.getSelection){var T=S.getSelection(),$=f.textContent.length,k=Math.min(r.start,$),xt=r.end===void 0?k:Math.min(r.end,$);!T.extend&&k>xt&&(i=xt,xt=k,k=i);var h=No(f,k),v=No(f,xt);if(h&&v&&(T.rangeCount!==1||T.anchorNode!==h.node||T.anchorOffset!==h.offset||T.focusNode!==v.node||T.focusOffset!==v.offset)){var b=N.createRange();b.setStart(h.node,h.offset),T.removeAllRanges(),k>xt?(T.addRange(b),T.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),T.addRange(b))}}}}for(N=[],T=f;T=T.parentNode;)T.nodeType===1&&N.push({element:T,left:T.scrollLeft,top:T.scrollTop});for(typeof f.focus=="function"&&f.focus(),f=0;f<N.length;f++){var O=N[f];O.element.scrollLeft=O.left,O.element.scrollTop=O.top}}Wu=!!ff,of=ff=null}finally{yt=n,q.p=a,E.T=l}}t.current=e,Kt=2}}function Lr(){if(Kt===2){Kt=0;var t=bl,e=za,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=E.T,E.T=null;var a=q.p;q.p=2;var n=yt;yt|=4;try{Tr(t,e.alternate,e)}finally{yt=n,q.p=a,E.T=l}}Kt=3}}function Kr(){if(Kt===4||Kt===3){Kt=0,Bm();var t=bl,e=za,l=_a,a=Cr;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Kt=5:(Kt=0,za=bl=null,kr(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(gl=null),vi(l),e=e.stateNode,ue&&typeof ue.onCommitFiberRoot=="function")try{ue.onCommitFiberRoot(Ya,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=E.T,n=q.p,q.p=2,E.T=null;try{for(var u=t.onRecoverableError,i=0;i<a.length;i++){var f=a[i];u(f.value,{componentStack:f.stack})}}finally{E.T=e,q.p=n}}(_a&3)!==0&&Gu(),He(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===kc?zn++:(zn=0,kc=t):zn=0,_n(0)}}function kr(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,nn(e)))}function Gu(t){return Zr(),Lr(),Kr(),Jr()}function Jr(){if(Kt!==5)return!1;var t=bl,e=Lc;Lc=0;var l=vi(_a),a=E.T,n=q.p;try{q.p=32>l?32:l,E.T=null,l=Kc,Kc=null;var u=bl,i=_a;if(Kt=0,za=bl=null,_a=0,(yt&6)!==0)throw Error(o(331));var f=yt;if(yt|=4,Dr(u.current),Or(u,u.current,i,l),yt=f,_n(0,!1),ue&&typeof ue.onPostCommitFiberRoot=="function")try{ue.onPostCommitFiberRoot(Ya,u)}catch{}return!0}finally{q.p=n,E.T=a,kr(t,e)}}function $r(t,e,l){e=pe(l,e),e=zc(t.stateNode,e,2),t=fl(t,e,2),t!==null&&(Qa(t,2),He(t))}function Et(t,e,l){if(t.tag===3)$r(t,t,l);else for(;e!==null;){if(e.tag===3){$r(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(gl===null||!gl.has(a))){t=pe(l,t),l=Ps(2),a=fl(e,l,2),a!==null&&(tr(l,a,e,t),Qa(a,2),He(a));break}}e=e.return}}function Fc(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new dh;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Xc=!0,n.add(l),t=gh.bind(null,t,e,l),e.then(t,t))}function gh(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,zt===t&&(rt&l)===l&&(Ct===4||Ct===3&&(rt&62914560)===rt&&300>Ne()-Zc?(yt&2)===0&&Ma(t,0):Qc|=l,Ea===rt&&(Ea=0)),He(t)}function Wr(t,e){e===0&&(e=Lf()),t=oa(t,e),t!==null&&(Qa(t,e),He(t))}function bh(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),Wr(t,l)}function ph(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(e),Wr(t,l)}function Sh(t,e){return si(t,e)}var Bu=null,Ra=null,Ic=!1,Yu=!1,Pc=!1,Ll=0;function He(t){t!==Ra&&t.next===null&&(Ra===null?Bu=Ra=t:Ra=Ra.next=t),Yu=!0,Ic||(Ic=!0,Th())}function _n(t,e){if(!Pc&&Yu){Pc=!0;do for(var l=!1,a=Bu;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var i=a.suspendedLanes,f=a.pingedLanes;u=(1<<31-ie(42|t)+1)-1,u&=n&~(i&~f),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,td(a,u))}else u=rt,u=Kn(a,a===zt?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||Xa(a,u)||(l=!0,td(a,u));a=a.next}while(l);Pc=!1}}function xh(){Fr()}function Fr(){Yu=Ic=!1;var t=0;Ll!==0&&(Nh()&&(t=Ll),Ll=0);for(var e=Ne(),l=null,a=Bu;a!==null;){var n=a.next,u=Ir(a,e);u===0?(a.next=null,l===null?Bu=n:l.next=n,n===null&&(Ra=l)):(l=a,(t!==0||(u&3)!==0)&&(Yu=!0)),a=n}_n(t)}function Ir(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var i=31-ie(u),f=1<<i,r=n[i];r===-1?((f&l)===0||(f&a)!==0)&&(n[i]=km(f,e)):r<=e&&(t.expiredLanes|=f),u&=~f}if(e=zt,l=rt,l=Kn(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(gt===2||gt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&ri(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||Xa(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&ri(a),vi(l)){case 2:case 8:l=Qf;break;case 32:l=Vn;break;case 268435456:l=Vf;break;default:l=Vn}return a=Pr.bind(null,t),l=si(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&ri(a),t.callbackPriority=2,t.callbackNode=null,2}function Pr(t,e){if(Kt!==0&&Kt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Gu()&&t.callbackNode!==l)return null;var a=rt;return a=Kn(t,t===zt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Hr(t,a,e),Ir(t,Ne()),t.callbackNode!=null&&t.callbackNode===l?Pr.bind(null,t):null)}function td(t,e){if(Gu())return null;Hr(t,e,!0)}function Th(){Uh(function(){(yt&6)!==0?si(Xf,xh):Fr()})}function tf(){return Ll===0&&(Ll=Zf()),Ll}function ed(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Fn(""+t)}function ld(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Ah(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var u=ed((n[Pt]||null).action),i=a.submitter;i&&(e=(e=i[Pt]||null)?ed(e.formAction):i.getAttribute("formAction"),e!==null&&(u=e,i=null));var f=new eu("action","action",null,a,n);t.push({event:f,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Ll!==0){var r=i?ld(n,i):new FormData(n);Sc(l,{pending:!0,data:r,method:n.method,action:u},null,r)}}else typeof u=="function"&&(f.preventDefault(),r=i?ld(n,i):new FormData(n),Sc(l,{pending:!0,data:r,method:n.method,action:u},u,r))},currentTarget:n}]})}}for(var ef=0;ef<Bi.length;ef++){var lf=Bi[ef],Eh=lf.toLowerCase(),zh=lf[0].toUpperCase()+lf.slice(1);_e(Eh,"on"+zh)}_e(Ho,"onAnimationEnd"),_e(qo,"onAnimationIteration"),_e(wo,"onAnimationStart"),_e("dblclick","onDoubleClick"),_e("focusin","onFocus"),_e("focusout","onBlur"),_e(Qv,"onTransitionRun"),_e(Vv,"onTransitionStart"),_e(Zv,"onTransitionCancel"),_e(Go,"onTransitionEnd"),Pl("onMouseEnter",["mouseout","mouseover"]),Pl("onMouseLeave",["mouseout","mouseover"]),Pl("onPointerEnter",["pointerout","pointerover"]),Pl("onPointerLeave",["pointerout","pointerover"]),Rl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Rl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Rl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Rl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Rl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Rl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),_h=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Mn));function ad(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var u=void 0;if(e)for(var i=a.length-1;0<=i;i--){var f=a[i],r=f.instance,p=f.currentTarget;if(f=f.listener,r!==u&&n.isPropagationStopped())break t;u=f,n.currentTarget=p;try{u(n)}catch(M){Mu(M)}n.currentTarget=null,u=r}else for(i=0;i<a.length;i++){if(f=a[i],r=f.instance,p=f.currentTarget,f=f.listener,r!==u&&n.isPropagationStopped())break t;u=f,n.currentTarget=p;try{u(n)}catch(M){Mu(M)}n.currentTarget=null,u=r}}}}function ft(t,e){var l=e[hi];l===void 0&&(l=e[hi]=new Set);var a=t+"__bubble";l.has(a)||(nd(e,t,2,!1),l.add(a))}function af(t,e,l){var a=0;e&&(a|=4),nd(l,t,a,e)}var Xu="_reactListening"+Math.random().toString(36).slice(2);function nf(t){if(!t[Xu]){t[Xu]=!0,Wf.forEach(function(l){l!=="selectionchange"&&(_h.has(l)||af(l,!1,t),af(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Xu]||(e[Xu]=!0,af("selectionchange",!1,e))}}function nd(t,e,l,a){switch(Od(e)){case 2:var n=Ph;break;case 8:n=ty;break;default:n=pf}l=n.bind(null,e,l,t),n=void 0,!_i||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function uf(t,e,l,a,n){var u=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var f=a.stateNode.containerInfo;if(f===n)break;if(i===4)for(i=a.return;i!==null;){var r=i.tag;if((r===3||r===4)&&i.stateNode.containerInfo===n)return;i=i.return}for(;f!==null;){if(i=Wl(f),i===null)return;if(r=i.tag,r===5||r===6||r===26||r===27){a=u=i;continue t}f=f.parentNode}}a=a.return}so(function(){var p=u,M=Ei(l),N=[];t:{var S=Bo.get(t);if(S!==void 0){var T=eu,$=t;switch(t){case"keypress":if(Pn(l)===0)break t;case"keydown":case"keyup":T=Sv;break;case"focusin":$="focus",T=Ni;break;case"focusout":$="blur",T=Ni;break;case"beforeblur":case"afterblur":T=Ni;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":T=vo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":T=fv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":T=Av;break;case Ho:case qo:case wo:T=rv;break;case Go:T=zv;break;case"scroll":case"scrollend":T=iv;break;case"wheel":T=Mv;break;case"copy":case"cut":case"paste":T=mv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":T=yo;break;case"toggle":case"beforetoggle":T=Rv}var k=(e&4)!==0,xt=!k&&(t==="scroll"||t==="scrollend"),h=k?S!==null?S+"Capture":null:S;k=[];for(var v=p,b;v!==null;){var O=v;if(b=O.stateNode,O=O.tag,O!==5&&O!==26&&O!==27||b===null||h===null||(O=La(v,h),O!=null&&k.push(On(v,O,b))),xt)break;v=v.return}0<k.length&&(S=new T(S,$,null,l,M),N.push({event:S,listeners:k}))}}if((e&7)===0){t:{if(S=t==="mouseover"||t==="pointerover",T=t==="mouseout"||t==="pointerout",S&&l!==Ai&&($=l.relatedTarget||l.fromElement)&&(Wl($)||$[$l]))break t;if((T||S)&&(S=M.window===M?M:(S=M.ownerDocument)?S.defaultView||S.parentWindow:window,T?($=l.relatedTarget||l.toElement,T=p,$=$?Wl($):null,$!==null&&(xt=x($),k=$.tag,$!==xt||k!==5&&k!==27&&k!==6)&&($=null)):(T=null,$=p),T!==$)){if(k=vo,O="onMouseLeave",h="onMouseEnter",v="mouse",(t==="pointerout"||t==="pointerover")&&(k=yo,O="onPointerLeave",h="onPointerEnter",v="pointer"),xt=T==null?S:Za(T),b=$==null?S:Za($),S=new k(O,v+"leave",T,l,M),S.target=xt,S.relatedTarget=b,O=null,Wl(M)===p&&(k=new k(h,v+"enter",$,l,M),k.target=b,k.relatedTarget=xt,O=k),xt=O,T&&$)e:{for(k=T,h=$,v=0,b=k;b;b=Na(b))v++;for(b=0,O=h;O;O=Na(O))b++;for(;0<v-b;)k=Na(k),v--;for(;0<b-v;)h=Na(h),b--;for(;v--;){if(k===h||h!==null&&k===h.alternate)break e;k=Na(k),h=Na(h)}k=null}else k=null;T!==null&&ud(N,S,T,k,!1),$!==null&&xt!==null&&ud(N,xt,$,k,!0)}}t:{if(S=p?Za(p):window,T=S.nodeName&&S.nodeName.toLowerCase(),T==="select"||T==="input"&&S.type==="file")var Q=Eo;else if(To(S))if(zo)Q=Bv;else{Q=wv;var ut=qv}else T=S.nodeName,!T||T.toLowerCase()!=="input"||S.type!=="checkbox"&&S.type!=="radio"?p&&Ti(p.elementType)&&(Q=Eo):Q=Gv;if(Q&&(Q=Q(t,p))){Ao(N,Q,l,M);break t}ut&&ut(t,S,p),t==="focusout"&&p&&S.type==="number"&&p.memoizedProps.value!=null&&xi(S,"number",S.value)}switch(ut=p?Za(p):window,t){case"focusin":(To(ut)||ut.contentEditable==="true")&&(ia=ut,qi=p,Pa=null);break;case"focusout":Pa=qi=ia=null;break;case"mousedown":wi=!0;break;case"contextmenu":case"mouseup":case"dragend":wi=!1,Co(N,l,M);break;case"selectionchange":if(Xv)break;case"keydown":case"keyup":Co(N,l,M)}var K;if(Ui)t:{switch(t){case"compositionstart":var J="onCompositionStart";break t;case"compositionend":J="onCompositionEnd";break t;case"compositionupdate":J="onCompositionUpdate";break t}J=void 0}else ua?So(t,l)&&(J="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(J="onCompositionStart");J&&(go&&l.locale!=="ko"&&(ua||J!=="onCompositionStart"?J==="onCompositionEnd"&&ua&&(K=ro()):(nl=M,Mi="value"in nl?nl.value:nl.textContent,ua=!0)),ut=Qu(p,J),0<ut.length&&(J=new ho(J,t,null,l,M),N.push({event:J,listeners:ut}),K?J.data=K:(K=xo(l),K!==null&&(J.data=K)))),(K=Dv?Uv(t,l):Cv(t,l))&&(J=Qu(p,"onBeforeInput"),0<J.length&&(ut=new ho("onBeforeInput","beforeinput",null,l,M),N.push({event:ut,listeners:J}),ut.data=K)),Ah(N,t,p,l,M)}ad(N,e)})}function On(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Qu(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=La(t,l),n!=null&&a.unshift(On(t,n,u)),n=La(t,e),n!=null&&a.push(On(t,n,u))),t.tag===3)return a;t=t.return}return[]}function Na(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function ud(t,e,l,a,n){for(var u=e._reactName,i=[];l!==null&&l!==a;){var f=l,r=f.alternate,p=f.stateNode;if(f=f.tag,r!==null&&r===a)break;f!==5&&f!==26&&f!==27||p===null||(r=p,n?(p=La(l,u),p!=null&&i.unshift(On(l,p,r))):n||(p=La(l,u),p!=null&&i.push(On(l,p,r)))),l=l.return}i.length!==0&&t.push({event:e,listeners:i})}var Mh=/\r\n?/g,Oh=/\u0000|\uFFFD/g;function id(t){return(typeof t=="string"?t:""+t).replace(Mh,`
`).replace(Oh,"")}function cd(t,e){return e=id(e),id(t)===e}function Vu(){}function St(t,e,l,a,n,u){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||la(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&la(t,""+a);break;case"className":Jn(t,"class",a);break;case"tabIndex":Jn(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Jn(t,l,a);break;case"style":fo(t,a,u);break;case"data":if(e!=="object"){Jn(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Fn(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&St(t,e,"name",n.name,n,null),St(t,e,"formEncType",n.formEncType,n,null),St(t,e,"formMethod",n.formMethod,n,null),St(t,e,"formTarget",n.formTarget,n,null)):(St(t,e,"encType",n.encType,n,null),St(t,e,"method",n.method,n,null),St(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Fn(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=Vu);break;case"onScroll":a!=null&&ft("scroll",t);break;case"onScrollEnd":a!=null&&ft("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=Fn(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":ft("beforetoggle",t),ft("toggle",t),kn(t,"popover",a);break;case"xlinkActuate":qe(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":qe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":qe(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":qe(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":qe(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":qe(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":qe(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":qe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":qe(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":kn(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=nv.get(l)||l,kn(t,l,a))}}function cf(t,e,l,a,n,u){switch(l){case"style":fo(t,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(o(60));t.innerHTML=l}}break;case"children":typeof a=="string"?la(t,a):(typeof a=="number"||typeof a=="bigint")&&la(t,""+a);break;case"onScroll":a!=null&&ft("scroll",t);break;case"onScrollEnd":a!=null&&ft("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Vu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ff.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),u=t[Pt]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):kn(t,l,a)}}}function kt(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ft("error",t),ft("load",t);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var i=l[u];if(i!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:St(t,e,u,i,l,null)}}n&&St(t,e,"srcSet",l.srcSet,l,null),a&&St(t,e,"src",l.src,l,null);return;case"input":ft("invalid",t);var f=u=i=n=null,r=null,p=null;for(a in l)if(l.hasOwnProperty(a)){var M=l[a];if(M!=null)switch(a){case"name":n=M;break;case"type":i=M;break;case"checked":r=M;break;case"defaultChecked":p=M;break;case"value":u=M;break;case"defaultValue":f=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(o(137,e));break;default:St(t,e,a,M,l,null)}}no(t,u,f,r,p,i,n,!1),$n(t);return;case"select":ft("invalid",t),a=i=u=null;for(n in l)if(l.hasOwnProperty(n)&&(f=l[n],f!=null))switch(n){case"value":u=f;break;case"defaultValue":i=f;break;case"multiple":a=f;default:St(t,e,n,f,l,null)}e=u,l=i,t.multiple=!!a,e!=null?ea(t,!!a,e,!1):l!=null&&ea(t,!!a,l,!0);return;case"textarea":ft("invalid",t),u=n=a=null;for(i in l)if(l.hasOwnProperty(i)&&(f=l[i],f!=null))switch(i){case"value":a=f;break;case"defaultValue":n=f;break;case"children":u=f;break;case"dangerouslySetInnerHTML":if(f!=null)throw Error(o(91));break;default:St(t,e,i,f,l,null)}io(t,a,n,u),$n(t);return;case"option":for(r in l)if(l.hasOwnProperty(r)&&(a=l[r],a!=null))switch(r){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:St(t,e,r,a,l,null)}return;case"dialog":ft("beforetoggle",t),ft("toggle",t),ft("cancel",t),ft("close",t);break;case"iframe":case"object":ft("load",t);break;case"video":case"audio":for(a=0;a<Mn.length;a++)ft(Mn[a],t);break;case"image":ft("error",t),ft("load",t);break;case"details":ft("toggle",t);break;case"embed":case"source":case"link":ft("error",t),ft("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(p in l)if(l.hasOwnProperty(p)&&(a=l[p],a!=null))switch(p){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:St(t,e,p,a,l,null)}return;default:if(Ti(e)){for(M in l)l.hasOwnProperty(M)&&(a=l[M],a!==void 0&&cf(t,e,M,a,l,void 0));return}}for(f in l)l.hasOwnProperty(f)&&(a=l[f],a!=null&&St(t,e,f,a,l,null))}function Rh(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,i=null,f=null,r=null,p=null,M=null;for(T in l){var N=l[T];if(l.hasOwnProperty(T)&&N!=null)switch(T){case"checked":break;case"value":break;case"defaultValue":r=N;default:a.hasOwnProperty(T)||St(t,e,T,null,a,N)}}for(var S in a){var T=a[S];if(N=l[S],a.hasOwnProperty(S)&&(T!=null||N!=null))switch(S){case"type":u=T;break;case"name":n=T;break;case"checked":p=T;break;case"defaultChecked":M=T;break;case"value":i=T;break;case"defaultValue":f=T;break;case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(o(137,e));break;default:T!==N&&St(t,e,S,T,a,N)}}Si(t,i,f,r,p,M,u,n);return;case"select":T=i=f=S=null;for(u in l)if(r=l[u],l.hasOwnProperty(u)&&r!=null)switch(u){case"value":break;case"multiple":T=r;default:a.hasOwnProperty(u)||St(t,e,u,null,a,r)}for(n in a)if(u=a[n],r=l[n],a.hasOwnProperty(n)&&(u!=null||r!=null))switch(n){case"value":S=u;break;case"defaultValue":f=u;break;case"multiple":i=u;default:u!==r&&St(t,e,n,u,a,r)}e=f,l=i,a=T,S!=null?ea(t,!!l,S,!1):!!a!=!!l&&(e!=null?ea(t,!!l,e,!0):ea(t,!!l,l?[]:"",!1));return;case"textarea":T=S=null;for(f in l)if(n=l[f],l.hasOwnProperty(f)&&n!=null&&!a.hasOwnProperty(f))switch(f){case"value":break;case"children":break;default:St(t,e,f,null,a,n)}for(i in a)if(n=a[i],u=l[i],a.hasOwnProperty(i)&&(n!=null||u!=null))switch(i){case"value":S=n;break;case"defaultValue":T=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(o(91));break;default:n!==u&&St(t,e,i,n,a,u)}uo(t,S,T);return;case"option":for(var $ in l)if(S=l[$],l.hasOwnProperty($)&&S!=null&&!a.hasOwnProperty($))switch($){case"selected":t.selected=!1;break;default:St(t,e,$,null,a,S)}for(r in a)if(S=a[r],T=l[r],a.hasOwnProperty(r)&&S!==T&&(S!=null||T!=null))switch(r){case"selected":t.selected=S&&typeof S!="function"&&typeof S!="symbol";break;default:St(t,e,r,S,a,T)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var k in l)S=l[k],l.hasOwnProperty(k)&&S!=null&&!a.hasOwnProperty(k)&&St(t,e,k,null,a,S);for(p in a)if(S=a[p],T=l[p],a.hasOwnProperty(p)&&S!==T&&(S!=null||T!=null))switch(p){case"children":case"dangerouslySetInnerHTML":if(S!=null)throw Error(o(137,e));break;default:St(t,e,p,S,a,T)}return;default:if(Ti(e)){for(var xt in l)S=l[xt],l.hasOwnProperty(xt)&&S!==void 0&&!a.hasOwnProperty(xt)&&cf(t,e,xt,void 0,a,S);for(M in a)S=a[M],T=l[M],!a.hasOwnProperty(M)||S===T||S===void 0&&T===void 0||cf(t,e,M,S,a,T);return}}for(var h in l)S=l[h],l.hasOwnProperty(h)&&S!=null&&!a.hasOwnProperty(h)&&St(t,e,h,null,a,S);for(N in a)S=a[N],T=l[N],!a.hasOwnProperty(N)||S===T||S==null&&T==null||St(t,e,N,S,a,T)}var ff=null,of=null;function Zu(t){return t.nodeType===9?t:t.ownerDocument}function fd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function od(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function sf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var rf=null;function Nh(){var t=window.event;return t&&t.type==="popstate"?t===rf?!1:(rf=t,!0):(rf=null,!1)}var sd=typeof setTimeout=="function"?setTimeout:void 0,Dh=typeof clearTimeout=="function"?clearTimeout:void 0,rd=typeof Promise=="function"?Promise:void 0,Uh=typeof queueMicrotask=="function"?queueMicrotask:typeof rd<"u"?function(t){return rd.resolve(null).then(t).catch(Ch)}:sd;function Ch(t){setTimeout(function(){throw t})}function Sl(t){return t==="head"}function dd(t,e){var l=e,a=0,n=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var i=t.ownerDocument;if(l&1&&Rn(i.documentElement),l&2&&Rn(i.body),l&4)for(l=i.head,Rn(l),i=l.firstChild;i;){var f=i.nextSibling,r=i.nodeName;i[Va]||r==="SCRIPT"||r==="STYLE"||r==="LINK"&&i.rel.toLowerCase()==="stylesheet"||l.removeChild(i),i=f}}if(n===0){t.removeChild(u),wn(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);wn(e)}function df(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":df(l),yi(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function jh(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[Va])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Oe(t.nextSibling),t===null)break}return null}function Hh(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Oe(t.nextSibling),t===null))return null;return t}function mf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function qh(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Oe(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var vf=null;function md(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function vd(t,e,l){switch(e=Zu(l),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function Rn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);yi(t)}var ze=new Map,hd=new Set;function Lu(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var We=q.d;q.d={f:wh,r:Gh,D:Bh,C:Yh,L:Xh,m:Qh,X:Zh,S:Vh,M:Lh};function wh(){var t=We.f(),e=qu();return t||e}function Gh(t){var e=Fl(t);e!==null&&e.tag===5&&e.type==="form"?Hs(e):We.r(t)}var Da=typeof document>"u"?null:document;function yd(t,e,l){var a=Da;if(a&&typeof e=="string"&&e){var n=be(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),hd.has(n)||(hd.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),kt(e,"link",t),Xt(e),a.head.appendChild(e)))}}function Bh(t){We.D(t),yd("dns-prefetch",t,null)}function Yh(t,e){We.C(t,e),yd("preconnect",t,e)}function Xh(t,e,l){We.L(t,e,l);var a=Da;if(a&&t&&e){var n='link[rel="preload"][as="'+be(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+be(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+be(l.imageSizes)+'"]')):n+='[href="'+be(t)+'"]';var u=n;switch(e){case"style":u=Ua(t);break;case"script":u=Ca(t)}ze.has(u)||(t=A({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),ze.set(u,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(Nn(u))||e==="script"&&a.querySelector(Dn(u))||(e=a.createElement("link"),kt(e,"link",t),Xt(e),a.head.appendChild(e)))}}function Qh(t,e){We.m(t,e);var l=Da;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+be(a)+'"][href="'+be(t)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Ca(t)}if(!ze.has(u)&&(t=A({rel:"modulepreload",href:t},e),ze.set(u,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Dn(u)))return}a=l.createElement("link"),kt(a,"link",t),Xt(a),l.head.appendChild(a)}}}function Vh(t,e,l){We.S(t,e,l);var a=Da;if(a&&t){var n=Il(a).hoistableStyles,u=Ua(t);e=e||"default";var i=n.get(u);if(!i){var f={loading:0,preload:null};if(i=a.querySelector(Nn(u)))f.loading=5;else{t=A({rel:"stylesheet",href:t,"data-precedence":e},l),(l=ze.get(u))&&hf(t,l);var r=i=a.createElement("link");Xt(r),kt(r,"link",t),r._p=new Promise(function(p,M){r.onload=p,r.onerror=M}),r.addEventListener("load",function(){f.loading|=1}),r.addEventListener("error",function(){f.loading|=2}),f.loading|=4,Ku(i,e,a)}i={type:"stylesheet",instance:i,count:1,state:f},n.set(u,i)}}}function Zh(t,e){We.X(t,e);var l=Da;if(l&&t){var a=Il(l).hoistableScripts,n=Ca(t),u=a.get(n);u||(u=l.querySelector(Dn(n)),u||(t=A({src:t,async:!0},e),(e=ze.get(n))&&yf(t,e),u=l.createElement("script"),Xt(u),kt(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Lh(t,e){We.M(t,e);var l=Da;if(l&&t){var a=Il(l).hoistableScripts,n=Ca(t),u=a.get(n);u||(u=l.querySelector(Dn(n)),u||(t=A({src:t,async:!0,type:"module"},e),(e=ze.get(n))&&yf(t,e),u=l.createElement("script"),Xt(u),kt(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function gd(t,e,l,a){var n=(n=W.current)?Lu(n):null;if(!n)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=Ua(l.href),l=Il(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=Ua(l.href);var u=Il(n).hoistableStyles,i=u.get(t);if(i||(n=n.ownerDocument||n,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,i),(u=n.querySelector(Nn(t)))&&!u._p&&(i.instance=u,i.state.loading=5),ze.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},ze.set(t,l),u||Kh(n,t,l,i.state))),e&&a===null)throw Error(o(528,""));return i}if(e&&a!==null)throw Error(o(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ca(l),l=Il(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function Ua(t){return'href="'+be(t)+'"'}function Nn(t){return'link[rel="stylesheet"]['+t+"]"}function bd(t){return A({},t,{"data-precedence":t.precedence,precedence:null})}function Kh(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),kt(e,"link",l),Xt(e),t.head.appendChild(e))}function Ca(t){return'[src="'+be(t)+'"]'}function Dn(t){return"script[async]"+t}function pd(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+be(l.href)+'"]');if(a)return e.instance=a,Xt(a),a;var n=A({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Xt(a),kt(a,"style",n),Ku(a,l.precedence,t),e.instance=a;case"stylesheet":n=Ua(l.href);var u=t.querySelector(Nn(n));if(u)return e.state.loading|=4,e.instance=u,Xt(u),u;a=bd(l),(n=ze.get(n))&&hf(a,n),u=(t.ownerDocument||t).createElement("link"),Xt(u);var i=u;return i._p=new Promise(function(f,r){i.onload=f,i.onerror=r}),kt(u,"link",a),e.state.loading|=4,Ku(u,l.precedence,t),e.instance=u;case"script":return u=Ca(l.src),(n=t.querySelector(Dn(u)))?(e.instance=n,Xt(n),n):(a=l,(n=ze.get(u))&&(a=A({},l),yf(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Xt(n),kt(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Ku(a,l.precedence,t));return e.instance}function Ku(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,i=0;i<a.length;i++){var f=a[i];if(f.dataset.precedence===e)u=f;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function hf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function yf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var ku=null;function Sd(t,e,l){if(ku===null){var a=new Map,n=ku=new Map;n.set(l,a)}else n=ku,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var u=l[n];if(!(u[Va]||u[$t]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var i=u.getAttribute(e)||"";i=t+i;var f=a.get(i);f?f.push(u):a.set(i,[u])}}return a}function xd(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function kh(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Td(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Un=null;function Jh(){}function $h(t,e,l){if(Un===null)throw Error(o(475));var a=Un;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Ua(l.href),u=t.querySelector(Nn(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Ju.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=u,Xt(u);return}u=t.ownerDocument||t,l=bd(l),(n=ze.get(n))&&hf(l,n),u=u.createElement("link"),Xt(u);var i=u;i._p=new Promise(function(f,r){i.onload=f,i.onerror=r}),kt(u,"link",l),e.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Ju.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function Wh(){if(Un===null)throw Error(o(475));var t=Un;return t.stylesheets&&t.count===0&&gf(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&gf(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Ju(){if(this.count--,this.count===0){if(this.stylesheets)gf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var $u=null;function gf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,$u=new Map,e.forEach(Fh,t),$u=null,Ju.call(t))}function Fh(t,e){if(!(e.state.loading&4)){var l=$u.get(t);if(l)var a=l.get(null);else{l=new Map,$u.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var i=n[u];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(l.set(i.dataset.precedence,i),a=i)}a&&l.set(null,a)}n=e.instance,i=n.getAttribute("data-precedence"),u=l.get(i)||a,u===a&&l.set(null,n),l.set(i,n),this.count++,a=Ju.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Cn={$$typeof:et,Provider:null,Consumer:null,_currentValue:C,_currentValue2:C,_threadCount:0};function Ih(t,e,l,a,n,u,i,f){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=di(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=di(0),this.hiddenUpdates=di(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=f,this.incompleteTransitions=new Map}function Ad(t,e,l,a,n,u,i,f,r,p,M,N){return t=new Ih(t,e,l,i,f,r,p,N),e=1,u===!0&&(e|=24),u=fe(3,null,null,e),t.current=u,u.stateNode=t,e=Fi(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:e},ec(u),t}function Ed(t){return t?(t=sa,t):sa}function zd(t,e,l,a,n,u){n=Ed(n),a.context===null?a.context=n:a.pendingContext=n,a=cl(e),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=fl(t,a,e),l!==null&&(me(l,t,e),on(l,t,e))}function _d(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function bf(t,e){_d(t,e),(t=t.alternate)&&_d(t,e)}function Md(t){if(t.tag===13){var e=oa(t,67108864);e!==null&&me(e,t,67108864),bf(t,67108864)}}var Wu=!0;function Ph(t,e,l,a){var n=E.T;E.T=null;var u=q.p;try{q.p=2,pf(t,e,l,a)}finally{q.p=u,E.T=n}}function ty(t,e,l,a){var n=E.T;E.T=null;var u=q.p;try{q.p=8,pf(t,e,l,a)}finally{q.p=u,E.T=n}}function pf(t,e,l,a){if(Wu){var n=Sf(a);if(n===null)uf(t,e,a,Fu,l),Rd(t,a);else if(ly(n,t,e,l,a))a.stopPropagation();else if(Rd(t,a),e&4&&-1<ey.indexOf(t)){for(;n!==null;){var u=Fl(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var i=Ol(u.pendingLanes);if(i!==0){var f=u;for(f.pendingLanes|=2,f.entangledLanes|=2;i;){var r=1<<31-ie(i);f.entanglements[1]|=r,i&=~r}He(u),(yt&6)===0&&(ju=Ne()+500,_n(0))}}break;case 13:f=oa(u,2),f!==null&&me(f,u,2),qu(),bf(u,2)}if(u=Sf(a),u===null&&uf(t,e,a,Fu,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else uf(t,e,a,null,l)}}function Sf(t){return t=Ei(t),xf(t)}var Fu=null;function xf(t){if(Fu=null,t=Wl(t),t!==null){var e=x(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=_(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Fu=t,null}function Od(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ym()){case Xf:return 2;case Qf:return 8;case Vn:case Xm:return 32;case Vf:return 268435456;default:return 32}default:return 32}}var Tf=!1,xl=null,Tl=null,Al=null,jn=new Map,Hn=new Map,El=[],ey="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Rd(t,e){switch(t){case"focusin":case"focusout":xl=null;break;case"dragenter":case"dragleave":Tl=null;break;case"mouseover":case"mouseout":Al=null;break;case"pointerover":case"pointerout":jn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hn.delete(e.pointerId)}}function qn(t,e,l,a,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},e!==null&&(e=Fl(e),e!==null&&Md(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function ly(t,e,l,a,n){switch(e){case"focusin":return xl=qn(xl,t,e,l,a,n),!0;case"dragenter":return Tl=qn(Tl,t,e,l,a,n),!0;case"mouseover":return Al=qn(Al,t,e,l,a,n),!0;case"pointerover":var u=n.pointerId;return jn.set(u,qn(jn.get(u)||null,t,e,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Hn.set(u,qn(Hn.get(u)||null,t,e,l,a,n)),!0}return!1}function Nd(t){var e=Wl(t.target);if(e!==null){var l=x(e);if(l!==null){if(e=l.tag,e===13){if(e=_(l),e!==null){t.blockedOn=e,$m(t.priority,function(){if(l.tag===13){var a=de();a=mi(a);var n=oa(l,a);n!==null&&me(n,l,a),bf(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Iu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=Sf(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);Ai=a,l.target.dispatchEvent(a),Ai=null}else return e=Fl(l),e!==null&&Md(e),t.blockedOn=l,!1;e.shift()}return!0}function Dd(t,e,l){Iu(t)&&l.delete(e)}function ay(){Tf=!1,xl!==null&&Iu(xl)&&(xl=null),Tl!==null&&Iu(Tl)&&(Tl=null),Al!==null&&Iu(Al)&&(Al=null),jn.forEach(Dd),Hn.forEach(Dd)}function Pu(t,e){t.blockedOn===e&&(t.blockedOn=null,Tf||(Tf=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,ay)))}var ti=null;function Ud(t){ti!==t&&(ti=t,c.unstable_scheduleCallback(c.unstable_NormalPriority,function(){ti===t&&(ti=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(xf(a||l)===null)continue;break}var u=Fl(l);u!==null&&(t.splice(e,3),e-=3,Sc(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function wn(t){function e(r){return Pu(r,t)}xl!==null&&Pu(xl,t),Tl!==null&&Pu(Tl,t),Al!==null&&Pu(Al,t),jn.forEach(e),Hn.forEach(e);for(var l=0;l<El.length;l++){var a=El[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<El.length&&(l=El[0],l.blockedOn===null);)Nd(l),l.blockedOn===null&&El.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],i=n[Pt]||null;if(typeof u=="function")i||Ud(l);else if(i){var f=null;if(u&&u.hasAttribute("formAction")){if(n=u,i=u[Pt]||null)f=i.formAction;else if(xf(n)!==null)continue}else f=i.action;typeof f=="function"?l[a+1]=f:(l.splice(a,3),a-=3),Ud(l)}}}function Af(t){this._internalRoot=t}ei.prototype.render=Af.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var l=e.current,a=de();zd(l,a,t,e,null,null)},ei.prototype.unmount=Af.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;zd(t.current,2,null,t,null,null),qu(),e[$l]=null}};function ei(t){this._internalRoot=t}ei.prototype.unstable_scheduleHydration=function(t){if(t){var e=Jf();t={blockedOn:null,target:t,priority:e};for(var l=0;l<El.length&&e!==0&&e<El[l].priority;l++);El.splice(l,0,t),l===0&&Nd(t)}};var Cd=s.version;if(Cd!=="19.1.0")throw Error(o(527,Cd,"19.1.0"));q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=z(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var ny={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:E,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var li=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!li.isDisabled&&li.supportsFiber)try{Ya=li.inject(ny),ue=li}catch{}}return Bn.createRoot=function(t,e){if(!y(t))throw Error(o(299));var l=!1,a="",n=$s,u=Ws,i=Fs,f=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(i=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(f=e.unstable_transitionCallbacks)),e=Ad(t,1,!1,null,null,l,a,n,u,i,f,null),t[$l]=e.current,nf(t),new Af(e)},Bn.hydrateRoot=function(t,e,l){if(!y(t))throw Error(o(299));var a=!1,n="",u=$s,i=Ws,f=Fs,r=null,p=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(i=l.onCaughtError),l.onRecoverableError!==void 0&&(f=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(r=l.unstable_transitionCallbacks),l.formState!==void 0&&(p=l.formState)),e=Ad(t,1,!0,e,l??null,a,n,u,i,f,r,p),e.context=Ed(null),l=e.current,a=de(),a=mi(a),n=cl(a),n.callback=null,fl(l,n,a),l=a,e.current.lanes=l,Qa(e,l),He(e),t[$l]=e.current,nf(t),new ei(e)},Bn.version="19.1.0",Bn}var Vd;function hy(){if(Vd)return _f.exports;Vd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),_f.exports=vy(),_f.exports}var yy=hy();/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gy=c=>c.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),by=c=>c.replace(/^([A-Z])|[\s-_]+(\w)/g,(s,d,o)=>o?o.toUpperCase():d.toLowerCase()),Zd=c=>{const s=by(c);return s.charAt(0).toUpperCase()+s.slice(1)},um=(...c)=>c.filter((s,d,o)=>!!s&&s.trim()!==""&&o.indexOf(s)===d).join(" ").trim(),py=c=>{for(const s in c)if(s.startsWith("aria-")||s==="role"||s==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Sy={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xy=w.forwardRef(({color:c="currentColor",size:s=24,strokeWidth:d=2,absoluteStrokeWidth:o,className:y="",children:x,iconNode:_,...U},z)=>w.createElement("svg",{ref:z,...Sy,width:s,height:s,stroke:c,strokeWidth:o?Number(d)*24/Number(s):d,className:um("lucide",y),...!x&&!py(U)&&{"aria-hidden":"true"},...U},[..._.map(([g,A])=>w.createElement(g,A)),...Array.isArray(x)?x:[x]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fi=(c,s)=>{const d=w.forwardRef(({className:o,...y},x)=>w.createElement(xy,{ref:x,iconNode:s,className:um(`lucide-${gy(Zd(c))}`,`lucide-${c}`,o),...y}));return d.displayName=Zd(c),d};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ty=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],Ay=fi("database",Ty);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ey=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],zy=fi("globe",Ey);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _y=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Ld=fi("search",_y);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const My=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]],Oy=fi("trending-up",My);function Kd(c,s){if(typeof c=="function")return c(s);c!=null&&(c.current=s)}function Ry(...c){return s=>{let d=!1;const o=c.map(y=>{const x=Kd(y,s);return!d&&typeof x=="function"&&(d=!0),x});if(d)return()=>{for(let y=0;y<o.length;y++){const x=o[y];typeof x=="function"?x():Kd(c[y],null)}}}}function Xn(...c){return w.useCallback(Ry(...c),c)}function ii(c){const s=Dy(c),d=w.forwardRef((o,y)=>{const{children:x,..._}=o,U=w.Children.toArray(x),z=U.find(Cy);if(z){const g=z.props.children,A=U.map(j=>j===z?w.Children.count(g)>1?w.Children.only(null):w.isValidElement(g)?g.props.children:null:j);return R.jsx(s,{..._,ref:y,children:w.isValidElement(g)?w.cloneElement(g,void 0,A):null})}return R.jsx(s,{..._,ref:y,children:x})});return d.displayName=`${c}.Slot`,d}var Ny=ii("Slot");function Dy(c){const s=w.forwardRef((d,o)=>{const{children:y,...x}=d,_=w.isValidElement(y)?Hy(y):void 0,U=Xn(_,o);if(w.isValidElement(y)){const z=jy(x,y.props);return y.type!==w.Fragment&&(z.ref=U),w.cloneElement(y,z)}return w.Children.count(y)>1?w.Children.only(null):null});return s.displayName=`${c}.SlotClone`,s}var Uy=Symbol("radix.slottable");function Cy(c){return w.isValidElement(c)&&typeof c.type=="function"&&"__radixId"in c.type&&c.type.__radixId===Uy}function jy(c,s){const d={...s};for(const o in s){const y=c[o],x=s[o];/^on[A-Z]/.test(o)?y&&x?d[o]=(...U)=>{const z=x(...U);return y(...U),z}:y&&(d[o]=y):o==="style"?d[o]={...y,...x}:o==="className"&&(d[o]=[y,x].filter(Boolean).join(" "))}return{...c,...d}}function Hy(c){var o,y;let s=(o=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:o.get,d=s&&"isReactWarning"in s&&s.isReactWarning;return d?c.ref:(s=(y=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:y.get,d=s&&"isReactWarning"in s&&s.isReactWarning,d?c.props.ref:c.props.ref||c.ref)}function im(c){var s,d,o="";if(typeof c=="string"||typeof c=="number")o+=c;else if(typeof c=="object")if(Array.isArray(c)){var y=c.length;for(s=0;s<y;s++)c[s]&&(d=im(c[s]))&&(o&&(o+=" "),o+=d)}else for(d in c)c[d]&&(o&&(o+=" "),o+=d);return o}function cm(){for(var c,s,d=0,o="",y=arguments.length;d<y;d++)(c=arguments[d])&&(s=im(c))&&(o&&(o+=" "),o+=s);return o}const kd=c=>typeof c=="boolean"?`${c}`:c===0?"0":c,Jd=cm,qy=(c,s)=>d=>{var o;if((s==null?void 0:s.variants)==null)return Jd(c,d==null?void 0:d.class,d==null?void 0:d.className);const{variants:y,defaultVariants:x}=s,_=Object.keys(y).map(g=>{const A=d==null?void 0:d[g],j=x==null?void 0:x[g];if(A===null)return null;const B=kd(A)||kd(j);return y[g][B]}),U=d&&Object.entries(d).reduce((g,A)=>{let[j,B]=A;return B===void 0||(g[j]=B),g},{}),z=s==null||(o=s.compoundVariants)===null||o===void 0?void 0:o.reduce((g,A)=>{let{class:j,className:B,...tt}=A;return Object.entries(tt).every(P=>{let[F,L]=P;return Array.isArray(L)?L.includes({...x,...U}[F]):{...x,...U}[F]===L})?[...g,j,B]:g},[]);return Jd(c,_,z,d==null?void 0:d.class,d==null?void 0:d.className)},Gf="-",wy=c=>{const s=By(c),{conflictingClassGroups:d,conflictingClassGroupModifiers:o}=c;return{getClassGroupId:_=>{const U=_.split(Gf);return U[0]===""&&U.length!==1&&U.shift(),fm(U,s)||Gy(_)},getConflictingClassGroupIds:(_,U)=>{const z=d[_]||[];return U&&o[_]?[...z,...o[_]]:z}}},fm=(c,s)=>{var _;if(c.length===0)return s.classGroupId;const d=c[0],o=s.nextPart.get(d),y=o?fm(c.slice(1),o):void 0;if(y)return y;if(s.validators.length===0)return;const x=c.join(Gf);return(_=s.validators.find(({validator:U})=>U(x)))==null?void 0:_.classGroupId},$d=/^\[(.+)\]$/,Gy=c=>{if($d.test(c)){const s=$d.exec(c)[1],d=s==null?void 0:s.substring(0,s.indexOf(":"));if(d)return"arbitrary.."+d}},By=c=>{const{theme:s,classGroups:d}=c,o={nextPart:new Map,validators:[]};for(const y in d)Cf(d[y],o,y,s);return o},Cf=(c,s,d,o)=>{c.forEach(y=>{if(typeof y=="string"){const x=y===""?s:Wd(s,y);x.classGroupId=d;return}if(typeof y=="function"){if(Yy(y)){Cf(y(o),s,d,o);return}s.validators.push({validator:y,classGroupId:d});return}Object.entries(y).forEach(([x,_])=>{Cf(_,Wd(s,x),d,o)})})},Wd=(c,s)=>{let d=c;return s.split(Gf).forEach(o=>{d.nextPart.has(o)||d.nextPart.set(o,{nextPart:new Map,validators:[]}),d=d.nextPart.get(o)}),d},Yy=c=>c.isThemeGetter,Xy=c=>{if(c<1)return{get:()=>{},set:()=>{}};let s=0,d=new Map,o=new Map;const y=(x,_)=>{d.set(x,_),s++,s>c&&(s=0,o=d,d=new Map)};return{get(x){let _=d.get(x);if(_!==void 0)return _;if((_=o.get(x))!==void 0)return y(x,_),_},set(x,_){d.has(x)?d.set(x,_):y(x,_)}}},jf="!",Hf=":",Qy=Hf.length,Vy=c=>{const{prefix:s,experimentalParseClassName:d}=c;let o=y=>{const x=[];let _=0,U=0,z=0,g;for(let P=0;P<y.length;P++){let F=y[P];if(_===0&&U===0){if(F===Hf){x.push(y.slice(z,P)),z=P+Qy;continue}if(F==="/"){g=P;continue}}F==="["?_++:F==="]"?_--:F==="("?U++:F===")"&&U--}const A=x.length===0?y:y.substring(z),j=Zy(A),B=j!==A,tt=g&&g>z?g-z:void 0;return{modifiers:x,hasImportantModifier:B,baseClassName:j,maybePostfixModifierPosition:tt}};if(s){const y=s+Hf,x=o;o=_=>_.startsWith(y)?x(_.substring(y.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:_,maybePostfixModifierPosition:void 0}}if(d){const y=o;o=x=>d({className:x,parseClassName:y})}return o},Zy=c=>c.endsWith(jf)?c.substring(0,c.length-1):c.startsWith(jf)?c.substring(1):c,Ly=c=>{const s=Object.fromEntries(c.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const y=[];let x=[];return o.forEach(_=>{_[0]==="["||s[_]?(y.push(...x.sort(),_),x=[]):x.push(_)}),y.push(...x.sort()),y}},Ky=c=>({cache:Xy(c.cacheSize),parseClassName:Vy(c),sortModifiers:Ly(c),...wy(c)}),ky=/\s+/,Jy=(c,s)=>{const{parseClassName:d,getClassGroupId:o,getConflictingClassGroupIds:y,sortModifiers:x}=s,_=[],U=c.trim().split(ky);let z="";for(let g=U.length-1;g>=0;g-=1){const A=U[g],{isExternal:j,modifiers:B,hasImportantModifier:tt,baseClassName:P,maybePostfixModifierPosition:F}=d(A);if(j){z=A+(z.length>0?" "+z:z);continue}let L=!!F,st=o(L?P.substring(0,F):P);if(!st){if(!L){z=A+(z.length>0?" "+z:z);continue}if(st=o(P),!st){z=A+(z.length>0?" "+z:z);continue}L=!1}const Tt=x(B).join(":"),et=tt?Tt+jf:Tt,ot=et+st;if(_.includes(ot))continue;_.push(ot);const I=y(st,L);for(let bt=0;bt<I.length;++bt){const Mt=I[bt];_.push(et+Mt)}z=A+(z.length>0?" "+z:z)}return z};function $y(){let c=0,s,d,o="";for(;c<arguments.length;)(s=arguments[c++])&&(d=om(s))&&(o&&(o+=" "),o+=d);return o}const om=c=>{if(typeof c=="string")return c;let s,d="";for(let o=0;o<c.length;o++)c[o]&&(s=om(c[o]))&&(d&&(d+=" "),d+=s);return d};function Wy(c,...s){let d,o,y,x=_;function _(z){const g=s.reduce((A,j)=>j(A),c());return d=Ky(g),o=d.cache.get,y=d.cache.set,x=U,U(z)}function U(z){const g=o(z);if(g)return g;const A=Jy(z,d);return y(z,A),A}return function(){return x($y.apply(null,arguments))}}const Yt=c=>{const s=d=>d[c]||[];return s.isThemeGetter=!0,s},sm=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,rm=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Fy=/^\d+\/\d+$/,Iy=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Py=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,t0=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,e0=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,l0=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ja=c=>Fy.test(c),nt=c=>!!c&&!Number.isNaN(Number(c)),_l=c=>!!c&&Number.isInteger(Number(c)),Nf=c=>c.endsWith("%")&&nt(c.slice(0,-1)),Fe=c=>Iy.test(c),a0=()=>!0,n0=c=>Py.test(c)&&!t0.test(c),dm=()=>!1,u0=c=>e0.test(c),i0=c=>l0.test(c),c0=c=>!V(c)&&!Z(c),f0=c=>wa(c,hm,dm),V=c=>sm.test(c),Kl=c=>wa(c,ym,n0),Df=c=>wa(c,m0,nt),Fd=c=>wa(c,mm,dm),o0=c=>wa(c,vm,i0),ai=c=>wa(c,gm,u0),Z=c=>rm.test(c),Yn=c=>Ga(c,ym),s0=c=>Ga(c,v0),Id=c=>Ga(c,mm),r0=c=>Ga(c,hm),d0=c=>Ga(c,vm),ni=c=>Ga(c,gm,!0),wa=(c,s,d)=>{const o=sm.exec(c);return o?o[1]?s(o[1]):d(o[2]):!1},Ga=(c,s,d=!1)=>{const o=rm.exec(c);return o?o[1]?s(o[1]):d:!1},mm=c=>c==="position"||c==="percentage",vm=c=>c==="image"||c==="url",hm=c=>c==="length"||c==="size"||c==="bg-size",ym=c=>c==="length",m0=c=>c==="number",v0=c=>c==="family-name",gm=c=>c==="shadow",h0=()=>{const c=Yt("color"),s=Yt("font"),d=Yt("text"),o=Yt("font-weight"),y=Yt("tracking"),x=Yt("leading"),_=Yt("breakpoint"),U=Yt("container"),z=Yt("spacing"),g=Yt("radius"),A=Yt("shadow"),j=Yt("inset-shadow"),B=Yt("text-shadow"),tt=Yt("drop-shadow"),P=Yt("blur"),F=Yt("perspective"),L=Yt("aspect"),st=Yt("ease"),Tt=Yt("animate"),et=()=>["auto","avoid","all","avoid-page","page","left","right","column"],ot=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],I=()=>[...ot(),Z,V],bt=()=>["auto","hidden","clip","visible","scroll"],Mt=()=>["auto","contain","none"],Y=()=>[Z,V,z],Dt=()=>[ja,"full","auto",...Y()],ve=()=>[_l,"none","subgrid",Z,V],Jt=()=>["auto",{span:["full",_l,Z,V]},_l,Z,V],Rt=()=>[_l,"auto",Z,V],he=()=>["auto","min","max","fr",Z,V],ye=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_t=()=>["start","end","center","stretch","center-safe","end-safe"],E=()=>["auto",...Y()],q=()=>[ja,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...Y()],C=()=>[c,Z,V],ht=()=>[...ot(),Id,Fd,{position:[Z,V]}],m=()=>["no-repeat",{repeat:["","x","y","space","round"]}],D=()=>["auto","cover","contain",r0,f0,{size:[Z,V]}],G=()=>[Nf,Yn,Kl],H=()=>["","none","full",g,Z,V],X=()=>["",nt,Yn,Kl],it=()=>["solid","dashed","dotted","double"],W=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],dt=()=>[nt,Nf,Id,Fd],At=()=>["","none",P,Z,V],ne=()=>["none",nt,Z,V],Pe=()=>["none",nt,Z,V],tl=()=>[nt,Z,V],el=()=>[ja,"full",...Y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Fe],breakpoint:[Fe],color:[a0],container:[Fe],"drop-shadow":[Fe],ease:["in","out","in-out"],font:[c0],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Fe],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Fe],shadow:[Fe],spacing:["px",nt],text:[Fe],"text-shadow":[Fe],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",ja,V,Z,L]}],container:["container"],columns:[{columns:[nt,V,Z,U]}],"break-after":[{"break-after":et()}],"break-before":[{"break-before":et()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:I()}],overflow:[{overflow:bt()}],"overflow-x":[{"overflow-x":bt()}],"overflow-y":[{"overflow-y":bt()}],overscroll:[{overscroll:Mt()}],"overscroll-x":[{"overscroll-x":Mt()}],"overscroll-y":[{"overscroll-y":Mt()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Dt()}],"inset-x":[{"inset-x":Dt()}],"inset-y":[{"inset-y":Dt()}],start:[{start:Dt()}],end:[{end:Dt()}],top:[{top:Dt()}],right:[{right:Dt()}],bottom:[{bottom:Dt()}],left:[{left:Dt()}],visibility:["visible","invisible","collapse"],z:[{z:[_l,"auto",Z,V]}],basis:[{basis:[ja,"full","auto",U,...Y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[nt,ja,"auto","initial","none",V]}],grow:[{grow:["",nt,Z,V]}],shrink:[{shrink:["",nt,Z,V]}],order:[{order:[_l,"first","last","none",Z,V]}],"grid-cols":[{"grid-cols":ve()}],"col-start-end":[{col:Jt()}],"col-start":[{"col-start":Rt()}],"col-end":[{"col-end":Rt()}],"grid-rows":[{"grid-rows":ve()}],"row-start-end":[{row:Jt()}],"row-start":[{"row-start":Rt()}],"row-end":[{"row-end":Rt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":he()}],"auto-rows":[{"auto-rows":he()}],gap:[{gap:Y()}],"gap-x":[{"gap-x":Y()}],"gap-y":[{"gap-y":Y()}],"justify-content":[{justify:[...ye(),"normal"]}],"justify-items":[{"justify-items":[..._t(),"normal"]}],"justify-self":[{"justify-self":["auto",..._t()]}],"align-content":[{content:["normal",...ye()]}],"align-items":[{items:[..._t(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._t(),{baseline:["","last"]}]}],"place-content":[{"place-content":ye()}],"place-items":[{"place-items":[..._t(),"baseline"]}],"place-self":[{"place-self":["auto",..._t()]}],p:[{p:Y()}],px:[{px:Y()}],py:[{py:Y()}],ps:[{ps:Y()}],pe:[{pe:Y()}],pt:[{pt:Y()}],pr:[{pr:Y()}],pb:[{pb:Y()}],pl:[{pl:Y()}],m:[{m:E()}],mx:[{mx:E()}],my:[{my:E()}],ms:[{ms:E()}],me:[{me:E()}],mt:[{mt:E()}],mr:[{mr:E()}],mb:[{mb:E()}],ml:[{ml:E()}],"space-x":[{"space-x":Y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":Y()}],"space-y-reverse":["space-y-reverse"],size:[{size:q()}],w:[{w:[U,"screen",...q()]}],"min-w":[{"min-w":[U,"screen","none",...q()]}],"max-w":[{"max-w":[U,"screen","none","prose",{screen:[_]},...q()]}],h:[{h:["screen","lh",...q()]}],"min-h":[{"min-h":["screen","lh","none",...q()]}],"max-h":[{"max-h":["screen","lh",...q()]}],"font-size":[{text:["base",d,Yn,Kl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,Z,Df]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Nf,V]}],"font-family":[{font:[s0,V,s]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[y,Z,V]}],"line-clamp":[{"line-clamp":[nt,"none",Z,Df]}],leading:[{leading:[x,...Y()]}],"list-image":[{"list-image":["none",Z,V]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Z,V]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:C()}],"text-color":[{text:C()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...it(),"wavy"]}],"text-decoration-thickness":[{decoration:[nt,"from-font","auto",Z,Kl]}],"text-decoration-color":[{decoration:C()}],"underline-offset":[{"underline-offset":[nt,"auto",Z,V]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z,V]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z,V]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ht()}],"bg-repeat":[{bg:m()}],"bg-size":[{bg:D()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},_l,Z,V],radial:["",Z,V],conic:[_l,Z,V]},d0,o0]}],"bg-color":[{bg:C()}],"gradient-from-pos":[{from:G()}],"gradient-via-pos":[{via:G()}],"gradient-to-pos":[{to:G()}],"gradient-from":[{from:C()}],"gradient-via":[{via:C()}],"gradient-to":[{to:C()}],rounded:[{rounded:H()}],"rounded-s":[{"rounded-s":H()}],"rounded-e":[{"rounded-e":H()}],"rounded-t":[{"rounded-t":H()}],"rounded-r":[{"rounded-r":H()}],"rounded-b":[{"rounded-b":H()}],"rounded-l":[{"rounded-l":H()}],"rounded-ss":[{"rounded-ss":H()}],"rounded-se":[{"rounded-se":H()}],"rounded-ee":[{"rounded-ee":H()}],"rounded-es":[{"rounded-es":H()}],"rounded-tl":[{"rounded-tl":H()}],"rounded-tr":[{"rounded-tr":H()}],"rounded-br":[{"rounded-br":H()}],"rounded-bl":[{"rounded-bl":H()}],"border-w":[{border:X()}],"border-w-x":[{"border-x":X()}],"border-w-y":[{"border-y":X()}],"border-w-s":[{"border-s":X()}],"border-w-e":[{"border-e":X()}],"border-w-t":[{"border-t":X()}],"border-w-r":[{"border-r":X()}],"border-w-b":[{"border-b":X()}],"border-w-l":[{"border-l":X()}],"divide-x":[{"divide-x":X()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":X()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...it(),"hidden","none"]}],"divide-style":[{divide:[...it(),"hidden","none"]}],"border-color":[{border:C()}],"border-color-x":[{"border-x":C()}],"border-color-y":[{"border-y":C()}],"border-color-s":[{"border-s":C()}],"border-color-e":[{"border-e":C()}],"border-color-t":[{"border-t":C()}],"border-color-r":[{"border-r":C()}],"border-color-b":[{"border-b":C()}],"border-color-l":[{"border-l":C()}],"divide-color":[{divide:C()}],"outline-style":[{outline:[...it(),"none","hidden"]}],"outline-offset":[{"outline-offset":[nt,Z,V]}],"outline-w":[{outline:["",nt,Yn,Kl]}],"outline-color":[{outline:C()}],shadow:[{shadow:["","none",A,ni,ai]}],"shadow-color":[{shadow:C()}],"inset-shadow":[{"inset-shadow":["none",j,ni,ai]}],"inset-shadow-color":[{"inset-shadow":C()}],"ring-w":[{ring:X()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:C()}],"ring-offset-w":[{"ring-offset":[nt,Kl]}],"ring-offset-color":[{"ring-offset":C()}],"inset-ring-w":[{"inset-ring":X()}],"inset-ring-color":[{"inset-ring":C()}],"text-shadow":[{"text-shadow":["none",B,ni,ai]}],"text-shadow-color":[{"text-shadow":C()}],opacity:[{opacity:[nt,Z,V]}],"mix-blend":[{"mix-blend":[...W(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":W()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[nt]}],"mask-image-linear-from-pos":[{"mask-linear-from":dt()}],"mask-image-linear-to-pos":[{"mask-linear-to":dt()}],"mask-image-linear-from-color":[{"mask-linear-from":C()}],"mask-image-linear-to-color":[{"mask-linear-to":C()}],"mask-image-t-from-pos":[{"mask-t-from":dt()}],"mask-image-t-to-pos":[{"mask-t-to":dt()}],"mask-image-t-from-color":[{"mask-t-from":C()}],"mask-image-t-to-color":[{"mask-t-to":C()}],"mask-image-r-from-pos":[{"mask-r-from":dt()}],"mask-image-r-to-pos":[{"mask-r-to":dt()}],"mask-image-r-from-color":[{"mask-r-from":C()}],"mask-image-r-to-color":[{"mask-r-to":C()}],"mask-image-b-from-pos":[{"mask-b-from":dt()}],"mask-image-b-to-pos":[{"mask-b-to":dt()}],"mask-image-b-from-color":[{"mask-b-from":C()}],"mask-image-b-to-color":[{"mask-b-to":C()}],"mask-image-l-from-pos":[{"mask-l-from":dt()}],"mask-image-l-to-pos":[{"mask-l-to":dt()}],"mask-image-l-from-color":[{"mask-l-from":C()}],"mask-image-l-to-color":[{"mask-l-to":C()}],"mask-image-x-from-pos":[{"mask-x-from":dt()}],"mask-image-x-to-pos":[{"mask-x-to":dt()}],"mask-image-x-from-color":[{"mask-x-from":C()}],"mask-image-x-to-color":[{"mask-x-to":C()}],"mask-image-y-from-pos":[{"mask-y-from":dt()}],"mask-image-y-to-pos":[{"mask-y-to":dt()}],"mask-image-y-from-color":[{"mask-y-from":C()}],"mask-image-y-to-color":[{"mask-y-to":C()}],"mask-image-radial":[{"mask-radial":[Z,V]}],"mask-image-radial-from-pos":[{"mask-radial-from":dt()}],"mask-image-radial-to-pos":[{"mask-radial-to":dt()}],"mask-image-radial-from-color":[{"mask-radial-from":C()}],"mask-image-radial-to-color":[{"mask-radial-to":C()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":ot()}],"mask-image-conic-pos":[{"mask-conic":[nt]}],"mask-image-conic-from-pos":[{"mask-conic-from":dt()}],"mask-image-conic-to-pos":[{"mask-conic-to":dt()}],"mask-image-conic-from-color":[{"mask-conic-from":C()}],"mask-image-conic-to-color":[{"mask-conic-to":C()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ht()}],"mask-repeat":[{mask:m()}],"mask-size":[{mask:D()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Z,V]}],filter:[{filter:["","none",Z,V]}],blur:[{blur:At()}],brightness:[{brightness:[nt,Z,V]}],contrast:[{contrast:[nt,Z,V]}],"drop-shadow":[{"drop-shadow":["","none",tt,ni,ai]}],"drop-shadow-color":[{"drop-shadow":C()}],grayscale:[{grayscale:["",nt,Z,V]}],"hue-rotate":[{"hue-rotate":[nt,Z,V]}],invert:[{invert:["",nt,Z,V]}],saturate:[{saturate:[nt,Z,V]}],sepia:[{sepia:["",nt,Z,V]}],"backdrop-filter":[{"backdrop-filter":["","none",Z,V]}],"backdrop-blur":[{"backdrop-blur":At()}],"backdrop-brightness":[{"backdrop-brightness":[nt,Z,V]}],"backdrop-contrast":[{"backdrop-contrast":[nt,Z,V]}],"backdrop-grayscale":[{"backdrop-grayscale":["",nt,Z,V]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[nt,Z,V]}],"backdrop-invert":[{"backdrop-invert":["",nt,Z,V]}],"backdrop-opacity":[{"backdrop-opacity":[nt,Z,V]}],"backdrop-saturate":[{"backdrop-saturate":[nt,Z,V]}],"backdrop-sepia":[{"backdrop-sepia":["",nt,Z,V]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":Y()}],"border-spacing-x":[{"border-spacing-x":Y()}],"border-spacing-y":[{"border-spacing-y":Y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Z,V]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[nt,"initial",Z,V]}],ease:[{ease:["linear","initial",st,Z,V]}],delay:[{delay:[nt,Z,V]}],animate:[{animate:["none",Tt,Z,V]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[F,Z,V]}],"perspective-origin":[{"perspective-origin":I()}],rotate:[{rotate:ne()}],"rotate-x":[{"rotate-x":ne()}],"rotate-y":[{"rotate-y":ne()}],"rotate-z":[{"rotate-z":ne()}],scale:[{scale:Pe()}],"scale-x":[{"scale-x":Pe()}],"scale-y":[{"scale-y":Pe()}],"scale-z":[{"scale-z":Pe()}],"scale-3d":["scale-3d"],skew:[{skew:tl()}],"skew-x":[{"skew-x":tl()}],"skew-y":[{"skew-y":tl()}],transform:[{transform:[Z,V,"","none","gpu","cpu"]}],"transform-origin":[{origin:I()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:el()}],"translate-x":[{"translate-x":el()}],"translate-y":[{"translate-y":el()}],"translate-z":[{"translate-z":el()}],"translate-none":["translate-none"],accent:[{accent:C()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:C()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z,V]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Y()}],"scroll-mx":[{"scroll-mx":Y()}],"scroll-my":[{"scroll-my":Y()}],"scroll-ms":[{"scroll-ms":Y()}],"scroll-me":[{"scroll-me":Y()}],"scroll-mt":[{"scroll-mt":Y()}],"scroll-mr":[{"scroll-mr":Y()}],"scroll-mb":[{"scroll-mb":Y()}],"scroll-ml":[{"scroll-ml":Y()}],"scroll-p":[{"scroll-p":Y()}],"scroll-px":[{"scroll-px":Y()}],"scroll-py":[{"scroll-py":Y()}],"scroll-ps":[{"scroll-ps":Y()}],"scroll-pe":[{"scroll-pe":Y()}],"scroll-pt":[{"scroll-pt":Y()}],"scroll-pr":[{"scroll-pr":Y()}],"scroll-pb":[{"scroll-pb":Y()}],"scroll-pl":[{"scroll-pl":Y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z,V]}],fill:[{fill:["none",...C()]}],"stroke-w":[{stroke:[nt,Yn,Kl,Df]}],stroke:[{stroke:["none",...C()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},y0=Wy(h0);function Re(...c){return y0(cm(c))}const g0=qy("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Pd({className:c,variant:s,size:d,asChild:o=!1,...y}){const x=o?Ny:"button";return R.jsx(x,{"data-slot":"button",className:Re(g0({variant:s,size:d,className:c})),...y})}function b0({className:c,type:s,...d}){return R.jsx("input",{type:s,"data-slot":"input",className:Re("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",c),...d})}function kl({className:c,...s}){return R.jsx("div",{"data-slot":"card",className:Re("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",c),...s})}function Ha({className:c,...s}){return R.jsx("div",{"data-slot":"card-header",className:Re("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",c),...s})}function qa({className:c,...s}){return R.jsx("div",{"data-slot":"card-title",className:Re("leading-none font-semibold",c),...s})}function tm({className:c,...s}){return R.jsx("div",{"data-slot":"card-description",className:Re("text-muted-foreground text-sm",c),...s})}function Jl({className:c,...s}){return R.jsx("div",{"data-slot":"card-content",className:Re("px-6",c),...s})}function Ie(c,s,{checkForDefaultPrevented:d=!0}={}){return function(y){if(c==null||c(y),d===!1||!y.defaultPrevented)return s==null?void 0:s(y)}}function Bf(c,s=[]){let d=[];function o(x,_){const U=w.createContext(_),z=d.length;d=[...d,_];const g=j=>{var st;const{scope:B,children:tt,...P}=j,F=((st=B==null?void 0:B[c])==null?void 0:st[z])||U,L=w.useMemo(()=>P,Object.values(P));return R.jsx(F.Provider,{value:L,children:tt})};g.displayName=x+"Provider";function A(j,B){var F;const tt=((F=B==null?void 0:B[c])==null?void 0:F[z])||U,P=w.useContext(tt);if(P)return P;if(_!==void 0)return _;throw new Error(`\`${j}\` must be used within \`${x}\``)}return[g,A]}const y=()=>{const x=d.map(_=>w.createContext(_));return function(U){const z=(U==null?void 0:U[c])||x;return w.useMemo(()=>({[`__scope${c}`]:{...U,[c]:z}}),[U,z])}};return y.scopeName=c,[o,p0(y,...s)]}function p0(...c){const s=c[0];if(c.length===1)return s;const d=()=>{const o=c.map(y=>({useScope:y(),scopeName:y.scopeName}));return function(x){const _=o.reduce((U,{useScope:z,scopeName:g})=>{const j=z(x)[`__scope${g}`];return{...U,...j}},{});return w.useMemo(()=>({[`__scope${s.scopeName}`]:_}),[_])}};return d.scopeName=s.scopeName,d}function S0(c){const s=c+"CollectionProvider",[d,o]=Bf(s),[y,x]=d(s,{collectionRef:{current:null},itemMap:new Map}),_=F=>{const{scope:L,children:st}=F,Tt=Ml.useRef(null),et=Ml.useRef(new Map).current;return R.jsx(y,{scope:L,itemMap:et,collectionRef:Tt,children:st})};_.displayName=s;const U=c+"CollectionSlot",z=ii(U),g=Ml.forwardRef((F,L)=>{const{scope:st,children:Tt}=F,et=x(U,st),ot=Xn(L,et.collectionRef);return R.jsx(z,{ref:ot,children:Tt})});g.displayName=U;const A=c+"CollectionItemSlot",j="data-radix-collection-item",B=ii(A),tt=Ml.forwardRef((F,L)=>{const{scope:st,children:Tt,...et}=F,ot=Ml.useRef(null),I=Xn(L,ot),bt=x(A,st);return Ml.useEffect(()=>(bt.itemMap.set(ot,{ref:ot,...et}),()=>void bt.itemMap.delete(ot))),R.jsx(B,{[j]:"",ref:I,children:Tt})});tt.displayName=A;function P(F){const L=x(c+"CollectionConsumer",F);return Ml.useCallback(()=>{const Tt=L.collectionRef.current;if(!Tt)return[];const et=Array.from(Tt.querySelectorAll(`[${j}]`));return Array.from(L.itemMap.values()).sort((bt,Mt)=>et.indexOf(bt.ref.current)-et.indexOf(Mt.ref.current))},[L.collectionRef,L.itemMap])}return[{Provider:_,Slot:g,ItemSlot:tt},P,o]}var ci=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},x0=am[" useId ".trim().toString()]||(()=>{}),T0=0;function bm(c){const[s,d]=w.useState(x0());return ci(()=>{d(o=>o??String(T0++))},[c]),c||(s?`radix-${s}`:"")}nm();var A0=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ba=A0.reduce((c,s)=>{const d=ii(`Primitive.${s}`),o=w.forwardRef((y,x)=>{const{asChild:_,...U}=y,z=_?d:s;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),R.jsx(z,{...U,ref:x})});return o.displayName=`Primitive.${s}`,{...c,[s]:o}},{});function E0(c){const s=w.useRef(c);return w.useEffect(()=>{s.current=c}),w.useMemo(()=>(...d)=>{var o;return(o=s.current)==null?void 0:o.call(s,...d)},[])}var z0=am[" useInsertionEffect ".trim().toString()]||ci;function pm({prop:c,defaultProp:s,onChange:d=()=>{},caller:o}){const[y,x,_]=_0({defaultProp:s,onChange:d}),U=c!==void 0,z=U?c:y;{const A=w.useRef(c!==void 0);w.useEffect(()=>{const j=A.current;j!==U&&console.warn(`${o} is changing from ${j?"controlled":"uncontrolled"} to ${U?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),A.current=U},[U,o])}const g=w.useCallback(A=>{var j;if(U){const B=M0(A)?A(c):A;B!==c&&((j=_.current)==null||j.call(_,B))}else x(A)},[U,c,x,_]);return[z,g]}function _0({defaultProp:c,onChange:s}){const[d,o]=w.useState(c),y=w.useRef(d),x=w.useRef(s);return z0(()=>{x.current=s},[s]),w.useEffect(()=>{var _;y.current!==d&&((_=x.current)==null||_.call(x,d),y.current=d)},[d,y]),[d,o,x]}function M0(c){return typeof c=="function"}var O0=w.createContext(void 0);function Sm(c){const s=w.useContext(O0);return c||s||"ltr"}var Uf="rovingFocusGroup.onEntryFocus",R0={bubbles:!1,cancelable:!0},Qn="RovingFocusGroup",[qf,xm,N0]=S0(Qn),[D0,Tm]=Bf(Qn,[N0]),[U0,C0]=D0(Qn),Am=w.forwardRef((c,s)=>R.jsx(qf.Provider,{scope:c.__scopeRovingFocusGroup,children:R.jsx(qf.Slot,{scope:c.__scopeRovingFocusGroup,children:R.jsx(j0,{...c,ref:s})})}));Am.displayName=Qn;var j0=w.forwardRef((c,s)=>{const{__scopeRovingFocusGroup:d,orientation:o,loop:y=!1,dir:x,currentTabStopId:_,defaultCurrentTabStopId:U,onCurrentTabStopIdChange:z,onEntryFocus:g,preventScrollOnEntryFocus:A=!1,...j}=c,B=w.useRef(null),tt=Xn(s,B),P=Sm(x),[F,L]=pm({prop:_,defaultProp:U??null,onChange:z,caller:Qn}),[st,Tt]=w.useState(!1),et=E0(g),ot=xm(d),I=w.useRef(!1),[bt,Mt]=w.useState(0);return w.useEffect(()=>{const Y=B.current;if(Y)return Y.addEventListener(Uf,et),()=>Y.removeEventListener(Uf,et)},[et]),R.jsx(U0,{scope:d,orientation:o,dir:P,loop:y,currentTabStopId:F,onItemFocus:w.useCallback(Y=>L(Y),[L]),onItemShiftTab:w.useCallback(()=>Tt(!0),[]),onFocusableItemAdd:w.useCallback(()=>Mt(Y=>Y+1),[]),onFocusableItemRemove:w.useCallback(()=>Mt(Y=>Y-1),[]),children:R.jsx(Ba.div,{tabIndex:st||bt===0?-1:0,"data-orientation":o,...j,ref:tt,style:{outline:"none",...c.style},onMouseDown:Ie(c.onMouseDown,()=>{I.current=!0}),onFocus:Ie(c.onFocus,Y=>{const Dt=!I.current;if(Y.target===Y.currentTarget&&Dt&&!st){const ve=new CustomEvent(Uf,R0);if(Y.currentTarget.dispatchEvent(ve),!ve.defaultPrevented){const Jt=ot().filter(E=>E.focusable),Rt=Jt.find(E=>E.active),he=Jt.find(E=>E.id===F),_t=[Rt,he,...Jt].filter(Boolean).map(E=>E.ref.current);_m(_t,A)}}I.current=!1}),onBlur:Ie(c.onBlur,()=>Tt(!1))})})}),Em="RovingFocusGroupItem",zm=w.forwardRef((c,s)=>{const{__scopeRovingFocusGroup:d,focusable:o=!0,active:y=!1,tabStopId:x,children:_,...U}=c,z=bm(),g=x||z,A=C0(Em,d),j=A.currentTabStopId===g,B=xm(d),{onFocusableItemAdd:tt,onFocusableItemRemove:P,currentTabStopId:F}=A;return w.useEffect(()=>{if(o)return tt(),()=>P()},[o,tt,P]),R.jsx(qf.ItemSlot,{scope:d,id:g,focusable:o,active:y,children:R.jsx(Ba.span,{tabIndex:j?0:-1,"data-orientation":A.orientation,...U,ref:s,onMouseDown:Ie(c.onMouseDown,L=>{o?A.onItemFocus(g):L.preventDefault()}),onFocus:Ie(c.onFocus,()=>A.onItemFocus(g)),onKeyDown:Ie(c.onKeyDown,L=>{if(L.key==="Tab"&&L.shiftKey){A.onItemShiftTab();return}if(L.target!==L.currentTarget)return;const st=w0(L,A.orientation,A.dir);if(st!==void 0){if(L.metaKey||L.ctrlKey||L.altKey||L.shiftKey)return;L.preventDefault();let et=B().filter(ot=>ot.focusable).map(ot=>ot.ref.current);if(st==="last")et.reverse();else if(st==="prev"||st==="next"){st==="prev"&&et.reverse();const ot=et.indexOf(L.currentTarget);et=A.loop?G0(et,ot+1):et.slice(ot+1)}setTimeout(()=>_m(et))}}),children:typeof _=="function"?_({isCurrentTabStop:j,hasTabStop:F!=null}):_})})});zm.displayName=Em;var H0={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function q0(c,s){return s!=="rtl"?c:c==="ArrowLeft"?"ArrowRight":c==="ArrowRight"?"ArrowLeft":c}function w0(c,s,d){const o=q0(c.key,d);if(!(s==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(s==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return H0[o]}function _m(c,s=!1){const d=document.activeElement;for(const o of c)if(o===d||(o.focus({preventScroll:s}),document.activeElement!==d))return}function G0(c,s){return c.map((d,o)=>c[(s+o)%c.length])}var B0=Am,Y0=zm;function X0(c,s){return w.useReducer((d,o)=>s[d][o]??d,c)}var Mm=c=>{const{present:s,children:d}=c,o=Q0(s),y=typeof d=="function"?d({present:o.isPresent}):w.Children.only(d),x=Xn(o.ref,V0(y));return typeof d=="function"||o.isPresent?w.cloneElement(y,{ref:x}):null};Mm.displayName="Presence";function Q0(c){const[s,d]=w.useState(),o=w.useRef(null),y=w.useRef(c),x=w.useRef("none"),_=c?"mounted":"unmounted",[U,z]=X0(_,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const g=ui(o.current);x.current=U==="mounted"?g:"none"},[U]),ci(()=>{const g=o.current,A=y.current;if(A!==c){const B=x.current,tt=ui(g);c?z("MOUNT"):tt==="none"||(g==null?void 0:g.display)==="none"?z("UNMOUNT"):z(A&&B!==tt?"ANIMATION_OUT":"UNMOUNT"),y.current=c}},[c,z]),ci(()=>{if(s){let g;const A=s.ownerDocument.defaultView??window,j=tt=>{const F=ui(o.current).includes(tt.animationName);if(tt.target===s&&F&&(z("ANIMATION_END"),!y.current)){const L=s.style.animationFillMode;s.style.animationFillMode="forwards",g=A.setTimeout(()=>{s.style.animationFillMode==="forwards"&&(s.style.animationFillMode=L)})}},B=tt=>{tt.target===s&&(x.current=ui(o.current))};return s.addEventListener("animationstart",B),s.addEventListener("animationcancel",j),s.addEventListener("animationend",j),()=>{A.clearTimeout(g),s.removeEventListener("animationstart",B),s.removeEventListener("animationcancel",j),s.removeEventListener("animationend",j)}}else z("ANIMATION_END")},[s,z]),{isPresent:["mounted","unmountSuspended"].includes(U),ref:w.useCallback(g=>{o.current=g?getComputedStyle(g):null,d(g)},[])}}function ui(c){return(c==null?void 0:c.animationName)||"none"}function V0(c){var o,y;let s=(o=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:o.get,d=s&&"isReactWarning"in s&&s.isReactWarning;return d?c.ref:(s=(y=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:y.get,d=s&&"isReactWarning"in s&&s.isReactWarning,d?c.props.ref:c.props.ref||c.ref)}var oi="Tabs",[Z0,P0]=Bf(oi,[Tm]),Om=Tm(),[L0,Yf]=Z0(oi),Rm=w.forwardRef((c,s)=>{const{__scopeTabs:d,value:o,onValueChange:y,defaultValue:x,orientation:_="horizontal",dir:U,activationMode:z="automatic",...g}=c,A=Sm(U),[j,B]=pm({prop:o,onChange:y,defaultProp:x??"",caller:oi});return R.jsx(L0,{scope:d,baseId:bm(),value:j,onValueChange:B,orientation:_,dir:A,activationMode:z,children:R.jsx(Ba.div,{dir:A,"data-orientation":_,...g,ref:s})})});Rm.displayName=oi;var Nm="TabsList",Dm=w.forwardRef((c,s)=>{const{__scopeTabs:d,loop:o=!0,...y}=c,x=Yf(Nm,d),_=Om(d);return R.jsx(B0,{asChild:!0,..._,orientation:x.orientation,dir:x.dir,loop:o,children:R.jsx(Ba.div,{role:"tablist","aria-orientation":x.orientation,...y,ref:s})})});Dm.displayName=Nm;var Um="TabsTrigger",Cm=w.forwardRef((c,s)=>{const{__scopeTabs:d,value:o,disabled:y=!1,...x}=c,_=Yf(Um,d),U=Om(d),z=qm(_.baseId,o),g=wm(_.baseId,o),A=o===_.value;return R.jsx(Y0,{asChild:!0,...U,focusable:!y,active:A,children:R.jsx(Ba.button,{type:"button",role:"tab","aria-selected":A,"aria-controls":g,"data-state":A?"active":"inactive","data-disabled":y?"":void 0,disabled:y,id:z,...x,ref:s,onMouseDown:Ie(c.onMouseDown,j=>{!y&&j.button===0&&j.ctrlKey===!1?_.onValueChange(o):j.preventDefault()}),onKeyDown:Ie(c.onKeyDown,j=>{[" ","Enter"].includes(j.key)&&_.onValueChange(o)}),onFocus:Ie(c.onFocus,()=>{const j=_.activationMode!=="manual";!A&&!y&&j&&_.onValueChange(o)})})})});Cm.displayName=Um;var jm="TabsContent",Hm=w.forwardRef((c,s)=>{const{__scopeTabs:d,value:o,forceMount:y,children:x,..._}=c,U=Yf(jm,d),z=qm(U.baseId,o),g=wm(U.baseId,o),A=o===U.value,j=w.useRef(A);return w.useEffect(()=>{const B=requestAnimationFrame(()=>j.current=!1);return()=>cancelAnimationFrame(B)},[]),R.jsx(Mm,{present:y||A,children:({present:B})=>R.jsx(Ba.div,{"data-state":A?"active":"inactive","data-orientation":U.orientation,role:"tabpanel","aria-labelledby":z,hidden:!B,id:g,tabIndex:0,..._,ref:s,style:{...c.style,animationDuration:j.current?"0s":void 0},children:B&&x})})});Hm.displayName=jm;function qm(c,s){return`${c}-trigger-${s}`}function wm(c,s){return`${c}-content-${s}`}var K0=Rm,k0=Dm,J0=Cm,$0=Hm;function W0({className:c,...s}){return R.jsx(K0,{"data-slot":"tabs",className:Re("flex flex-col gap-2",c),...s})}function F0({className:c,...s}){return R.jsx(k0,{"data-slot":"tabs-list",className:Re("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",c),...s})}function em({className:c,...s}){return R.jsx(J0,{"data-slot":"tabs-trigger",className:Re("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",c),...s})}function lm({className:c,...s}){return R.jsx($0,{"data-slot":"tabs-content",className:Re("flex-1 outline-none",c),...s})}function I0(){const[c,s]=w.useState(""),[d,o]=w.useState([]),[y,x]=w.useState(""),[_,U]=w.useState(!1),z=async()=>{if(c.trim()){U(!0);try{const j=await(await fetch("/api/query",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:c})})).json();j.status==="success"?x(j.data):x("查詢時發生錯誤："+j.message)}catch{x("連接服務器時發生錯誤")}U(!1)}},g=async()=>{if(c.trim()){U(!0);try{const j=await(await fetch(`/api/hts/search?keyword=${encodeURIComponent(c)}`)).json();j.status==="success"?o(j.data):o([])}catch{o([])}U(!1)}};return R.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[R.jsx("header",{className:"bg-white shadow-sm border-b",children:R.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:R.jsx("div",{className:"flex items-center justify-between",children:R.jsxs("div",{className:"flex items-center space-x-3",children:[R.jsx("div",{className:"bg-blue-600 p-2 rounded-lg",children:R.jsx(zy,{className:"h-6 w-6 text-white"})}),R.jsxs("div",{children:[R.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"TARIFFED"}),R.jsx("p",{className:"text-sm text-gray-600",children:"美國關稅查詢系統"})]})]})})})}),R.jsxs("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[R.jsxs("div",{className:"text-center mb-12",children:[R.jsx("h2",{className:"text-4xl font-bold text-gray-900 mb-4",children:"探索美國關稅與國際貿易"}),R.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"使用AI驅動的智能查詢系統，快速了解關稅政策、貿易數據和商品分類資訊"})]}),R.jsxs(kl,{className:"mb-8",children:[R.jsxs(Ha,{children:[R.jsxs(qa,{className:"flex items-center space-x-2",children:[R.jsx(Ld,{className:"h-5 w-5"}),R.jsx("span",{children:"智能查詢"})]}),R.jsx(tm,{children:"輸入您的問題或商品名稱，獲取相關的關稅和貿易資訊"})]}),R.jsx(Jl,{children:R.jsxs("div",{className:"flex space-x-2",children:[R.jsx(b0,{placeholder:"例如：絲綢的關稅率是多少？或搜尋 HTS 代碼...",value:c,onChange:A=>s(A.target.value),onKeyPress:A=>A.key==="Enter"&&z(),className:"flex-1"}),R.jsx(Pd,{onClick:z,disabled:_,children:_?"查詢中...":"AI 查詢"}),R.jsx(Pd,{variant:"outline",onClick:g,disabled:_,children:"HTS 搜尋"})]})})]}),R.jsxs(W0,{defaultValue:"ai",className:"w-full",children:[R.jsxs(F0,{className:"grid w-full grid-cols-2",children:[R.jsx(em,{value:"ai",children:"AI 回應"}),R.jsx(em,{value:"hts",children:"HTS 搜尋結果"})]}),R.jsx(lm,{value:"ai",className:"space-y-4",children:R.jsxs(kl,{children:[R.jsx(Ha,{children:R.jsx(qa,{children:"AI 助手回應"})}),R.jsx(Jl,{children:y?R.jsx("div",{className:"prose max-w-none",children:R.jsx("p",{className:"whitespace-pre-wrap",children:y})}):R.jsx("p",{className:"text-gray-500 italic",children:"請輸入您的問題並點擊「AI 查詢」來獲取回應"})})]})}),R.jsx(lm,{value:"hts",className:"space-y-4",children:d.length>0?d.map(A=>R.jsxs(kl,{children:[R.jsxs(Ha,{children:[R.jsx(qa,{className:"text-lg",children:A.id}),R.jsx(tm,{children:A.description})]}),R.jsx(Jl,{children:R.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[R.jsxs("div",{children:[R.jsx("p",{className:"font-semibold text-sm text-gray-600",children:"一般稅率"}),R.jsx("p",{className:"text-lg",children:A.general_rate})]}),R.jsxs("div",{children:[R.jsx("p",{className:"font-semibold text-sm text-gray-600",children:"特殊稅率"}),R.jsx("p",{className:"text-sm",children:A.special_rate})]}),R.jsxs("div",{children:[R.jsx("p",{className:"font-semibold text-sm text-gray-600",children:"計量單位"}),R.jsx("p",{className:"text-lg",children:A.unit_of_quantity})]})]})})]},A.id)):R.jsx(kl,{children:R.jsx(Jl,{className:"pt-6",children:R.jsx("p",{className:"text-gray-500 italic text-center",children:"請輸入關鍵字並點擊「HTS 搜尋」來查看結果"})})})})]}),R.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12",children:[R.jsxs(kl,{children:[R.jsx(Ha,{children:R.jsxs(qa,{className:"flex items-center space-x-2",children:[R.jsx(Ay,{className:"h-5 w-5 text-blue-600"}),R.jsx("span",{children:"HTS 資料庫"})]})}),R.jsx(Jl,{children:R.jsx("p",{className:"text-gray-600",children:"完整的美國協調關稅表資料庫，包含所有商品分類和稅率資訊"})})]}),R.jsxs(kl,{children:[R.jsx(Ha,{children:R.jsxs(qa,{className:"flex items-center space-x-2",children:[R.jsx(Oy,{className:"h-5 w-5 text-green-600"}),R.jsx("span",{children:"貿易分析"})]})}),R.jsx(Jl,{children:R.jsx("p",{className:"text-gray-600",children:"深入分析貿易數據，了解不同國家的進出口趨勢和關稅影響"})})]}),R.jsxs(kl,{children:[R.jsx(Ha,{children:R.jsxs(qa,{className:"flex items-center space-x-2",children:[R.jsx(Ld,{className:"h-5 w-5 text-purple-600"}),R.jsx("span",{children:"智能搜尋"})]})}),R.jsx(Jl,{children:R.jsx("p",{className:"text-gray-600",children:"使用AI技術提供自然語言查詢，快速找到您需要的關稅和貿易資訊"})})]})]})]}),R.jsx("footer",{className:"bg-white border-t mt-16",children:R.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:R.jsx("div",{className:"text-center text-gray-600",children:R.jsx("p",{children:"© 2025 TARIFFED. 基於開源技術構建的關稅查詢系統。"})})})})]})}yy.createRoot(document.getElementById("root")).render(R.jsx(w.StrictMode,{children:R.jsx(I0,{})}));
