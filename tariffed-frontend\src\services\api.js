// TARIFFED API 服務
const API_BASE_URL = 'http://localhost:5001/api';

// 基礎 API 請求函數
const apiRequest = async (endpoint, options = {}) => {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`API 請求錯誤 (${endpoint}):`, error);
    throw error;
  }
};

// HTS 相關 API
export const getHTSSubheadings = async (params = {}) => {
  const queryString = new URLSearchParams(params).toString();
  return apiRequest(`/hts/subheadings?${queryString}`);
};

export const getHTSChapters = async () => {
  return apiRequest('/hts/chapters');
};

export const getHTSSections = async () => {
  return apiRequest('/hts/sections');
};

// 查詢 AI 代理
export const queryAI = async (query, options = {}) => {
  const requestBody = {
    query,
    ...options
  };

  return apiRequest('/query', {
    method: 'POST',
    body: JSON.stringify(requestBody),
  });
};

// 外部搜尋
export const searchExternal = async (query, searchType = 'general', options = {}) => {
  const requestBody = {
    query,
    search_type: searchType,
    source: options.source || 'auto',
    max_results: options.maxResults || 5,
    ...options
  };

  return apiRequest('/search/external', {
    method: 'POST',
    body: JSON.stringify(requestBody),
  });
};

// 增強查詢（結合內部代理和外部搜尋）
export const enhancedQuery = async (query, context = {}) => {
  const requestBody = {
    query,
    context
  };

  return apiRequest('/query/enhanced', {
    method: 'POST',
    body: JSON.stringify(requestBody),
  });
};

// 協作查詢
export const collaborativeQuery = async (query, agents = null) => {
  const requestBody = {
    query,
    agents
  };

  return apiRequest('/agents/collaborative', {
    method: 'POST',
    body: JSON.stringify(requestBody),
  });
};

// 查詢特定代理
export const querySpecificAgent = async (agentName, query, context = {}) => {
  const requestBody = {
    query,
    context
  };

  return apiRequest(`/agents/${agentName}/query`, {
    method: 'POST',
    body: JSON.stringify(requestBody),
  });
};

// 獲取代理狀態
export const getAgentsStatus = async () => {
  return apiRequest('/agents/status');
};

// 貿易數據相關 API
export const getTradeData = async (params = {}) => {
  const queryString = new URLSearchParams(params).toString();
  return apiRequest(`/trade-data?${queryString}`);
};

export const getCountries = async () => {
  return apiRequest('/countries');
};

// 搜尋相關的輔助函數
export const searchTypes = {
  GENERAL: 'general',
  TARIFF: 'tariff',
  TRADE_NEWS: 'trade_news'
};

export const searchSources = {
  AUTO: 'auto',
  TAVILY: 'tavily',
  GOOGLE: 'google'
};

export const agentTypes = {
  QUERY: 'query_agent',
  ANALYSIS: 'analysis_agent',
  RECOMMENDATION: 'recommendation_agent'
};

// 搜尋歷史管理
export const searchHistory = {
  // 獲取本地搜尋歷史
  get: () => {
    try {
      const history = localStorage.getItem('tariffed_search_history');
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error('獲取搜尋歷史錯誤:', error);
      return [];
    }
  },

  // 添加搜尋記錄
  add: (query, result, type = 'general') => {
    try {
      const history = searchHistory.get();
      const newRecord = {
        id: Date.now(),
        query,
        type,
        timestamp: new Date().toISOString(),
        success: result.status === 'success',
        agent: result.metadata?.agent_used || 'unknown',
        source: result.metadata?.external_source || null
      };

      history.unshift(newRecord);
      
      // 保持歷史記錄在 50 條以內
      if (history.length > 50) {
        history.splice(50);
      }

      localStorage.setItem('tariffed_search_history', JSON.stringify(history));
    } catch (error) {
      console.error('保存搜尋歷史錯誤:', error);
    }
  },

  // 清除搜尋歷史
  clear: () => {
    try {
      localStorage.removeItem('tariffed_search_history');
    } catch (error) {
      console.error('清除搜尋歷史錯誤:', error);
    }
  },

  // 刪除特定記錄
  remove: (id) => {
    try {
      const history = searchHistory.get();
      const filteredHistory = history.filter(record => record.id !== id);
      localStorage.setItem('tariffed_search_history', JSON.stringify(filteredHistory));
    } catch (error) {
      console.error('刪除搜尋記錄錯誤:', error);
    }
  }
};

// 用戶偏好設置
export const userPreferences = {
  // 獲取用戶偏好
  get: () => {
    try {
      const prefs = localStorage.getItem('tariffed_user_preferences');
      return prefs ? JSON.parse(prefs) : {
        defaultSearchType: searchTypes.GENERAL,
        defaultSearchSource: searchSources.AUTO,
        enableExternalSearch: true,
        enableCollaborativeMode: false,
        maxSearchResults: 5,
        theme: 'light'
      };
    } catch (error) {
      console.error('獲取用戶偏好錯誤:', error);
      return {};
    }
  },

  // 保存用戶偏好
  set: (preferences) => {
    try {
      const currentPrefs = userPreferences.get();
      const updatedPrefs = { ...currentPrefs, ...preferences };
      localStorage.setItem('tariffed_user_preferences', JSON.stringify(updatedPrefs));
    } catch (error) {
      console.error('保存用戶偏好錯誤:', error);
    }
  }
};

// API 狀態檢查
export const checkAPIStatus = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/agents/status`);
    return {
      online: response.ok,
      status: response.status,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      online: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

// 錯誤處理輔助函數
export const handleAPIError = (error, context = '') => {
  console.error(`API 錯誤 ${context}:`, error);
  
  if (error.message.includes('Failed to fetch')) {
    return {
      type: 'network',
      message: '網路連接錯誤，請檢查網路連接或稍後再試。'
    };
  } else if (error.message.includes('HTTP error! status: 500')) {
    return {
      type: 'server',
      message: '服務器內部錯誤，請稍後再試。'
    };
  } else if (error.message.includes('HTTP error! status: 404')) {
    return {
      type: 'not_found',
      message: '請求的資源不存在。'
    };
  } else {
    return {
      type: 'unknown',
      message: `發生未知錯誤：${error.message}`
    };
  }
};

export default {
  // HTS API
  getHTSSubheadings,
  getHTSChapters,
  getHTSSections,
  
  // AI 查詢 API
  queryAI,
  searchExternal,
  enhancedQuery,
  collaborativeQuery,
  querySpecificAgent,
  getAgentsStatus,
  
  // 貿易數據 API
  getTradeData,
  getCountries,
  
  // 常量
  searchTypes,
  searchSources,
  agentTypes,
  
  // 輔助功能
  searchHistory,
  userPreferences,
  checkAPIStatus,
  handleAPIError
};
