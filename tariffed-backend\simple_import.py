#!/usr/bin/env python3

import os
import sys
sys.path.insert(0, os.path.dirname(__file__))

from flask import Flask
from src.models.hts import db, HTSSection, HTSChapter, HTSHeading, HTSSubheading, Country, TradeData

def create_sample_data():
    # 創建 Flask 應用
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///src/database/app.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    
    with app.app_context():
        # 創建所有表
        db.create_all()
        
        # 檢查是否已有資料
        if HTSSection.query.count() > 0:
            print("資料庫已有資料，跳過導入")
            return
        
        # 創建範例資料
        
        # Section
        section1 = HTSSection(id=1, title="Live Animals; Animal Products")
        section2 = HTSSection(id=2, title="Vegetable Products")
        section16 = HTSSection(id=16, title="Machinery and Mechanical Appliances; Electrical Equipment")
        section17 = HTSSection(id=17, title="Vehicles, Aircraft, Vessels and Associated Transport Equipment")
        
        db.session.add_all([section1, section2, section16, section17])
        
        # Chapter
        chapter1 = HTSChapter(id=1, title="Live Animals", section_id=1)
        chapter2 = HTSChapter(id=2, title="Meat and Edible Meat Offal", section_id=1)
        chapter7 = HTSChapter(id=7, title="Edible Vegetables and Certain Roots and Tubers", section_id=2)
        chapter8 = HTSChapter(id=8, title="Edible Fruit and Nuts; Peel of Citrus Fruit or Melons", section_id=2)
        chapter84 = HTSChapter(id=84, title="Nuclear Reactors, Boilers, Machinery and Mechanical Appliances", section_id=16)
        chapter87 = HTSChapter(id=87, title="Vehicles Other than Railway or Tramway Rolling Stock", section_id=17)
        
        db.session.add_all([chapter1, chapter2, chapter7, chapter8, chapter84, chapter87])
        
        # Heading
        heading0101 = HTSHeading(id="0101", description="Live horses, asses, mules and hinnies", chapter_id=1)
        heading0201 = HTSHeading(id="0201", description="Meat of bovine animals, fresh or chilled", chapter_id=2)
        heading0701 = HTSHeading(id="0701", description="Potatoes, fresh or chilled", chapter_id=7)
        heading0803 = HTSHeading(id="0803", description="Bananas, including plantains, fresh or dried", chapter_id=8)
        heading8471 = HTSHeading(id="8471", description="Automatic data processing machines and units thereof", chapter_id=84)
        heading8703 = HTSHeading(id="8703", description="Motor cars and other motor vehicles principally designed for the transport of persons", chapter_id=87)
        
        db.session.add_all([heading0101, heading0201, heading0701, heading0803, heading8471, heading8703])
        
        # Subheading
        subheadings = [
            HTSSubheading(
                id="0101.21.00",
                description="Horses: Pure bred breeding animals",
                unit_of_quantity="No.",
                general_rate="Free",
                special_rate="Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
                column_2_rate="Free",
                heading_id="0101"
            ),
            HTSSubheading(
                id="0101.29.00",
                description="Horses: Other",
                unit_of_quantity="No.",
                general_rate="Free",
                special_rate="Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
                column_2_rate="Free",
                heading_id="0101"
            ),
            HTSSubheading(
                id="0201.10.05",
                description="Carcasses and half-carcasses: High quality beef cuts",
                unit_of_quantity="kg",
                general_rate="4.4¢/kg",
                special_rate="Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
                column_2_rate="30.8¢/kg",
                heading_id="0201"
            ),
            HTSSubheading(
                id="0701.10.00",
                description="Seed",
                unit_of_quantity="kg",
                general_rate="0.5¢/kg",
                special_rate="Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
                column_2_rate="2.8¢/kg",
                heading_id="0701"
            ),
            HTSSubheading(
                id="0803.10.00",
                description="Plantains",
                unit_of_quantity="kg",
                general_rate="Free",
                special_rate="Free",
                column_2_rate="Free",
                heading_id="0803"
            ),
            HTSSubheading(
                id="8471.30.01",
                description="Portable automatic data processing machines, weighing not more than 10 kg",
                unit_of_quantity="No.",
                general_rate="Free",
                special_rate="Free",
                column_2_rate="35%",
                heading_id="8471"
            ),
            HTSSubheading(
                id="8703.21.00",
                description="Other vehicles, with spark-ignition internal combustion reciprocating piston engine: Of a cylinder capacity not exceeding 1,000 cc",
                unit_of_quantity="No.",
                general_rate="2.5%",
                special_rate="Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
                column_2_rate="10%",
                heading_id="8703"
            )
        ]
        
        db.session.add_all(subheadings)
        
        # Countries
        countries = [
            Country(id="CN", name="China"),
            Country(id="CA", name="Canada"),
            Country(id="MX", name="Mexico"),
            Country(id="JP", name="Japan"),
            Country(id="DE", name="Germany"),
            Country(id="GB", name="United Kingdom"),
            Country(id="FR", name="France"),
            Country(id="IT", name="Italy"),
            Country(id="BR", name="Brazil"),
            Country(id="KR", name="Korea, Republic of"),
            Country(id="US", name="United States")
        ]
        
        db.session.add_all(countries)
        
        # 提交所有資料
        db.session.commit()
        
        # 統計結果
        section_count = HTSSection.query.count()
        chapter_count = HTSChapter.query.count()
        heading_count = HTSHeading.query.count()
        subheading_count = HTSSubheading.query.count()
        country_count = Country.query.count()
        
        print(f"資料導入完成:")
        print(f"  Sections: {section_count}")
        print(f"  Chapters: {chapter_count}")
        print(f"  Headings: {heading_count}")
        print(f"  Subheadings: {subheading_count}")
        print(f"  Countries: {country_count}")

if __name__ == "__main__":
    create_sample_data()
