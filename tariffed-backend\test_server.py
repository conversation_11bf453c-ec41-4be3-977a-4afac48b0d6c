#!/usr/bin/env python3

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

try:
    print("Testing imports...")
    from flask import Flask
    print("✓ Flask imported successfully")
    
    from flask_cors import CORS
    print("✓ Flask-CORS imported successfully")
    
    from flasgger import Swagger
    print("✓ Flasgger imported successfully")
    
    print("\nTesting database models...")
    from src.models.hts import db
    print("✓ Database models imported successfully")
    
    print("\nTesting routes...")
    from src.routes.tariff import tariff_bp
    print("✓ Routes imported successfully")
    
    print("\nCreating Flask app...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-key'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    CORS(app)
    
    # Simple Swagger config
    swagger = Swagger(app)
    
    db.init_app(app)
    app.register_blueprint(tariff_bp, url_prefix='/api')
    
    print("✓ Flask app created successfully")
    
    @app.route('/test')
    def test():
        return {'status': 'success', 'message': 'Server is running!'}
    
    print("\nStarting server on http://localhost:5001")
    print("API Documentation available at: http://localhost:5001/apidocs/")
    print("Test endpoint: http://localhost:5001/test")
    
    app.run(host='0.0.0.0', port=5001, debug=True)
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
