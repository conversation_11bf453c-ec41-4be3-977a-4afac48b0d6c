#!/usr/bin/env python3
"""
資料視覺化服務 - 生成圖表配置和統計分析
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import statistics
import math

logger = logging.getLogger(__name__)

class VisualizationService:
    """資料視覺化服務"""
    
    def __init__(self):
        """初始化視覺化服務"""
        self.chart_types = {
            'line': 'Line Chart',
            'bar': 'Bar Chart',
            'pie': 'Pie Chart',
            'area': 'Area Chart',
            'scatter': 'Scatter Plot',
            'histogram': 'Histogram',
            'box': 'Box Plot',
            'heatmap': 'Heat Map',
            'treemap': 'Tree Map',
            'radar': 'Radar Chart',
            'gauge': 'Gauge Chart',
            'funnel': 'Funnel Chart'
        }
        
        self.color_schemes = {
            'default': ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899'],
            'professional': ['#1E40AF', '#DC2626', '#059669', '#D97706', '#7C3AED', '#DB2777'],
            'trade': ['#0F766E', '#B91C1C', '#1D4ED8', '#CA8A04', '#9333EA', '#C2410C'],
            'government': ['#1F2937', '#374151', '#4B5563', '#6B7280', '#9CA3AF', '#D1D5DB']
        }
        
        logger.info("📊 視覺化服務已初始化")
    
    def generate_trade_trend_chart(self, data: List[Dict[str, Any]], 
                                 chart_type: str = 'line') -> Dict[str, Any]:
        """
        生成貿易趨勢圖表配置
        
        Args:
            data: 貿易數據列表
            chart_type: 圖表類型
            
        Returns:
            圖表配置字典
        """
        try:
            if not data:
                return self._generate_empty_chart_config("沒有可用的貿易數據")
            
            # 處理數據
            processed_data = self._process_trade_data(data)
            
            # 生成圖表配置
            if chart_type == 'line':
                config = self._generate_line_chart_config(processed_data, "貿易趨勢分析")
            elif chart_type == 'bar':
                config = self._generate_bar_chart_config(processed_data, "貿易數據比較")
            elif chart_type == 'area':
                config = self._generate_area_chart_config(processed_data, "貿易趨勢區域圖")
            else:
                config = self._generate_line_chart_config(processed_data, "貿易趨勢分析")
            
            # 添加統計信息
            config['statistics'] = self._calculate_trend_statistics(processed_data)
            
            return config
            
        except Exception as e:
            logger.error(f"❌ 生成貿易趨勢圖表錯誤: {e}")
            return self._generate_error_chart_config(str(e))
    
    def generate_tariff_comparison_chart(self, tariff_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成關稅比較圖表
        
        Args:
            tariff_data: 關稅數據列表
            
        Returns:
            圖表配置字典
        """
        try:
            if not tariff_data:
                return self._generate_empty_chart_config("沒有可用的關稅數據")
            
            # 處理關稅數據
            processed_data = self._process_tariff_data(tariff_data)
            
            # 生成橫向柱狀圖配置
            config = {
                'type': 'bar',
                'title': '關稅稅率比較',
                'subtitle': f'比較 {len(processed_data)} 個商品類別的關稅稅率',
                'data': {
                    'labels': [item['label'] for item in processed_data],
                    'datasets': [{
                        'label': '一般稅率 (%)',
                        'data': [item['general_rate'] for item in processed_data],
                        'backgroundColor': self.color_schemes['trade'][0],
                        'borderColor': self.color_schemes['trade'][0],
                        'borderWidth': 1
                    }, {
                        'label': '特殊稅率 (%)',
                        'data': [item['special_rate'] for item in processed_data],
                        'backgroundColor': self.color_schemes['trade'][1],
                        'borderColor': self.color_schemes['trade'][1],
                        'borderWidth': 1
                    }]
                },
                'options': {
                    'responsive': True,
                    'indexAxis': 'y',  # 橫向柱狀圖
                    'plugins': {
                        'legend': {
                            'position': 'top'
                        },
                        'tooltip': {
                            'callbacks': {
                                'label': 'function(context) { return context.dataset.label + ": " + context.parsed.x + "%"; }'
                            }
                        }
                    },
                    'scales': {
                        'x': {
                            'beginAtZero': True,
                            'title': {
                                'display': True,
                                'text': '稅率 (%)'
                            }
                        },
                        'y': {
                            'title': {
                                'display': True,
                                'text': '商品類別'
                            }
                        }
                    }
                },
                'statistics': self._calculate_tariff_statistics(processed_data)
            }
            
            return config
            
        except Exception as e:
            logger.error(f"❌ 生成關稅比較圖表錯誤: {e}")
            return self._generate_error_chart_config(str(e))
    
    def generate_country_market_share_chart(self, country_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成國家市場份額圓餅圖
        
        Args:
            country_data: 國家貿易數據
            
        Returns:
            圖表配置字典
        """
        try:
            if not country_data:
                return self._generate_empty_chart_config("沒有可用的國家數據")
            
            # 處理國家數據
            processed_data = self._process_country_data(country_data)
            
            # 生成圓餅圖配置
            config = {
                'type': 'pie',
                'title': '主要貿易夥伴市場份額',
                'subtitle': f'基於 {len(processed_data)} 個國家的貿易數據',
                'data': {
                    'labels': [item['country'] for item in processed_data],
                    'datasets': [{
                        'data': [item['value'] for item in processed_data],
                        'backgroundColor': self.color_schemes['professional'][:len(processed_data)],
                        'borderColor': '#FFFFFF',
                        'borderWidth': 2
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'legend': {
                            'position': 'right'
                        },
                        'tooltip': {
                            'callbacks': {
                                'label': 'function(context) { return context.label + ": $" + context.parsed.toLocaleString() + " (" + ((context.parsed / context.dataset.data.reduce((a,b) => a + b, 0)) * 100).toFixed(1) + "%)"; }'
                            }
                        }
                    }
                },
                'statistics': self._calculate_market_share_statistics(processed_data)
            }
            
            return config
            
        except Exception as e:
            logger.error(f"❌ 生成市場份額圖表錯誤: {e}")
            return self._generate_error_chart_config(str(e))
    
    def generate_hts_category_analysis(self, hts_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成 HTS 類別分析圖表
        
        Args:
            hts_data: HTS 分類數據
            
        Returns:
            圖表配置字典
        """
        try:
            if not hts_data:
                return self._generate_empty_chart_config("沒有可用的 HTS 數據")
            
            # 處理 HTS 數據
            processed_data = self._process_hts_data(hts_data)
            
            # 生成樹狀圖配置
            config = {
                'type': 'treemap',
                'title': 'HTS 商品類別分析',
                'subtitle': f'基於 {len(processed_data)} 個 HTS 類別',
                'data': {
                    'datasets': [{
                        'label': 'HTS 類別',
                        'data': processed_data,
                        'backgroundColor': function(context) {
                            const colors = self.color_schemes['trade']
                            return colors[context.dataIndex % colors.length]
                        },
                        'borderColor': '#FFFFFF',
                        'borderWidth': 1
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'legend': {
                            'display': False
                        },
                        'tooltip': {
                            'callbacks': {
                                'title': 'function(context) { return context[0].raw.label; }',
                                'label': 'function(context) { return "進口值: $" + context.raw.value.toLocaleString(); }'
                            }
                        }
                    }
                },
                'statistics': self._calculate_hts_statistics(processed_data)
            }
            
            return config
            
        except Exception as e:
            logger.error(f"❌ 生成 HTS 分析圖表錯誤: {e}")
            return self._generate_error_chart_config(str(e))
    
    def generate_statistical_dashboard(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成統計儀表板配置
        
        Args:
            data: 綜合數據
            
        Returns:
            儀表板配置字典
        """
        try:
            dashboard = {
                'title': 'TARIFFED 貿易數據統計儀表板',
                'timestamp': datetime.now().isoformat(),
                'charts': [],
                'kpis': [],
                'summary': {}
            }
            
            # 生成 KPI 指標
            if 'trade_data' in data:
                dashboard['kpis'].extend(self._generate_trade_kpis(data['trade_data']))
            
            if 'tariff_data' in data:
                dashboard['kpis'].extend(self._generate_tariff_kpis(data['tariff_data']))
            
            # 生成圖表
            if 'trend_data' in data:
                trend_chart = self.generate_trade_trend_chart(data['trend_data'])
                dashboard['charts'].append(trend_chart)
            
            if 'country_data' in data:
                market_chart = self.generate_country_market_share_chart(data['country_data'])
                dashboard['charts'].append(market_chart)
            
            if 'tariff_comparison' in data:
                tariff_chart = self.generate_tariff_comparison_chart(data['tariff_comparison'])
                dashboard['charts'].append(tariff_chart)
            
            # 生成摘要統計
            dashboard['summary'] = self._generate_dashboard_summary(data)
            
            return dashboard
            
        except Exception as e:
            logger.error(f"❌ 生成統計儀表板錯誤: {e}")
            return {
                'title': '統計儀表板',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _process_trade_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """處理貿易數據"""
        processed = []
        for item in data:
            processed.append({
                'x': item.get('date', item.get('year', datetime.now().year)),
                'y': item.get('value', item.get('import_value', 0)),
                'label': item.get('label', f"數據點 {len(processed) + 1}")
            })
        return processed
    
    def _process_tariff_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """處理關稅數據"""
        processed = []
        for item in data:
            general_rate = self._parse_tariff_rate(item.get('general_rate', '0%'))
            special_rate = self._parse_tariff_rate(item.get('special_rate', '0%'))
            
            processed.append({
                'label': item.get('hts_code', item.get('description', f"項目 {len(processed) + 1}")),
                'general_rate': general_rate,
                'special_rate': special_rate,
                'description': item.get('description', '')
            })
        return processed
    
    def _process_country_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """處理國家數據"""
        processed = []
        for item in data:
            processed.append({
                'country': item.get('country', f"國家 {len(processed) + 1}"),
                'value': item.get('import_value', item.get('value', 0)),
                'percentage': item.get('percentage', 0)
            })
        
        # 按價值排序
        processed.sort(key=lambda x: x['value'], reverse=True)
        return processed[:10]  # 只取前10個
    
    def _process_hts_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """處理 HTS 數據"""
        processed = []
        for item in data:
            processed.append({
                'label': item.get('hts_code', f"HTS {len(processed) + 1}"),
                'value': item.get('import_value', item.get('value', 0)),
                'description': item.get('description', '')
            })
        return processed
    
    def _parse_tariff_rate(self, rate_str: str) -> float:
        """解析關稅稅率字符串"""
        try:
            if isinstance(rate_str, (int, float)):
                return float(rate_str)
            
            if 'free' in rate_str.lower():
                return 0.0
            
            # 提取百分比
            import re
            match = re.search(r'(\d+(?:\.\d+)?)', str(rate_str))
            if match:
                return float(match.group(1))
            
            return 0.0
        except:
            return 0.0
    
    def _generate_line_chart_config(self, data: List[Dict[str, Any]], title: str) -> Dict[str, Any]:
        """生成線圖配置"""
        return {
            'type': 'line',
            'title': title,
            'data': {
                'labels': [item['x'] for item in data],
                'datasets': [{
                    'label': '進口值',
                    'data': [item['y'] for item in data],
                    'borderColor': self.color_schemes['trade'][0],
                    'backgroundColor': self.color_schemes['trade'][0] + '20',
                    'fill': False,
                    'tension': 0.4
                }]
            },
            'options': {
                'responsive': True,
                'plugins': {
                    'legend': {
                        'position': 'top'
                    }
                },
                'scales': {
                    'y': {
                        'beginAtZero': True,
                        'title': {
                            'display': True,
                            'text': '進口值 (USD)'
                        }
                    }
                }
            }
        }
    
    def _generate_bar_chart_config(self, data: List[Dict[str, Any]], title: str) -> Dict[str, Any]:
        """生成柱狀圖配置"""
        return {
            'type': 'bar',
            'title': title,
            'data': {
                'labels': [item['x'] for item in data],
                'datasets': [{
                    'label': '進口值',
                    'data': [item['y'] for item in data],
                    'backgroundColor': self.color_schemes['trade'][0],
                    'borderColor': self.color_schemes['trade'][0],
                    'borderWidth': 1
                }]
            },
            'options': {
                'responsive': True,
                'plugins': {
                    'legend': {
                        'position': 'top'
                    }
                },
                'scales': {
                    'y': {
                        'beginAtZero': True,
                        'title': {
                            'display': True,
                            'text': '進口值 (USD)'
                        }
                    }
                }
            }
        }
    
    def _generate_area_chart_config(self, data: List[Dict[str, Any]], title: str) -> Dict[str, Any]:
        """生成區域圖配置"""
        config = self._generate_line_chart_config(data, title)
        config['type'] = 'line'
        config['data']['datasets'][0]['fill'] = True
        config['data']['datasets'][0]['backgroundColor'] = self.color_schemes['trade'][0] + '40'
        return config

    def _calculate_trend_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """計算趋勢統計"""
        if not data:
            return {}

        values = [item['y'] for item in data]

        try:
            stats = {
                'total_points': len(values),
                'min_value': min(values),
                'max_value': max(values),
                'mean_value': statistics.mean(values),
                'median_value': statistics.median(values),
                'std_deviation': statistics.stdev(values) if len(values) > 1 else 0,
                'growth_rate': self._calculate_growth_rate(values),
                'trend_direction': self._determine_trend_direction(values),
                'volatility': self._calculate_volatility(values)
            }

            return stats
        except Exception as e:
            logger.error(f"❌ 計算趨勢統計錯誤: {e}")
            return {}

    def _calculate_tariff_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """計算關稅統計"""
        if not data:
            return {}

        general_rates = [item['general_rate'] for item in data]
        special_rates = [item['special_rate'] for item in data]

        try:
            stats = {
                'total_categories': len(data),
                'general_rate_avg': statistics.mean(general_rates),
                'general_rate_max': max(general_rates),
                'general_rate_min': min(general_rates),
                'special_rate_avg': statistics.mean(special_rates),
                'special_rate_max': max(special_rates),
                'special_rate_min': min(special_rates),
                'free_trade_count': sum(1 for rate in general_rates if rate == 0),
                'high_tariff_count': sum(1 for rate in general_rates if rate > 10),
                'average_savings': statistics.mean([g - s for g, s in zip(general_rates, special_rates)])
            }

            return stats
        except Exception as e:
            logger.error(f"❌ 計算關稅統計錯誤: {e}")
            return {}

    def _calculate_market_share_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """計算市場份額統計"""
        if not data:
            return {}

        values = [item['value'] for item in data]
        total_value = sum(values)

        try:
            stats = {
                'total_countries': len(data),
                'total_trade_value': total_value,
                'top_country': data[0]['country'] if data else 'N/A',
                'top_country_share': (values[0] / total_value * 100) if total_value > 0 else 0,
                'concentration_ratio_top3': sum(values[:3]) / total_value * 100 if total_value > 0 else 0,
                'concentration_ratio_top5': sum(values[:5]) / total_value * 100 if total_value > 0 else 0,
                'hhi_index': self._calculate_hhi_index(values, total_value)
            }

            return stats
        except Exception as e:
            logger.error(f"❌ 計算市場份額統計錯誤: {e}")
            return {}

    def _calculate_hts_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """計算 HTS 統計"""
        if not data:
            return {}

        values = [item['value'] for item in data]
        total_value = sum(values)

        try:
            stats = {
                'total_categories': len(data),
                'total_import_value': total_value,
                'average_category_value': statistics.mean(values),
                'largest_category': data[0]['label'] if data else 'N/A',
                'largest_category_value': max(values) if values else 0,
                'smallest_category_value': min(values) if values else 0,
                'diversity_index': self._calculate_diversity_index(values)
            }

            return stats
        except Exception as e:
            logger.error(f"❌ 計算 HTS 統計錯誤: {e}")
            return {}

    def _calculate_growth_rate(self, values: List[float]) -> float:
        """計算增長率"""
        if len(values) < 2:
            return 0.0

        try:
            start_value = values[0]
            end_value = values[-1]

            if start_value == 0:
                return 0.0

            periods = len(values) - 1
            growth_rate = ((end_value / start_value) ** (1 / periods) - 1) * 100

            return round(growth_rate, 2)
        except:
            return 0.0

    def _determine_trend_direction(self, values: List[float]) -> str:
        """確定趨勢方向"""
        if len(values) < 2:
            return 'stable'

        try:
            # 計算線性回歸斜率
            n = len(values)
            x = list(range(n))

            sum_x = sum(x)
            sum_y = sum(values)
            sum_xy = sum(x[i] * values[i] for i in range(n))
            sum_x2 = sum(x[i] ** 2 for i in range(n))

            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)

            if slope > 0.1:
                return 'increasing'
            elif slope < -0.1:
                return 'decreasing'
            else:
                return 'stable'
        except:
            return 'unknown'

    def _calculate_volatility(self, values: List[float]) -> float:
        """計算波動性"""
        if len(values) < 2:
            return 0.0

        try:
            mean_val = statistics.mean(values)
            variance = sum((x - mean_val) ** 2 for x in values) / len(values)
            volatility = math.sqrt(variance) / mean_val * 100 if mean_val != 0 else 0

            return round(volatility, 2)
        except:
            return 0.0

    def _calculate_hhi_index(self, values: List[float], total: float) -> float:
        """計算 HHI 指數（市場集中度）"""
        if total == 0:
            return 0.0

        try:
            hhi = sum((value / total * 100) ** 2 for value in values)
            return round(hhi, 2)
        except:
            return 0.0

    def _calculate_diversity_index(self, values: List[float]) -> float:
        """計算多樣性指數"""
        if not values:
            return 0.0

        try:
            total = sum(values)
            if total == 0:
                return 0.0

            # Shannon 多樣性指數
            diversity = -sum((v / total) * math.log(v / total) for v in values if v > 0)
            return round(diversity, 3)
        except:
            return 0.0

    def _generate_trade_kpis(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成貿易 KPI 指標"""
        if not data:
            return []

        values = [item.get('value', 0) for item in data]
        total_value = sum(values)

        kpis = [
            {
                'title': '總進口值',
                'value': f'${total_value:,.0f}',
                'type': 'currency',
                'trend': self._determine_trend_direction(values),
                'icon': 'dollar-sign'
            },
            {
                'title': '平均月進口值',
                'value': f'${statistics.mean(values):,.0f}' if values else '$0',
                'type': 'currency',
                'trend': 'stable',
                'icon': 'trending-up'
            },
            {
                'title': '數據期間',
                'value': f'{len(data)} 個月',
                'type': 'count',
                'trend': 'stable',
                'icon': 'calendar'
            }
        ]

        return kpis

    def _generate_tariff_kpis(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成關稅 KPI 指標"""
        if not data:
            return []

        general_rates = [self._parse_tariff_rate(item.get('general_rate', '0%')) for item in data]
        free_count = sum(1 for rate in general_rates if rate == 0)

        kpis = [
            {
                'title': '平均關稅稅率',
                'value': f'{statistics.mean(general_rates):.1f}%' if general_rates else '0%',
                'type': 'percentage',
                'trend': 'stable',
                'icon': 'percent'
            },
            {
                'title': '免稅商品數量',
                'value': str(free_count),
                'type': 'count',
                'trend': 'stable',
                'icon': 'gift'
            },
            {
                'title': '商品類別總數',
                'value': str(len(data)),
                'type': 'count',
                'trend': 'stable',
                'icon': 'package'
            }
        ]

        return kpis

    def _generate_dashboard_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成儀表板摘要"""
        summary = {
            'generated_at': datetime.now().isoformat(),
            'data_sources': list(data.keys()),
            'total_charts': 0,
            'key_insights': []
        }

        # 添加關鍵洞察
        if 'trade_data' in data and data['trade_data']:
            trade_values = [item.get('value', 0) for item in data['trade_data']]
            if trade_values:
                trend = self._determine_trend_direction(trade_values)
                growth = self._calculate_growth_rate(trade_values)

                if trend == 'increasing':
                    summary['key_insights'].append(f"貿易呈現增長趨勢，增長率為 {growth:.1f}%")
                elif trend == 'decreasing':
                    summary['key_insights'].append(f"貿易呈現下降趨勢，下降率為 {abs(growth):.1f}%")
                else:
                    summary['key_insights'].append("貿易保持相對穩定")

        return summary

    def _generate_empty_chart_config(self, message: str) -> Dict[str, Any]:
        """生成空圖表配置"""
        return {
            'type': 'empty',
            'title': '暫無數據',
            'message': message,
            'timestamp': datetime.now().isoformat()
        }

    def _generate_error_chart_config(self, error: str) -> Dict[str, Any]:
        """生成錯誤圖表配置"""
        return {
            'type': 'error',
            'title': '圖表生成錯誤',
            'error': error,
            'timestamp': datetime.now().isoformat()
        }

# 全局視覺化服務實例
visualization_service = VisualizationService()
