#!/usr/bin/env python3
"""
簡化的 HTS 資料導入執行腳本
"""

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

def main():
    try:
        print("🌟 開始執行 HTS 資料導入...")
        
        from hts_data_importer import HTSDataImporter
        print("✅ 導入工具載入成功")
        
        importer = HTSDataImporter()
        print("✅ 導入器初始化成功")
        
        success = importer.run_import()
        
        if success:
            print("\n🎉 HTS 資料導入完成！")
        else:
            print("\n❌ HTS 資料導入失敗")
            
    except Exception as e:
        print(f"❌ 執行錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
