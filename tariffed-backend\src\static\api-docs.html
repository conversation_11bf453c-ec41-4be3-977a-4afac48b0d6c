<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TARIFFED API 文檔</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .api-section {
            margin-bottom: 40px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
        }
        .api-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e5e9;
        }
        .api-method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            margin-right: 10px;
        }
        .method-post { background: #28a745; color: white; }
        .method-get { background: #007bff; color: white; }
        .api-content {
            padding: 20px;
        }
        .endpoint {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .response {
            background: #d4edda;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        .status-200 { background: #d4edda; color: #155724; }
        .status-400 { background: #f8d7da; color: #721c24; }
        .status-500 { background: #f8d7da; color: #721c24; }
        .nav-links {
            text-align: center;
            margin-bottom: 30px;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚢 TARIFFED API 文檔</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            美國關稅查詢系統 API - 基於 AI 的關稅查詢和分析系統
        </p>
        
        <div class="nav-links">
            <a href="/">返回首頁</a>
            <a href="/test">測試端點</a>
            <a href="/api/test">API 測試</a>
        </div>

        <!-- AI 查詢 API -->
        <div class="api-section">
            <div class="api-header">
                <span class="api-method method-post">POST</span>
                <strong>AI 自然語言查詢</strong>
            </div>
            <div class="api-content">
                <div class="endpoint">/api/query</div>
                <p><strong>描述：</strong>處理使用者的自然語言查詢，返回 AI 分析結果</p>
                
                <h4>請求參數：</h4>
                <div class="example">
                    <pre>{
  "query": "中國進口的馬匹關稅是多少？"
}</pre>
                </div>
                
                <h4>回應範例：</h4>
                <div class="response">
                    <span class="status-badge status-200">200 OK</span>
                    <pre>{
  "status": "success",
  "data": "根據 HTS 條目 0101.21.00，中國進口的純種繁殖馬匹關稅為免稅..."
}</pre>
                </div>
            </div>
        </div>

        <!-- HTS 搜尋 API -->
        <div class="api-section">
            <div class="api-header">
                <span class="api-method method-get">GET</span>
                <strong>HTS 條目搜尋</strong>
            </div>
            <div class="api-content">
                <div class="endpoint">/api/hts/search?keyword=horse</div>
                <p><strong>描述：</strong>根據關鍵字搜尋 HTS 條目</p>
                
                <h4>查詢參數：</h4>
                <ul>
                    <li><code>keyword</code> (必填): 搜尋關鍵字</li>
                </ul>
                
                <h4>回應範例：</h4>
                <div class="response">
                    <span class="status-badge status-200">200 OK</span>
                    <pre>{
  "status": "success",
  "data": [
    {
      "id": "0101.21.00",
      "description": "Horses: Pure bred breeding animals",
      "unit_of_quantity": "No.",
      "general_rate": "Free",
      "special_rate": "Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
      "column_2_rate": "Free"
    }
  ]
}</pre>
                </div>
            </div>
        </div>

        <!-- HTS 詳細資訊 API -->
        <div class="api-section">
            <div class="api-header">
                <span class="api-method method-get">GET</span>
                <strong>HTS 條目詳細資訊</strong>
            </div>
            <div class="api-content">
                <div class="endpoint">/api/hts/{hts_id}</div>
                <p><strong>描述：</strong>獲取特定 HTS 條目的詳細資訊，包含層級結構</p>
                
                <h4>路徑參數：</h4>
                <ul>
                    <li><code>hts_id</code>: HTS 條目 ID (例如: 0101.21.00)</li>
                </ul>
                
                <h4>回應範例：</h4>
                <div class="response">
                    <span class="status-badge status-200">200 OK</span>
                    <pre>{
  "status": "success",
  "data": {
    "id": "0101.21.00",
    "description": "Horses: Pure bred breeding animals",
    "unit_of_quantity": "No.",
    "general_rate": "Free",
    "special_rate": "Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
    "column_2_rate": "Free",
    "heading": {
      "id": "0101",
      "description": "Live horses, asses, mules and hinnies"
    },
    "chapter": {
      "id": 1,
      "title": "Live Animals"
    },
    "section": {
      "id": 1,
      "title": "Live Animals; Animal Products"
    }
  }
}</pre>
                </div>
            </div>
        </div>

        <!-- 國家列表 API -->
        <div class="api-section">
            <div class="api-header">
                <span class="api-method method-get">GET</span>
                <strong>國家列表</strong>
            </div>
            <div class="api-content">
                <div class="endpoint">/api/countries</div>
                <p><strong>描述：</strong>獲取所有國家列表</p>
                
                <h4>回應範例：</h4>
                <div class="response">
                    <span class="status-badge status-200">200 OK</span>
                    <pre>{
  "status": "success",
  "data": [
    {
      "id": "CN",
      "name": "China"
    },
    {
      "id": "US",
      "name": "United States"
    }
  ]
}</pre>
                </div>
            </div>
        </div>

        <!-- 貿易數據 API -->
        <div class="api-section">
            <div class="api-header">
                <span class="api-method method-get">GET</span>
                <strong>貿易數據查詢</strong>
            </div>
            <div class="api-content">
                <div class="endpoint">/api/trade-data/{hts_id}?year=2023&country_id=CN</div>
                <p><strong>描述：</strong>獲取特定 HTS 條目的貿易數據</p>
                
                <h4>路徑參數：</h4>
                <ul>
                    <li><code>hts_id</code>: HTS 條目 ID</li>
                </ul>
                
                <h4>查詢參數：</h4>
                <ul>
                    <li><code>year</code> (可選): 年份篩選</li>
                    <li><code>country_id</code> (可選): 國家代碼篩選</li>
                </ul>
                
                <h4>回應範例：</h4>
                <div class="response">
                    <span class="status-badge status-200">200 OK</span>
                    <pre>{
  "status": "success",
  "data": [
    {
      "country": {
        "id": "CN",
        "name": "China"
      },
      "year": 2023,
      "import_value": 1500000.50,
      "import_quantity": 150
    }
  ]
}</pre>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e1e5e9; color: #666;">
            <p>TARIFFED API v1.0.0 - 美國關稅查詢系統</p>
            <p>基於 Flask + AI 技術構建</p>
        </div>
    </div>
</body>
</html>
