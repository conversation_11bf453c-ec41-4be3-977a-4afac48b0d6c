#!/usr/bin/env python3
"""
外部搜尋服務 - 整合 Tavily 和其他搜尋 API
"""

import os
import requests
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import time

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TavilySearchService:
    """Tavily 搜尋服務"""
    
    def __init__(self):
        """初始化 Tavily 搜尋服務"""
        self.api_key = os.getenv('TAVILY_API_KEY', '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')
        self.base_url = "https://api.tavily.com/search"
        self.timeout = 30
        
        if not self.api_key:
            logger.warning("⚠️ Tavily API 密鑰未設置")
        else:
            logger.info("✅ Tavily 搜尋服務已初始化")
    
    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        執行 Tavily 搜尋
        
        Args:
            query: 搜尋查詢
            **kwargs: 其他搜尋參數
            
        Returns:
            搜尋結果字典
        """
        try:
            # 準備搜尋參數
            search_params = {
                "api_key": self.api_key,
                "query": query,
                "search_depth": kwargs.get('search_depth', 'basic'),
                "include_answer": kwargs.get('include_answer', True),
                "include_raw_content": kwargs.get('include_raw_content', False),
                "max_results": kwargs.get('max_results', 5),
                "include_domains": kwargs.get('include_domains', []),
                "exclude_domains": kwargs.get('exclude_domains', [])
            }
            
            # 發送請求
            logger.info(f"🔍 Tavily 搜尋: {query}")
            response = requests.post(
                self.base_url,
                json=search_params,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ Tavily 搜尋成功，找到 {len(result.get('results', []))} 個結果")
                return {
                    'success': True,
                    'data': result,
                    'source': 'tavily',
                    'timestamp': datetime.now().isoformat()
                }
            else:
                logger.error(f"❌ Tavily 搜尋失敗: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f'API 錯誤: {response.status_code}',
                    'source': 'tavily'
                }
                
        except requests.exceptions.Timeout:
            logger.error("❌ Tavily 搜尋超時")
            return {
                'success': False,
                'error': '搜尋請求超時',
                'source': 'tavily'
            }
        except Exception as e:
            logger.error(f"❌ Tavily 搜尋異常: {e}")
            return {
                'success': False,
                'error': str(e),
                'source': 'tavily'
            }

class GoogleCustomSearchService:
    """Google Custom Search 服務（備用）"""
    
    def __init__(self):
        """初始化 Google Custom Search 服務"""
        self.api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
        self.search_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
        self.base_url = "https://www.googleapis.com/customsearch/v1"
        
        if not self.api_key or not self.search_engine_id:
            logger.warning("⚠️ Google Custom Search API 未完整配置")
        else:
            logger.info("✅ Google Custom Search 服務已初始化")
    
    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        執行 Google Custom Search
        
        Args:
            query: 搜尋查詢
            **kwargs: 其他搜尋參數
            
        Returns:
            搜尋結果字典
        """
        if not self.api_key or not self.search_engine_id:
            return {
                'success': False,
                'error': 'Google Custom Search API 未配置',
                'source': 'google'
            }
        
        try:
            params = {
                'key': self.api_key,
                'cx': self.search_engine_id,
                'q': query,
                'num': kwargs.get('max_results', 5),
                'start': kwargs.get('start', 1)
            }
            
            logger.info(f"🔍 Google 搜尋: {query}")
            response = requests.get(self.base_url, params=params, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ Google 搜尋成功")
                return {
                    'success': True,
                    'data': result,
                    'source': 'google',
                    'timestamp': datetime.now().isoformat()
                }
            else:
                logger.error(f"❌ Google 搜尋失敗: {response.status_code}")
                return {
                    'success': False,
                    'error': f'API 錯誤: {response.status_code}',
                    'source': 'google'
                }
                
        except Exception as e:
            logger.error(f"❌ Google 搜尋異常: {e}")
            return {
                'success': False,
                'error': str(e),
                'source': 'google'
            }

class ExternalSearchService:
    """統一的外部搜尋服務"""
    
    def __init__(self):
        """初始化外部搜尋服務"""
        self.tavily = TavilySearchService()
        self.google = GoogleCustomSearchService()
        self.search_history = []
        
        logger.info("🚀 外部搜尋服務已初始化")
    
    def search(self, query: str, source: str = 'auto', **kwargs) -> Dict[str, Any]:
        """
        統一搜尋接口
        
        Args:
            query: 搜尋查詢
            source: 搜尋來源 ('tavily', 'google', 'auto')
            **kwargs: 其他搜尋參數
            
        Returns:
            搜尋結果字典
        """
        try:
            # 記錄搜尋歷史
            search_record = {
                'query': query,
                'source': source,
                'timestamp': datetime.now().isoformat(),
                'params': kwargs
            }
            
            if source == 'tavily':
                result = self.tavily.search(query, **kwargs)
            elif source == 'google':
                result = self.google.search(query, **kwargs)
            elif source == 'auto':
                # 自動選擇最佳搜尋源
                result = self._auto_search(query, **kwargs)
            else:
                return {
                    'success': False,
                    'error': f'不支援的搜尋來源: {source}'
                }
            
            # 更新搜尋記錄
            search_record['success'] = result.get('success', False)
            search_record['results_count'] = len(result.get('data', {}).get('results', []))
            self.search_history.append(search_record)
            
            # 保持歷史記錄在合理範圍內
            if len(self.search_history) > 100:
                self.search_history = self.search_history[-100:]
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 搜尋服務異常: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _auto_search(self, query: str, **kwargs) -> Dict[str, Any]:
        """自動選擇搜尋源"""
        # 優先使用 Tavily（通常更適合實時搜尋）
        result = self.tavily.search(query, **kwargs)
        
        if result.get('success', False):
            return result
        
        # 如果 Tavily 失敗，嘗試 Google
        logger.info("🔄 Tavily 搜尋失敗，嘗試 Google 搜尋")
        return self.google.search(query, **kwargs)
    
    def search_tariff_related(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        關稅相關的專門搜尋
        
        Args:
            query: 搜尋查詢
            **kwargs: 其他搜尋參數
            
        Returns:
            搜尋結果字典
        """
        # 增強查詢以獲得更相關的關稅信息
        enhanced_query = f"{query} tariff HTS customs trade import export"
        
        # 指定相關域名
        include_domains = kwargs.get('include_domains', [])
        tariff_domains = [
            'usitc.gov',
            'cbp.gov', 
            'trade.gov',
            'census.gov',
            'ustr.gov'
        ]
        include_domains.extend(tariff_domains)
        kwargs['include_domains'] = include_domains
        
        return self.search(enhanced_query, **kwargs)
    
    def search_trade_news(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        貿易新聞搜尋
        
        Args:
            query: 搜尋查詢
            **kwargs: 其他搜尋參數
            
        Returns:
            搜尋結果字典
        """
        # 增強查詢以獲得貿易新聞
        enhanced_query = f"{query} trade news policy tariff import export"
        
        # 指定新聞域名
        include_domains = kwargs.get('include_domains', [])
        news_domains = [
            'reuters.com',
            'bloomberg.com',
            'wsj.com',
            'ft.com',
            'tradingeconomics.com'
        ]
        include_domains.extend(news_domains)
        kwargs['include_domains'] = include_domains
        
        return self.search(enhanced_query, **kwargs)
    
    def get_search_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """獲取搜尋歷史"""
        return self.search_history[-limit:] if self.search_history else []
    
    def get_search_stats(self) -> Dict[str, Any]:
        """獲取搜尋統計"""
        if not self.search_history:
            return {
                'total_searches': 0,
                'success_rate': 0,
                'sources_used': {}
            }
        
        total = len(self.search_history)
        successful = sum(1 for record in self.search_history if record.get('success', False))
        
        sources = {}
        for record in self.search_history:
            source = record.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
        
        return {
            'total_searches': total,
            'success_rate': successful / total if total > 0 else 0,
            'sources_used': sources,
            'recent_searches': self.get_search_history(5)
        }

# 全局外部搜尋服務實例
external_search_service = ExternalSearchService()
