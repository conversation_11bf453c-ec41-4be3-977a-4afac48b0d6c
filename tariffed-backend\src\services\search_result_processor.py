#!/usr/bin/env python3
"""
搜尋結果處理器 - 處理和格式化外部搜尋結果
"""

import re
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class SearchResultProcessor:
    """搜尋結果處理器"""
    
    def __init__(self):
        """初始化搜尋結果處理器"""
        self.trusted_domains = {
            'usitc.gov': '美國國際貿易委員會',
            'cbp.gov': '美國海關和邊境保護局',
            'trade.gov': '美國商務部國際貿易管理局',
            'census.gov': '美國人口普查局',
            'ustr.gov': '美國貿易代表辦公室',
            'wto.org': '世界貿易組織',
            'oecd.org': '經濟合作暨發展組織',
            'worldbank.org': '世界銀行',
            'imf.org': '國際貨幣基金組織'
        }
        
        self.news_domains = {
            'reuters.com': 'Reuters',
            'bloomberg.com': 'Bloomberg',
            'wsj.com': 'Wall Street Journal',
            'ft.com': 'Financial Times',
            'tradingeconomics.com': 'Trading Economics',
            'cnbc.com': 'CNBC',
            'marketwatch.com': 'MarketWatch'
        }
    
    def process_tavily_results(self, tavily_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        處理 Tavily 搜尋結果
        
        Args:
            tavily_data: Tavily API 返回的原始數據
            
        Returns:
            處理後的結果字典
        """
        try:
            processed_results = {
                'answer': tavily_data.get('answer', ''),
                'query': tavily_data.get('query', ''),
                'results': [],
                'sources': [],
                'summary': '',
                'credibility_score': 0.0,
                'processed_at': datetime.now().isoformat()
            }
            
            # 處理搜尋結果
            raw_results = tavily_data.get('results', [])
            
            for result in raw_results:
                processed_result = self._process_single_result(result, 'tavily')
                if processed_result:
                    processed_results['results'].append(processed_result)
                    processed_results['sources'].append(processed_result['domain'])
            
            # 計算可信度分數
            processed_results['credibility_score'] = self._calculate_credibility_score(
                processed_results['results']
            )
            
            # 生成摘要
            processed_results['summary'] = self._generate_summary(
                processed_results['results'], 
                tavily_data.get('answer', '')
            )
            
            return processed_results
            
        except Exception as e:
            logger.error(f"❌ 處理 Tavily 結果時出錯: {e}")
            return {
                'error': str(e),
                'results': [],
                'processed_at': datetime.now().isoformat()
            }
    
    def process_google_results(self, google_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        處理 Google Custom Search 結果
        
        Args:
            google_data: Google API 返回的原始數據
            
        Returns:
            處理後的結果字典
        """
        try:
            processed_results = {
                'answer': '',
                'query': google_data.get('queries', {}).get('request', [{}])[0].get('searchTerms', ''),
                'results': [],
                'sources': [],
                'summary': '',
                'credibility_score': 0.0,
                'processed_at': datetime.now().isoformat()
            }
            
            # 處理搜尋結果
            raw_results = google_data.get('items', [])
            
            for result in raw_results:
                processed_result = self._process_single_result(result, 'google')
                if processed_result:
                    processed_results['results'].append(processed_result)
                    processed_results['sources'].append(processed_result['domain'])
            
            # 計算可信度分數
            processed_results['credibility_score'] = self._calculate_credibility_score(
                processed_results['results']
            )
            
            # 生成摘要
            processed_results['summary'] = self._generate_summary(processed_results['results'])
            
            return processed_results
            
        except Exception as e:
            logger.error(f"❌ 處理 Google 結果時出錯: {e}")
            return {
                'error': str(e),
                'results': [],
                'processed_at': datetime.now().isoformat()
            }
    
    def _process_single_result(self, result: Dict[str, Any], source: str) -> Optional[Dict[str, Any]]:
        """處理單個搜尋結果"""
        try:
            if source == 'tavily':
                url = result.get('url', '')
                title = result.get('title', '')
                content = result.get('content', '')
                score = result.get('score', 0.0)
            elif source == 'google':
                url = result.get('link', '')
                title = result.get('title', '')
                content = result.get('snippet', '')
                score = 0.5  # Google 不提供分數
            else:
                return None
            
            if not url or not title:
                return None
            
            # 解析域名
            domain = self._extract_domain(url)
            
            # 確定來源類型和可信度
            source_type, credibility = self._determine_source_credibility(domain)
            
            # 清理和格式化內容
            cleaned_content = self._clean_content(content)
            
            # 提取關鍵信息
            key_info = self._extract_key_information(cleaned_content, title)
            
            return {
                'title': title,
                'url': url,
                'domain': domain,
                'content': cleaned_content,
                'source_type': source_type,
                'credibility': credibility,
                'score': score,
                'key_info': key_info,
                'source': source
            }
            
        except Exception as e:
            logger.error(f"❌ 處理單個結果時出錯: {e}")
            return None
    
    def _extract_domain(self, url: str) -> str:
        """提取域名"""
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower()
        except:
            return 'unknown'
    
    def _determine_source_credibility(self, domain: str) -> tuple:
        """確定來源可信度"""
        if domain in self.trusted_domains:
            return 'official', 0.9
        elif domain in self.news_domains:
            return 'news', 0.7
        elif domain.endswith('.gov'):
            return 'government', 0.85
        elif domain.endswith('.edu'):
            return 'academic', 0.8
        elif domain.endswith('.org'):
            return 'organization', 0.6
        else:
            return 'general', 0.4
    
    def _clean_content(self, content: str) -> str:
        """清理內容"""
        if not content:
            return ''
        
        # 移除多餘的空白字符
        content = re.sub(r'\s+', ' ', content)
        
        # 移除特殊字符
        content = re.sub(r'[^\w\s\-.,;:()%$]', '', content)
        
        # 限制長度
        if len(content) > 500:
            content = content[:500] + '...'
        
        return content.strip()
    
    def _extract_key_information(self, content: str, title: str) -> Dict[str, Any]:
        """提取關鍵信息"""
        key_info = {
            'tariff_rates': [],
            'hts_codes': [],
            'countries': [],
            'dates': [],
            'percentages': [],
            'amounts': []
        }
        
        # 提取關稅稅率
        tariff_pattern = r'(\d+(?:\.\d+)?%|\d+(?:\.\d+)?\s*cents?)'
        key_info['tariff_rates'] = re.findall(tariff_pattern, content, re.IGNORECASE)
        
        # 提取 HTS 代碼
        hts_pattern = r'\b\d{4}\.\d{2}\.\d{2}\b'
        key_info['hts_codes'] = re.findall(hts_pattern, content)
        
        # 提取國家名稱
        country_pattern = r'\b(China|Canada|Mexico|Japan|Germany|Korea|India|Brazil|UK|France|Italy)\b'
        key_info['countries'] = list(set(re.findall(country_pattern, content, re.IGNORECASE)))
        
        # 提取日期
        date_pattern = r'\b\d{4}[-/]\d{1,2}[-/]\d{1,2}\b|\b\d{1,2}[-/]\d{1,2}[-/]\d{4}\b'
        key_info['dates'] = re.findall(date_pattern, content)
        
        # 提取百分比
        percentage_pattern = r'\b\d+(?:\.\d+)?%\b'
        key_info['percentages'] = re.findall(percentage_pattern, content)
        
        # 提取金額
        amount_pattern = r'\$\d+(?:,\d{3})*(?:\.\d{2})?(?:\s*(?:million|billion|trillion))?'
        key_info['amounts'] = re.findall(amount_pattern, content, re.IGNORECASE)
        
        return key_info
    
    def _calculate_credibility_score(self, results: List[Dict[str, Any]]) -> float:
        """計算整體可信度分數"""
        if not results:
            return 0.0
        
        total_score = 0.0
        total_weight = 0.0
        
        for result in results:
            credibility = result.get('credibility', 0.0)
            score = result.get('score', 0.5)
            weight = credibility * score
            
            total_score += weight
            total_weight += 1.0
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _generate_summary(self, results: List[Dict[str, Any]], answer: str = '') -> str:
        """生成搜尋結果摘要"""
        if not results:
            return '沒有找到相關結果。'
        
        # 如果有 Tavily 的答案，優先使用
        if answer:
            return f"根據搜尋結果：{answer}"
        
        # 否則基於結果生成摘要
        high_credibility_results = [r for r in results if r.get('credibility', 0) > 0.7]
        
        if high_credibility_results:
            summary_parts = []
            for result in high_credibility_results[:3]:
                domain_name = self.trusted_domains.get(result['domain']) or \
                             self.news_domains.get(result['domain']) or \
                             result['domain']
                summary_parts.append(f"根據 {domain_name} 的信息：{result['content'][:100]}...")
            
            return '\n'.join(summary_parts)
        else:
            return f"找到 {len(results)} 個相關結果，建議查看具體內容以獲得更詳細的信息。"
    
    def format_for_display(self, processed_results: Dict[str, Any]) -> str:
        """格式化結果用於顯示"""
        if not processed_results.get('results'):
            return "沒有找到相關的搜尋結果。"
        
        formatted = f"**🔍 搜尋結果摘要**\n\n"
        
        # 添加答案（如果有）
        if processed_results.get('answer'):
            formatted += f"**📋 快速答案：**\n{processed_results['answer']}\n\n"
        
        # 添加摘要
        if processed_results.get('summary'):
            formatted += f"**📊 結果摘要：**\n{processed_results['summary']}\n\n"
        
        # 添加主要結果
        formatted += f"**🔗 主要來源：**\n"
        
        for i, result in enumerate(processed_results['results'][:5], 1):
            source_name = self.trusted_domains.get(result['domain']) or \
                         self.news_domains.get(result['domain']) or \
                         result['domain']
            
            credibility_emoji = "🟢" if result['credibility'] > 0.8 else \
                               "🟡" if result['credibility'] > 0.6 else "🔴"
            
            formatted += f"{i}. {credibility_emoji} **{result['title']}**\n"
            formatted += f"   來源：{source_name}\n"
            formatted += f"   內容：{result['content'][:150]}...\n"
            formatted += f"   連結：{result['url']}\n\n"
        
        # 添加可信度評分
        credibility_score = processed_results.get('credibility_score', 0)
        credibility_level = "高" if credibility_score > 0.8 else \
                           "中" if credibility_score > 0.6 else "低"
        
        formatted += f"**⭐ 整體可信度：** {credibility_level} ({credibility_score:.2f})\n"
        formatted += f"**📅 搜尋時間：** {processed_results.get('processed_at', 'N/A')}\n"
        
        return formatted

# 全局搜尋結果處理器實例
search_result_processor = SearchResultProcessor()
