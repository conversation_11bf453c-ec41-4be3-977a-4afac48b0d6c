#!/usr/bin/env python3
"""
多代理服務 - 整合查詢、分析、建議代理的統一服務接口
"""

import os
import sys
import logging
from typing import Dict, Any, List, Optional

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.agents import initialize_agents, agent_coordinator
from src.services.ai_agent import TariffAIAgent
from src.services.external_search_service import external_search_service
from src.services.search_result_processor import search_result_processor
from src.services.visualization_service import visualization_service
from src.services.chart_recommendation_service import chart_recommendation_service

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiAgentService:
    """多代理服務 - 統一的智能查詢處理服務"""
    
    def __init__(self):
        """初始化多代理服務"""
        # 初始化多代理系統
        self.coordinator = initialize_agents()
        
        # 保留原有的 AI 代理作為備用
        self.fallback_agent = TariffAIAgent()
        
        logger.info("🚀 多代理服務已初始化")
        logger.info(f"📋 可用代理: {list(self.coordinator.agents.keys())}")
    
    def process_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        處理用戶查詢 - 智能路由到最適合的代理
        
        Args:
            query: 用戶查詢
            context: 查詢上下文
            
        Returns:
            處理結果字典
        """
        try:
            logger.info(f"🔍 處理查詢: {query}")
            
            # 使用協調器智能路由查詢
            result = self.coordinator.route_query(query, context)
            
            if result['success']:
                logger.info(f"✅ 查詢成功，使用代理: {result['agent_used']}")
                
                # 增強回應格式
                enhanced_result = self._enhance_response(result, query)
                return enhanced_result
            else:
                logger.warning(f"⚠️ 多代理處理失敗: {result.get('error', 'Unknown error')}")
                
                # 使用備用代理
                fallback_result = self._use_fallback_agent(query, context)
                return fallback_result
                
        except Exception as e:
            logger.error(f"❌ 多代理服務錯誤: {e}")
            
            # 使用備用代理
            return self._use_fallback_agent(query, context)
    
    def collaborative_query(self, query: str, agents: List[str] = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        多代理協作查詢 - 讓多個代理共同處理複雜查詢
        
        Args:
            query: 用戶查詢
            agents: 指定的代理列表，如果為 None 則使用所有代理
            context: 查詢上下文
            
        Returns:
            協作處理結果
        """
        try:
            if agents is None:
                agents = ['query_agent', 'analysis_agent', 'recommendation_agent']
            
            logger.info(f"🤝 協作查詢: {query}, 使用代理: {agents}")
            
            # 執行協作查詢
            result = self.coordinator.collaborative_query(query, agents, context)
            
            # 整合協作結果
            integrated_result = self._integrate_collaborative_results(result, query)
            
            return integrated_result
            
        except Exception as e:
            logger.error(f"❌ 協作查詢錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': f"協作查詢處理時發生錯誤：{e}"
            }
    
    def get_agent_status(self) -> Dict[str, Any]:
        """獲取所有代理的狀態信息"""
        try:
            status = self.coordinator.get_system_status()
            
            # 添加服務級別的信息
            status['service_info'] = {
                'name': 'TARIFFED Multi-Agent Service',
                'version': '1.0.0',
                'features': [
                    'Intelligent Query Routing',
                    'Multi-Agent Collaboration',
                    'Specialized Domain Agents',
                    'Fallback Support'
                ]
            }
            
            return status
            
        except Exception as e:
            logger.error(f"❌ 獲取代理狀態錯誤: {e}")
            return {
                'error': str(e),
                'service_health': 'degraded'
            }
    
    def query_specific_agent(self, agent_name: str, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        查詢特定代理
        
        Args:
            agent_name: 代理名稱 ('query_agent', 'analysis_agent', 'recommendation_agent')
            query: 用戶查詢
            context: 查詢上下文
            
        Returns:
            特定代理的處理結果
        """
        try:
            agent = self.coordinator.get_agent(agent_name)
            
            if not agent:
                return {
                    'success': False,
                    'error': f'代理 {agent_name} 不存在',
                    'available_agents': list(self.coordinator.agents.keys())
                }
            
            logger.info(f"🎯 直接查詢代理: {agent_name}")
            
            result = agent.process_query(query, context)
            
            return {
                'success': True,
                'agent_used': agent_name,
                'result': result,
                'query': query,
                'timestamp': self._get_timestamp()
            }
            
        except Exception as e:
            logger.error(f"❌ 查詢特定代理錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'agent_name': agent_name
            }
    
    def _enhance_response(self, result: Dict[str, Any], query: str) -> Dict[str, Any]:
        """增強回應格式"""
        enhanced = {
            'success': True,
            'query': query,
            'agent_used': result['agent_used'],
            'response': result['result'].get('response', ''),
            'data': result['result'].get('data', []),
            'timestamp': self._get_timestamp(),
            'metadata': {
                'query_type': result['result'].get('query_type', 'unknown'),
                'analysis_type': result['result'].get('analysis_type', None),
                'recommendation_type': result['result'].get('recommendation_type', None)
            }
        }
        
        # 添加額外的信息
        if 'chart_suggestions' in result['result']:
            enhanced['chart_suggestions'] = result['result']['chart_suggestions']
        
        if 'action_items' in result['result']:
            enhanced['action_items'] = result['result']['action_items']
        
        if 'suggestions' in result['result']:
            enhanced['suggestions'] = result['result']['suggestions']
        
        return enhanced
    
    def _use_fallback_agent(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """使用備用代理處理查詢"""
        try:
            logger.info("🔄 使用備用代理處理查詢")
            
            # 使用原有的 AI 代理
            response = self.fallback_agent.process_query(query)
            
            return {
                'success': True,
                'query': query,
                'agent_used': 'fallback_agent',
                'response': response,
                'data': [],
                'timestamp': self._get_timestamp(),
                'metadata': {
                    'fallback_used': True,
                    'reason': 'Multi-agent system unavailable'
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 備用代理也失敗: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': f"很抱歉，系統暫時無法處理您的查詢。請稍後再試。錯誤信息：{e}"
            }
    
    def _integrate_collaborative_results(self, collaborative_result: Dict[str, Any], query: str) -> Dict[str, Any]:
        """整合協作查詢結果"""
        try:
            results = collaborative_result.get('collaborative_results', {})
            
            # 收集成功的結果
            successful_results = {}
            failed_agents = []
            
            for agent_name, agent_result in results.items():
                if agent_result.get('success', False):
                    successful_results[agent_name] = agent_result['result']
                else:
                    failed_agents.append(agent_name)
            
            # 生成整合回應
            integrated_response = self._generate_integrated_response(successful_results, query)
            
            return {
                'success': True,
                'query': query,
                'collaborative_mode': True,
                'response': integrated_response,
                'individual_results': successful_results,
                'failed_agents': failed_agents,
                'timestamp': collaborative_result.get('timestamp'),
                'agents_used': list(successful_results.keys())
            }
            
        except Exception as e:
            logger.error(f"❌ 整合協作結果錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': f"整合協作結果時發生錯誤：{e}"
            }
    
    def _generate_integrated_response(self, results: Dict[str, Any], query: str) -> str:
        """生成整合回應"""
        response = f"**🤖 多代理協作分析結果**\n\n關於您的查詢「{query}」，我們的專業代理團隊提供以下綜合分析：\n\n"
        
        # 查詢代理結果
        if 'query_agent' in results:
            query_result = results['query_agent']
            response += "**🔍 查詢分析：**\n"
            response += f"{query_result.get('response', '無查詢結果')}\n\n"
        
        # 分析代理結果
        if 'analysis_agent' in results:
            analysis_result = results['analysis_agent']
            response += "**📊 數據分析：**\n"
            response += f"{analysis_result.get('response', '無分析結果')}\n\n"
        
        # 建議代理結果
        if 'recommendation_agent' in results:
            recommendation_result = results['recommendation_agent']
            response += "**💡 專業建議：**\n"
            response += f"{recommendation_result.get('response', '無建議結果')}\n\n"
        
        response += "---\n"
        response += "*此回應由 TARIFFED 多代理系統協作生成，結合了查詢、分析和建議專家的專業知識。*"
        
        return response
    
    def _get_timestamp(self) -> str:
        """獲取當前時間戳"""
        from datetime import datetime
        return datetime.now().isoformat()

    def search_external(self, query: str, search_type: str = 'general', **kwargs) -> Dict[str, Any]:
        """
        外部搜尋功能

        Args:
            query: 搜尋查詢
            search_type: 搜尋類型 ('general', 'tariff', 'trade_news')
            **kwargs: 其他搜尋參數

        Returns:
            搜尋結果字典
        """
        try:
            logger.info(f"🔍 外部搜尋: {query} (類型: {search_type})")

            # 根據搜尋類型選擇搜尋方法
            if search_type == 'tariff':
                search_result = external_search_service.search_tariff_related(query, **kwargs)
            elif search_type == 'trade_news':
                search_result = external_search_service.search_trade_news(query, **kwargs)
            else:
                search_result = external_search_service.search(query, **kwargs)

            if not search_result.get('success', False):
                return {
                    'success': False,
                    'error': search_result.get('error', '搜尋失敗'),
                    'query': query,
                    'search_type': search_type
                }

            # 處理搜尋結果
            raw_data = search_result.get('data', {})
            source = search_result.get('source', 'unknown')

            if source == 'tavily':
                processed_results = search_result_processor.process_tavily_results(raw_data)
            elif source == 'google':
                processed_results = search_result_processor.process_google_results(raw_data)
            else:
                processed_results = {'results': [], 'summary': '無法處理的搜尋結果格式'}

            # 格式化顯示結果
            formatted_response = search_result_processor.format_for_display(processed_results)

            return {
                'success': True,
                'query': query,
                'search_type': search_type,
                'source': source,
                'response': formatted_response,
                'raw_results': processed_results,
                'timestamp': self._get_timestamp(),
                'credibility_score': processed_results.get('credibility_score', 0.0)
            }

        except Exception as e:
            logger.error(f"❌ 外部搜尋錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'query': query,
                'search_type': search_type
            }

    def enhanced_query_with_search(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        增強查詢 - 結合內部代理和外部搜尋

        Args:
            query: 用戶查詢
            context: 查詢上下文

        Returns:
            增強的查詢結果
        """
        try:
            logger.info(f"🚀 增強查詢: {query}")

            # 首先使用內部代理處理查詢
            internal_result = self.process_query(query, context)

            # 判斷是否需要外部搜尋
            need_external_search = self._should_use_external_search(query, internal_result)

            if need_external_search:
                # 執行外部搜尋
                search_type = self._determine_search_type(query)
                external_result = self.search_external(query, search_type)

                if external_result.get('success', False):
                    # 整合內部和外部結果
                    integrated_result = self._integrate_internal_external_results(
                        internal_result, external_result, query
                    )
                    return integrated_result

            # 如果不需要外部搜尋或外部搜尋失敗，返回內部結果
            return internal_result

        except Exception as e:
            logger.error(f"❌ 增強查詢錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'query': query
            }

    def _should_use_external_search(self, query: str, internal_result: Dict[str, Any]) -> bool:
        """判斷是否需要外部搜尋"""
        # 檢查查詢中的關鍵詞
        external_search_keywords = [
            '最新', '新聞', '政策', '變化', '更新', '當前', '現在',
            'latest', 'news', 'policy', 'current', 'recent', 'update'
        ]

        query_lower = query.lower()
        has_external_keywords = any(keyword in query_lower for keyword in external_search_keywords)

        # 檢查內部結果的完整性
        internal_success = internal_result.get('success', False)
        internal_data = internal_result.get('data', [])

        # 如果有外部搜尋關鍵詞，或內部結果不完整，則使用外部搜尋
        return has_external_keywords or not internal_success or len(internal_data) == 0

    def _determine_search_type(self, query: str) -> str:
        """確定搜尋類型"""
        query_lower = query.lower()

        if any(keyword in query_lower for keyword in ['關稅', 'hts', 'tariff', '稅率', '進口']):
            return 'tariff'
        elif any(keyword in query_lower for keyword in ['新聞', 'news', '政策', 'policy']):
            return 'trade_news'
        else:
            return 'general'

    def _integrate_internal_external_results(self, internal_result: Dict[str, Any],
                                           external_result: Dict[str, Any],
                                           query: str) -> Dict[str, Any]:
        """整合內部和外部搜尋結果"""
        try:
            # 生成整合回應
            integrated_response = f"""**🤖 TARIFFED 智能分析結果**

根據您的查詢「{query}」，我們結合了內部專業分析和最新外部信息：

**📋 專業分析：**
{internal_result.get('response', '內部分析暫時不可用')}

**🔍 最新外部信息：**
{external_result.get('response', '外部搜尋結果暫時不可用')}

---
*此回應結合了 TARIFFED 專業代理分析和實時外部搜尋結果，為您提供最全面的信息。*"""

            return {
                'success': True,
                'query': query,
                'response': integrated_response,
                'internal_result': internal_result,
                'external_result': external_result,
                'integration_mode': True,
                'timestamp': self._get_timestamp(),
                'credibility_score': external_result.get('credibility_score', 0.0)
            }

        except Exception as e:
            logger.error(f"❌ 結果整合錯誤: {e}")
            return internal_result  # 返回內部結果作為備用

    def generate_visualization(self, data: Dict[str, Any], chart_type: str = None) -> Dict[str, Any]:
        """
        生成數據視覺化

        Args:
            data: 要視覺化的數據
            chart_type: 指定的圖表類型

        Returns:
            視覺化配置字典
        """
        try:
            logger.info(f"📊 生成數據視覺化，圖表類型: {chart_type or 'auto'}")

            # 如果沒有指定圖表類型，自動推薦
            if not chart_type:
                chart_recommendations = chart_recommendation_service.recommend_charts(
                    query=data.get('query', ''),
                    data=data.get('data', []),
                    context=data.get('context', {})
                )

                if chart_recommendations.get('success', False) and chart_recommendations.get('recommendations'):
                    chart_type = chart_recommendations['recommendations'][0]['chart_type']
                else:
                    chart_type = 'bar'  # 默認柱狀圖

            # 根據數據類型生成相應的圖表
            if 'trade_data' in data:
                chart_config = visualization_service.generate_trade_trend_chart(
                    data['trade_data'], chart_type
                )
            elif 'tariff_data' in data:
                chart_config = visualization_service.generate_tariff_comparison_chart(
                    data['tariff_data']
                )
            elif 'country_data' in data:
                chart_config = visualization_service.generate_country_market_share_chart(
                    data['country_data']
                )
            elif 'hts_data' in data:
                chart_config = visualization_service.generate_hts_category_analysis(
                    data['hts_data']
                )
            else:
                # 通用數據處理
                chart_config = self._generate_generic_chart(data, chart_type)

            return {
                'success': True,
                'chart_config': chart_config,
                'chart_type': chart_type,
                'data_summary': self._generate_data_summary(data),
                'timestamp': self._get_timestamp()
            }

        except Exception as e:
            logger.error(f"❌ 生成視覺化錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'chart_type': chart_type
            }

    def get_chart_recommendations(self, query: str, data: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        獲取圖表建議

        Args:
            query: 用戶查詢
            data: 數據樣本

        Returns:
            圖表建議字典
        """
        try:
            logger.info(f"🎯 獲取圖表建議: {query}")

            recommendations = chart_recommendation_service.recommend_charts(query, data)

            if recommendations.get('success', False):
                # 為每個建議添加配置模板
                for rec in recommendations.get('recommendations', []):
                    template = chart_recommendation_service.get_chart_config_template(
                        rec['chart_type'], data
                    )
                    rec['template'] = template

                return {
                    'success': True,
                    'query': query,
                    'recommendations': recommendations['recommendations'],
                    'analysis': {
                        'query_analysis': recommendations.get('query_analysis', {}),
                        'data_analysis': recommendations.get('data_analysis', {})
                    },
                    'timestamp': self._get_timestamp()
                }
            else:
                return recommendations

        except Exception as e:
            logger.error(f"❌ 獲取圖表建議錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'query': query
            }

    def generate_statistical_dashboard(self, data_sources: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成統計儀表板

        Args:
            data_sources: 各種數據源

        Returns:
            儀表板配置字典
        """
        try:
            logger.info("📊 生成統計儀表板")

            dashboard = visualization_service.generate_statistical_dashboard(data_sources)

            return {
                'success': True,
                'dashboard': dashboard,
                'timestamp': self._get_timestamp()
            }

        except Exception as e:
            logger.error(f"❌ 生成統計儀表板錯誤: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def analyze_with_visualization(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        帶視覺化的增強分析

        Args:
            query: 用戶查詢
            context: 查詢上下文

        Returns:
            包含分析結果和視覺化的字典
        """
        try:
            logger.info(f"📊 帶視覺化的增強分析: {query}")

            # 首先進行常規分析
            analysis_result = self.process_query(query, context)

            if not analysis_result.get('success', False):
                return analysis_result

            # 獲取圖表建議
            chart_recommendations = self.get_chart_recommendations(query, analysis_result.get('data', []))

            # 如果有推薦的圖表，生成視覺化
            visualization_result = None
            if chart_recommendations.get('success', False) and chart_recommendations.get('recommendations'):
                recommended_chart = chart_recommendations['recommendations'][0]['chart_type']

                visualization_data = {
                    'query': query,
                    'data': analysis_result.get('data', []),
                    'context': context or {}
                }

                visualization_result = self.generate_visualization(visualization_data, recommended_chart)

            # 整合結果
            enhanced_result = {
                'success': True,
                'query': query,
                'analysis': analysis_result,
                'chart_recommendations': chart_recommendations.get('recommendations', []),
                'visualization': visualization_result.get('chart_config') if visualization_result else None,
                'enhanced_response': self._generate_enhanced_response_with_charts(
                    analysis_result, chart_recommendations, visualization_result
                ),
                'timestamp': self._get_timestamp()
            }

            return enhanced_result

        except Exception as e:
            logger.error(f"❌ 帶視覺化的增強分析錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'query': query
            }

    def _generate_generic_chart(self, data: Dict[str, Any], chart_type: str) -> Dict[str, Any]:
        """生成通用圖表"""
        try:
            # 提取數據
            chart_data = data.get('data', [])

            if not chart_data:
                return {
                    'type': 'empty',
                    'title': '暫無數據',
                    'message': '沒有可用於視覺化的數據'
                }

            # 生成基本圖表配置
            template = chart_recommendation_service.get_chart_config_template(chart_type, chart_data)

            # 自定義配置
            config = {
                'type': chart_type,
                'title': data.get('title', '數據分析'),
                'data': self._format_data_for_chart(chart_data, chart_type),
                'options': template.get('config', {}),
                'statistics': self._calculate_basic_statistics(chart_data)
            }

            return config

        except Exception as e:
            logger.error(f"❌ 生成通用圖表錯誤: {e}")
            return {
                'type': 'error',
                'title': '圖表生成錯誤',
                'error': str(e)
            }

    def _format_data_for_chart(self, data: List[Dict[str, Any]], chart_type: str) -> Dict[str, Any]:
        """為圖表格式化數據"""
        if not data:
            return {'labels': [], 'datasets': []}

        # 提取標籤和數值
        labels = []
        values = []

        for item in data:
            if isinstance(item, dict):
                # 嘗試找到合適的標籤和數值字段
                label_field = None
                value_field = None

                for key, value in item.items():
                    if isinstance(value, str) and not label_field:
                        label_field = key
                    elif isinstance(value, (int, float)) and not value_field:
                        value_field = key

                if label_field and value_field:
                    labels.append(str(item[label_field]))
                    values.append(float(item[value_field]))

        # 如果沒有找到合適的字段，使用索引
        if not labels or not values:
            labels = [f"項目 {i+1}" for i in range(len(data))]
            values = [i+1 for i in range(len(data))]

        return {
            'labels': labels,
            'datasets': [{
                'label': '數據',
                'data': values,
                'backgroundColor': '#3B82F6',
                'borderColor': '#3B82F6',
                'borderWidth': 1
            }]
        }

    def _calculate_basic_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """計算基本統計信息"""
        if not data:
            return {}

        try:
            # 提取數值
            values = []
            for item in data:
                if isinstance(item, dict):
                    for value in item.values():
                        if isinstance(value, (int, float)):
                            values.append(float(value))
                            break

            if not values:
                return {'count': len(data)}

            import statistics

            return {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'std_dev': statistics.stdev(values) if len(values) > 1 else 0
            }

        except Exception as e:
            logger.error(f"❌ 計算基本統計錯誤: {e}")
            return {'count': len(data)}

    def _generate_data_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成數據摘要"""
        summary = {
            'total_records': 0,
            'data_types': [],
            'key_fields': []
        }

        for key, value in data.items():
            if isinstance(value, list):
                summary['total_records'] += len(value)
                summary['data_types'].append(key)

                if value and isinstance(value[0], dict):
                    summary['key_fields'].extend(list(value[0].keys()))

        summary['key_fields'] = list(set(summary['key_fields']))

        return summary

    def _generate_enhanced_response_with_charts(self, analysis_result: Dict[str, Any],
                                              chart_recommendations: Dict[str, Any],
                                              visualization_result: Dict[str, Any]) -> str:
        """生成包含圖表建議的增強回應"""
        response = analysis_result.get('response', '')

        # 添加圖表建議
        if chart_recommendations and chart_recommendations.get('recommendations'):
            response += "\n\n**📊 建議的視覺化方式：**\n"

            for i, rec in enumerate(chart_recommendations['recommendations'][:3], 1):
                chart_name = rec.get('template', {}).get('title', rec['chart_type'])
                confidence = rec.get('confidence', 0) * 100
                reason = rec.get('reason', '')

                response += f"{i}. **{chart_name}** (信心度: {confidence:.0f}%)\n"
                response += f"   推薦理由: {reason}\n\n"

        # 添加統計信息
        if visualization_result and visualization_result.get('chart_config', {}).get('statistics'):
            stats = visualization_result['chart_config']['statistics']
            response += "**📈 數據統計摘要：**\n"

            if 'mean_value' in stats:
                response += f"• 平均值: {stats['mean_value']:,.2f}\n"
            if 'growth_rate' in stats:
                response += f"• 增長率: {stats['growth_rate']:.1f}%\n"
            if 'trend_direction' in stats:
                trend_emoji = {"increasing": "📈", "decreasing": "📉", "stable": "➡️"}.get(stats['trend_direction'], "📊")
                response += f"• 趨勢方向: {trend_emoji} {stats['trend_direction']}\n"

        response += "\n---\n*此分析包含智能圖表建議，可幫助您更好地理解數據趨勢和模式。*"

        return response

# 全局多代理服務實例
multi_agent_service = MultiAgentService()
