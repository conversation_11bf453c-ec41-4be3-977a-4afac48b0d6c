#!/usr/bin/env python3
"""
多代理系統初始化模組
"""

from .base_agent import BaseAgent, AgentCoordinator, agent_coordinator
from .query_agent import QueryAgent
from .analysis_agent import AnalysisAgent
from .recommendation_agent import RecommendationAgent

# 初始化所有代理
def initialize_agents():
    """初始化並註冊所有代理"""
    
    # 創建代理實例
    query_agent = QueryAgent()
    analysis_agent = AnalysisAgent()
    recommendation_agent = RecommendationAgent()
    
    # 註冊到協調器
    agent_coordinator.register_agent(query_agent)
    agent_coordinator.register_agent(analysis_agent)
    agent_coordinator.register_agent(recommendation_agent)
    
    return agent_coordinator

# 導出主要組件
__all__ = [
    'BaseAgent',
    'AgentCoordinator', 
    'agent_coordinator',
    'QueryAgent',
    'AnalysisAgent',
    'RecommendationAgent',
    'initialize_agents'
]
