#!/usr/bin/env python3

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

from flask import Flask, send_from_directory
from flask_cors import CORS
from flasgger import Swagger
from src.models.hts import db
from src.routes.tariff import tariff_bp

def create_app():
    app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'src', 'static'))
    app.config['SECRET_KEY'] = 'tariffed-secret-key-2025'

    # Enable CORS for all routes
    CORS(app)

    # Swagger configuration
    swagger_config = {
        "headers": [],
        "specs": [
            {
                "endpoint": 'apispec_1',
                "route": '/apispec_1.json',
                "rule_filter": lambda rule: True,
                "model_filter": lambda tag: True,
            }
        ],
        "static_url_path": "/flasgger_static",
        "swagger_ui": True,
        "specs_route": "/api/docs/"
    }

    swagger_template = {
        "swagger": "2.0",
        "info": {
            "title": "TARIFFED API",
            "description": "美國關稅查詢系統 API 文檔",
            "version": "1.0.0",
            "contact": {
                "name": "TARIFFED Team",
                "email": "<EMAIL>"
            }
        },
        "host": "localhost:5001",
        "basePath": "/api",
        "schemes": ["http", "https"],
        "consumes": ["application/json"],
        "produces": ["application/json"],
        "tags": [
            {
                "name": "查詢",
                "description": "AI 自然語言查詢相關 API"
            },
            {
                "name": "HTS",
                "description": "HTS 條目搜尋和查詢相關 API"
            },
            {
                "name": "貿易數據",
                "description": "貿易統計數據相關 API"
            }
        ]
    }

    swagger = Swagger(app, config=swagger_config, template=swagger_template)

    # Register blueprints
    app.register_blueprint(tariff_bp, url_prefix='/api')

    # Database configuration
    app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'src', 'database', 'app.db')}"
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)

    with app.app_context():
        db.create_all()

    @app.route('/', defaults={'path': ''})
    @app.route('/<path:path>')
    def serve(path):
        static_folder_path = app.static_folder
        if static_folder_path is None:
            return "Static folder not configured", 404

        if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
            return send_from_directory(static_folder_path, path)
        else:
            index_path = os.path.join(static_folder_path, 'index.html')
            if os.path.exists(index_path):
                return send_from_directory(static_folder_path, 'index.html')
            else:
                return "index.html not found", 404

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(host='0.0.0.0', port=5001, debug=True)

