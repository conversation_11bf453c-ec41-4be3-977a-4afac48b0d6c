import { useState } from 'react'
import { Search, Database, Globe, TrendingUp, BarChart3, PieChart } from 'lucide-react'
import { Button } from '@/components/ui/button.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs.jsx'
import './App.css'

function App() {
  const [query, setQuery] = useState('')
  const [searchResults, setSearchResults] = useState([])
  const [aiResponse, setAiResponse] = useState('')
  const [chartData, setChartData] = useState(null)
  const [loading, setLoading] = useState(false)

  // 從AI回應中提取圖表數據的函數
  const extractChartDataFromResponse = (responseText) => {
    try {
      // 檢測進口國表格數據模式
      const importCountriesRegex = /\|\s*排名\s*\|\s*國家\s*\|[^\n]*\n[|\s-]*\n([|\s\w\d\(\)%\*\u4e00-\u9fff,.，。（）％、\-\$]+)/
      const importMatch = responseText.match(importCountriesRegex)

      if (importMatch) {
        const tableContent = importMatch[1]
        const rows = tableContent.split('\n').filter(row => row.includes('|') && !row.match(/^\s*[|\s-]+\s*$/))

        const data = []
        rows.forEach(row => {
          const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell)
          if (cells.length >= 4 && cells[0] && !isNaN(cells[0])) {
            // 提取數值 - 支援多種格式
            const valueText = cells[2] || ''
            const percentText = cells[4] || ''

            // 匹配不同的數值格式
            const valueMatch = valueText.match(/([\d,]+)\s*億/) ||
                             valueText.match(/約\s*([\d,]+)\s*億/) ||
                             valueText.match(/\$?([\d,]+\.?\d*)\s*億/) ||
                             valueText.match(/([\d,]+)/)

            const percentMatch = percentText.match(/([\d.]+)%/)

            if (valueMatch) {
              const countryName = cells[1].replace(/\*\*|\(\w+\)/g, '').trim()
              const value = parseInt(valueMatch[1].replace(/,/g, ''))
              const percentage = percentMatch ? parseFloat(percentMatch[1]) : 0

              data.push({
                country: countryName,
                value: value,
                percentage: percentage,
                products: cells[3] || '',
                rank: parseInt(cells[0])
              })
            }
          }
        })

        if (data.length > 0) {
          return {
            type: 'importCountries',
            title: '美國主要進口國',
            data: data
          }
        }
      }
      
      return null
    } catch (error) {
      console.error('解析圖表數據時發生錯誤:', error)
      return null
    }
  }

  const handleAIQuery = async () => {
    if (!query.trim()) return
    
    setLoading(true)
    try {
      const response = await fetch('/api/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      })
      
      const data = await response.json()
      if (data.status === 'success') {
        setAiResponse(data.data)
        // 檢測是否包含數據並嘗試解析圖表數據
        const extractedChartData = extractChartDataFromResponse(data.data)
        setChartData(extractedChartData)
      } else {
        setAiResponse('查詢時發生錯誤：' + data.message)
        setChartData(null)
      }
    } catch (error) {
      setAiResponse('連接服務器時發生錯誤')
    }
    setLoading(false)
  }

  const handleHTSSearch = async () => {
    if (!query.trim()) return
    
    setLoading(true)
    try {
      const response = await fetch(`/api/hts/search?keyword=${encodeURIComponent(query)}`)
      const data = await response.json()
      if (data.status === 'success') {
        setSearchResults(data.data)
      } else {
        setSearchResults([])
      }
    } catch (error) {
      setSearchResults([])
    }
    setLoading(false)
  }

  // 圖表組件
  const ChartComponent = ({ chartData }) => {
    if (!chartData) return null

    if (chartData.type === 'importCountries') {
      const maxValue = Math.max(...chartData.data.map(d => d.value))
      
      return (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              <span>{chartData.title}</span>
            </CardTitle>
            <CardDescription>
              按進口值排序（單位：億美元）
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {chartData.data.slice(0, 8).map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <span className="w-6 h-6 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                        {index + 1}
                      </span>
                      <span className="font-semibold text-gray-800">{item.country}</span>
                      <span className="text-sm text-gray-600">({item.percentage}%)</span>
                    </div>
                    <span className="font-bold text-blue-600">{item.value} 億</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
                      style={{ width: `${(item.value / maxValue) * 100}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-600 ml-8">
                    主要商品：{item.products.length > 50 ? item.products.substring(0, 50) + '...' : item.products}
                  </div>
                </div>
              ))}
            </div>
            
            {/* 餅狀圖 */}
            <div className="mt-8">
              <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                <PieChart className="h-4 w-4 mr-2 text-green-600" />
                進口份額分佈
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {chartData.data.slice(0, 8).map((item, index) => {
                  const colors = [
                    'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
                    'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-gray-500'
                  ]
                  return (
                    <div key={index} className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${colors[index]}`}></div>
                      <span className="text-sm text-gray-700">{item.country}</span>
                      <span className="text-sm text-gray-500">({item.percentage}%)</span>
                    </div>
                  )
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      )
    }

    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-600 p-2 rounded-lg">
                <Globe className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">TARIFFED</h1>
                <p className="text-sm text-gray-600">美國關稅查詢系統</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            探索美國關稅與國際貿易
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            使用AI驅動的智能查詢系統，快速了解關稅政策、貿易數據和商品分類資訊
          </p>
        </div>

        {/* Search Section */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>智能查詢</span>
            </CardTitle>
            <CardDescription>
              輸入您的問題或商品名稱，獲取相關的關稅和貿易資訊
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-2">
              <Input
                placeholder="例如：絲綢的關稅率是多少？或搜尋 HTS 代碼..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAIQuery()}
                className="flex-1"
              />
              <Button onClick={handleAIQuery} disabled={loading}>
                {loading ? '查詢中...' : 'AI 查詢'}
              </Button>
              <Button variant="outline" onClick={handleHTSSearch} disabled={loading}>
                HTS 搜尋
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results Section */}
        <Tabs defaultValue="ai" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="ai">AI 回應</TabsTrigger>
            <TabsTrigger value="hts">HTS 搜尋結果</TabsTrigger>
          </TabsList>
          
          <TabsContent value="ai" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>AI 助手回應</CardTitle>
              </CardHeader>
              <CardContent>
                {aiResponse ? (
                  <div className="prose max-w-none">
                    <p className="whitespace-pre-wrap">{aiResponse}</p>
                  </div>
                ) : (
                  <p className="text-gray-500 italic">
                    請輸入您的問題並點擊「AI 查詢」來獲取回應
                  </p>
                )}
              </CardContent>
            </Card>
            
            {/* 圖表組件 */}
            {chartData && <ChartComponent chartData={chartData} />}
          </TabsContent>
          
          <TabsContent value="hts" className="space-y-4">
            {searchResults.length > 0 ? (
              searchResults.map((item) => (
                <Card key={item.id}>
                  <CardHeader>
                    <CardTitle className="text-lg">{item.id}</CardTitle>
                    <CardDescription>{item.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <p className="font-semibold text-sm text-gray-600">一般稅率</p>
                        <p className="text-lg">{item.general_rate}</p>
                      </div>
                      <div>
                        <p className="font-semibold text-sm text-gray-600">特殊稅率</p>
                        <p className="text-sm">{item.special_rate}</p>
                      </div>
                      <div>
                        <p className="font-semibold text-sm text-gray-600">計量單位</p>
                        <p className="text-lg">{item.unit_of_quantity}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-gray-500 italic text-center">
                    請輸入關鍵字並點擊「HTS 搜尋」來查看結果
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        {/* Features Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-blue-600" />
                <span>HTS 資料庫</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                完整的美國協調關稅表資料庫，包含所有商品分類和稅率資訊
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <span>貿易分析</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                深入分析貿易數據，了解不同國家的進出口趨勢和關稅影響
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5 text-purple-600" />
                <span>智能搜尋</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                使用AI技術提供自然語言查詢，快速找到您需要的關稅和貿易資訊
              </p>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>&copy; 2025 TARIFFED. 基於開源技術構建的關稅查詢系統。</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App

