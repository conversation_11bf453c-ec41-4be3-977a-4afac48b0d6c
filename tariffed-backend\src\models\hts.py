from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

class HTSSection(db.Model):
    __tablename__ = 'hts_sections'
    
    section_id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.Text, nullable=False)
    
    # Relationship
    chapters = db.relationship('HTSChapter', backref='section', lazy=True)

class HTSChapter(db.Model):
    __tablename__ = 'hts_chapters'
    
    chapter_id = db.Column(db.Integer, primary_key=True)
    section_id = db.Column(db.Integer, db.ForeignKey('hts_sections.section_id'), nullable=False)
    title = db.Column(db.Text, nullable=False)
    
    # Relationship
    headings = db.relationship('HTSHeading', backref='chapter', lazy=True)

class HTSHeading(db.Model):
    __tablename__ = 'hts_headings'
    
    heading_id = db.Column(db.String(10), primary_key=True)
    chapter_id = db.Column(db.Integer, db.<PERSON>ey('hts_chapters.chapter_id'), nullable=False)
    description = db.Column(db.Text, nullable=False)
    
    # Relationship
    subheadings = db.relationship('HTSSubheading', backref='heading', lazy=True)

class HTSSubheading(db.Model):
    __tablename__ = 'hts_subheadings'
    
    subheading_id = db.Column(db.String(20), primary_key=True)
    heading_id = db.Column(db.String(10), db.ForeignKey('hts_headings.heading_id'), nullable=False)
    description = db.Column(db.Text, nullable=False)
    unit_of_quantity = db.Column(db.String(50))
    general_rate = db.Column(db.String(50))
    special_rate = db.Column(db.String(50))
    column_2_rate = db.Column(db.String(50))
    
    # Relationship
    trade_data = db.relationship('TradeData', backref='subheading', lazy=True)

class Country(db.Model):
    __tablename__ = 'countries'
    
    country_id = db.Column(db.String(3), primary_key=True)
    country_name = db.Column(db.String(100), nullable=False)
    
    # Relationship
    trade_data = db.relationship('TradeData', backref='country', lazy=True)

class TradeData(db.Model):
    __tablename__ = 'trade_data'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    subheading_id = db.Column(db.String(20), db.ForeignKey('hts_subheadings.subheading_id'), nullable=False)
    country_id = db.Column(db.String(3), db.ForeignKey('countries.country_id'), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    import_value = db.Column(db.Numeric(15, 2))
    import_quantity = db.Column(db.Numeric(15, 2))

