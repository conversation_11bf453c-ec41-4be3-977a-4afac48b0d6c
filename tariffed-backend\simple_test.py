#!/usr/bin/env python3
"""
簡化的外部搜尋測試
"""

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

def test_tavily_basic():
    """測試基本的 Tavily 搜尋功能"""
    print("🔍 測試 Tavily 基本搜尋...")
    
    try:
        # 設置 API 密鑰
        os.environ['TAVILY_API_KEY'] = '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'
        
        from src.services.external_search_service import TavilySearchService
        
        tavily = TavilySearchService()
        
        # 測試簡單搜尋
        result = tavily.search("US tariff rates 2024", max_results=3)
        
        if result.get('success', False):
            print("✅ Tavily 搜尋成功")
            data = result.get('data', {})
            results = data.get('results', [])
            print(f"   找到 {len(results)} 個結果")
            
            if data.get('answer'):
                print(f"   答案: {data['answer'][:100]}...")
            
            for i, res in enumerate(results[:2], 1):
                print(f"   結果 {i}: {res.get('title', 'N/A')[:50]}...")
            
            return True
        else:
            print(f"❌ Tavily 搜尋失敗: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 測試異常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_processor():
    """測試搜尋結果處理器"""
    print("\n🔧 測試搜尋結果處理器...")
    
    try:
        from src.services.search_result_processor import SearchResultProcessor
        
        processor = SearchResultProcessor()
        
        # 模擬 Tavily 結果
        mock_data = {
            'answer': 'US tariff rates vary by product and country.',
            'query': 'US tariff rates',
            'results': [
                {
                    'title': 'USITC Tariff Database',
                    'url': 'https://hts.usitc.gov/',
                    'content': 'Official US tariff information.',
                    'score': 0.95
                }
            ]
        }
        
        processed = processor.process_tavily_results(mock_data)
        
        if processed.get('results'):
            print("✅ 結果處理成功")
            print(f"   可信度分數: {processed.get('credibility_score', 0):.2f}")
            return True
        else:
            print("❌ 結果處理失敗")
            return False
            
    except Exception as e:
        print(f"❌ 處理器測試異常: {e}")
        return False

def main():
    """主測試函數"""
    print("🌟 TARIFFED 外部搜尋簡化測試")
    print("=" * 40)
    
    # 測試 Tavily 服務
    tavily_success = test_tavily_basic()
    
    # 測試結果處理器
    processor_success = test_search_processor()
    
    print("\n" + "=" * 40)
    print("📋 測試結果:")
    print(f"   Tavily 搜尋: {'✅ 通過' if tavily_success else '❌ 失敗'}")
    print(f"   結果處理: {'✅ 通過' if processor_success else '❌ 失敗'}")
    
    if tavily_success and processor_success:
        print("\n🎉 外部搜尋功能基本測試通過！")
        return True
    else:
        print("\n⚠️ 部分測試失敗")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
