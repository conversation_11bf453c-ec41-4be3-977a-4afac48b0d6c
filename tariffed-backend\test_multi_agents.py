#!/usr/bin/env python3
"""
多代理系統測試腳本
"""

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

def test_multi_agent_system():
    """測試多代理系統"""
    print("🚀 開始測試多代理系統...")
    
    try:
        # 測試代理導入
        print("\n📦 測試代理導入...")
        from src.agents import initialize_agents, agent_coordinator
        print("✅ 代理模組導入成功")
        
        # 初始化代理系統
        print("\n🤖 初始化代理系統...")
        coordinator = initialize_agents()
        print("✅ 代理系統初始化成功")
        
        # 檢查代理狀態
        print("\n📊 檢查代理狀態...")
        status = coordinator.get_system_status()
        print(f"✅ 系統狀態: {status['system_health']}")
        print(f"✅ 代理數量: {status['total_agents']}")
        
        for agent_info in status['agents']:
            print(f"   - {agent_info['name']}: {agent_info['status']}")
        
        # 測試查詢路由
        print("\n🔍 測試查詢路由...")
        test_queries = [
            "什麼是 HTS 0101.21.00？",
            "分析中國進口電子產品的趨勢",
            "建議降低進口成本的方法"
        ]
        
        for query in test_queries:
            print(f"\n查詢: {query}")
            result = coordinator.route_query(query)
            
            if result['success']:
                print(f"✅ 路由成功，使用代理: {result['agent_used']}")
                print(f"   回應: {result['result'].get('response', 'N/A')[:100]}...")
            else:
                print(f"❌ 路由失敗: {result.get('error', 'Unknown error')}")
        
        # 測試協作查詢
        print("\n🤝 測試協作查詢...")
        collaborative_query = "分析馬匹進口的關稅情況並提供採購建議"
        agents = ['query_agent', 'analysis_agent', 'recommendation_agent']
        
        result = coordinator.collaborative_query(collaborative_query, agents)
        print(f"✅ 協作查詢完成")
        
        for agent_name, agent_result in result['collaborative_results'].items():
            if agent_result.get('success', False):
                print(f"   - {agent_name}: 成功")
            else:
                print(f"   - {agent_name}: 失敗 - {agent_result.get('error', 'Unknown')}")
        
        # 測試多代理服務
        print("\n🔧 測試多代理服務...")
        from src.services.multi_agent_service import multi_agent_service
        
        service_status = multi_agent_service.get_agent_status()
        print(f"✅ 多代理服務狀態: {service_status.get('system_health', 'unknown')}")
        
        # 測試服務查詢
        test_query = "查找電腦的 HTS 條目"
        service_result = multi_agent_service.process_query(test_query)
        
        if service_result.get('success', False):
            print(f"✅ 服務查詢成功，使用代理: {service_result.get('agent_used', 'unknown')}")
        else:
            print(f"❌ 服務查詢失敗: {service_result.get('error', 'Unknown error')}")
        
        print("\n🎉 多代理系統測試完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_agents():
    """測試個別代理"""
    print("\n🔬 測試個別代理...")
    
    try:
        from src.agents.query_agent import QueryAgent
        from src.agents.analysis_agent import AnalysisAgent
        from src.agents.recommendation_agent import RecommendationAgent
        
        # 測試查詢代理
        print("\n🔍 測試查詢代理...")
        query_agent = QueryAgent()
        query_result = query_agent.process_query("什麼是 HTS 0101.21.00？")
        
        if query_result.get('success', False):
            print("✅ 查詢代理測試成功")
        else:
            print(f"❌ 查詢代理測試失敗: {query_result.get('error', 'Unknown')}")
        
        # 測試分析代理
        print("\n📊 測試分析代理...")
        analysis_agent = AnalysisAgent()
        analysis_result = analysis_agent.process_query("分析進口趨勢")
        
        if analysis_result.get('success', False):
            print("✅ 分析代理測試成功")
        else:
            print(f"❌ 分析代理測試失敗: {analysis_result.get('error', 'Unknown')}")
        
        # 測試建議代理
        print("\n💡 測試建議代理...")
        recommendation_agent = RecommendationAgent()
        recommendation_result = recommendation_agent.process_query("建議降低成本的方法")
        
        if recommendation_result.get('success', False):
            print("✅ 建議代理測試成功")
        else:
            print(f"❌ 建議代理測試失敗: {recommendation_result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 個別代理測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🌟 TARIFFED 多代理系統測試")
    print("=" * 50)
    
    # 測試個別代理
    individual_success = test_individual_agents()
    
    # 測試多代理系統
    system_success = test_multi_agent_system()
    
    print("\n" + "=" * 50)
    print("📋 測試結果總結:")
    print(f"   個別代理測試: {'✅ 通過' if individual_success else '❌ 失敗'}")
    print(f"   多代理系統測試: {'✅ 通過' if system_success else '❌ 失敗'}")
    
    if individual_success and system_success:
        print("\n🎊 所有測試通過！多代理系統已準備就緒。")
        return True
    else:
        print("\n⚠️ 部分測試失敗，請檢查錯誤信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
