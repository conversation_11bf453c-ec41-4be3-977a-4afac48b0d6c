#!/usr/bin/env python3
"""
TARIFFED 系統綜合功能測試
"""

import os
import sys
import time
import requests
import json
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

class TariffedTester:
    """TARIFFED 系統測試器"""
    
    def __init__(self):
        self.base_url = "http://localhost:5001/api"
        self.test_results = {}
        self.start_time = datetime.now()
        
    def log_test(self, test_name, success, details=""):
        """記錄測試結果"""
        status = "✅ 通過" if success else "❌ 失敗"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        
        self.test_results[test_name] = {
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
    
    def test_external_search_api(self):
        """測試外部搜尋 API"""
        print("\n🔍 測試外部搜尋 API...")
        
        try:
            # 測試 Tavily 搜尋服務
            from src.services.external_search_service import external_search_service
            
            result = external_search_service.search("US tariff rates 2024", max_results=3)
            
            if result.get('success', False):
                data = result.get('data', {})
                results_count = len(data.get('results', []))
                self.log_test("外部搜尋 API - Tavily 服務", True, f"找到 {results_count} 個結果")
                
                # 測試搜尋結果處理
                from src.services.search_result_processor import search_result_processor
                processed = search_result_processor.process_tavily_results(data)
                
                if processed.get('results'):
                    credibility = processed.get('credibility_score', 0)
                    self.log_test("搜尋結果處理", True, f"可信度分數: {credibility:.2f}")
                else:
                    self.log_test("搜尋結果處理", False, "處理結果為空")
                
                return True
            else:
                self.log_test("外部搜尋 API - Tavily 服務", False, result.get('error', 'Unknown error'))
                return False
                
        except Exception as e:
            self.log_test("外部搜尋 API", False, str(e))
            return False
    
    def test_hts_data_import(self):
        """測試 HTS 資料導入"""
        print("\n🏷️ 測試 HTS 資料導入...")
        
        try:
            # 測試資料庫連接和查詢
            from src.services.ai_agent import TariffAIAgent
            
            ai_agent = TariffAIAgent()
            
            # 測試 HTS 建議功能
            suggestions = ai_agent.get_hts_suggestions("horse", limit=3)
            
            if suggestions and len(suggestions) > 0:
                self.log_test("HTS 資料查詢", True, f"找到 {len(suggestions)} 個建議")
                
                # 檢查資料完整性
                first_suggestion = suggestions[0]
                if 'id' in first_suggestion and 'description' in first_suggestion:
                    self.log_test("HTS 資料完整性", True, f"樣本: {first_suggestion['id']}")
                else:
                    self.log_test("HTS 資料完整性", False, "資料欄位不完整")
                
                return True
            else:
                self.log_test("HTS 資料查詢", False, "沒有找到 HTS 建議")
                return False
                
        except Exception as e:
            self.log_test("HTS 資料導入", False, str(e))
            return False
    
    def test_visualization_features(self):
        """測試視覺化功能"""
        print("\n📊 測試資料視覺化功能...")
        
        try:
            from src.services.visualization_service import visualization_service
            from src.services.chart_recommendation_service import chart_recommendation_service
            
            # 測試圖表生成
            trade_data = [
                {'date': '2022-01', 'value': 1000000},
                {'date': '2022-02', 'value': 1200000},
                {'date': '2022-03', 'value': 1400000}
            ]
            
            chart = visualization_service.generate_trade_trend_chart(trade_data, 'line')
            
            if chart.get('type') == 'line':
                stats = chart.get('statistics', {})
                growth_rate = stats.get('growth_rate', 0)
                self.log_test("圖表生成功能", True, f"線圖生成成功，增長率: {growth_rate}%")
            else:
                self.log_test("圖表生成功能", False, "圖表生成失敗")
                return False
            
            # 測試圖表建議
            recommendations = chart_recommendation_service.recommend_charts("分析進口趨勢")
            
            if recommendations.get('success', False):
                recs = recommendations.get('recommendations', [])
                if recs:
                    top_rec = recs[0]
                    confidence = top_rec.get('confidence', 0) * 100
                    self.log_test("智能圖表建議", True, f"推薦: {top_rec['chart_type']} (信心度: {confidence:.0f}%)")
                else:
                    self.log_test("智能圖表建議", False, "沒有生成建議")
                    return False
            else:
                self.log_test("智能圖表建議", False, recommendations.get('error', 'Unknown error'))
                return False
            
            return True
            
        except Exception as e:
            self.log_test("資料視覺化功能", False, str(e))
            return False
    
    def test_ai_response_optimization(self):
        """測試 AI 回應優化"""
        print("\n🤖 測試 AI 回應優化...")
        
        try:
            from src.prompts.professional_prompts import ProfessionalPrompts, ResponseTemplates, QualityEnhancers
            
            # 測試專業提示詞
            query_prompt = ProfessionalPrompts.get_query_agent_prompt()
            analysis_prompt = ProfessionalPrompts.get_analysis_agent_prompt()
            recommendation_prompt = ProfessionalPrompts.get_recommendation_agent_prompt()
            
            if len(query_prompt) > 500 and len(analysis_prompt) > 500 and len(recommendation_prompt) > 500:
                self.log_test("專業提示詞系統", True, "所有代理提示詞已優化")
            else:
                self.log_test("專業提示詞系統", False, "提示詞長度不足")
                return False
            
            # 測試回應模板
            hts_template = ResponseTemplates.hts_classification_template()
            trend_template = ResponseTemplates.trend_analysis_template()
            
            if "{hts_code}" in hts_template and "{overall_trend}" in trend_template:
                self.log_test("回應模板系統", True, "模板變量正確")
            else:
                self.log_test("回應模板系統", False, "模板變量缺失")
                return False
            
            # 測試品質增強器
            sample_response = "這是一個測試回應。"
            enhanced = QualityEnhancers.add_professional_context(sample_response, "hts_classification")
            
            if len(enhanced) > len(sample_response):
                self.log_test("品質增強器", True, "回應已增強")
            else:
                self.log_test("品質增強器", False, "增強器未生效")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("AI 回應優化", False, str(e))
            return False
    
    def test_multi_agent_architecture(self):
        """測試多代理架構"""
        print("\n🤖 測試多代理架構...")
        
        try:
            from src.services.multi_agent_service import multi_agent_service
            
            # 測試代理路由
            test_queries = [
                ("查找馬匹的 HTS 條目", "query_agent"),
                ("分析進口趨勢數據", "analysis_agent"),
                ("建議成本優化方案", "recommendation_agent")
            ]
            
            successful_routes = 0
            
            for query, expected_agent in test_queries:
                result = multi_agent_service.process_query(query)
                
                if result.get('success', False):
                    agent_used = result.get('agent_used', '')
                    if agent_used == expected_agent:
                        successful_routes += 1
                        self.log_test(f"代理路由 - {expected_agent}", True, f"正確路由到 {agent_used}")
                    else:
                        self.log_test(f"代理路由 - {expected_agent}", False, f"路由到 {agent_used}")
                else:
                    self.log_test(f"代理路由 - {expected_agent}", False, result.get('error', 'Unknown error'))
            
            if successful_routes >= 2:  # 至少2個成功
                self.log_test("多代理架構", True, f"{successful_routes}/3 代理路由成功")
                return True
            else:
                self.log_test("多代理架構", False, f"只有 {successful_routes}/3 代理路由成功")
                return False
                
        except Exception as e:
            self.log_test("多代理架構", False, str(e))
            return False
    
    def test_api_documentation(self):
        """測試 API 文檔"""
        print("\n📚 測試 API 文檔...")
        
        try:
            # 測試 Swagger 文檔端點
            response = requests.get(f"{self.base_url}/docs/", timeout=10)
            
            if response.status_code == 200:
                content = response.text
                if "swagger" in content.lower() or "api" in content.lower():
                    self.log_test("Swagger API 文檔", True, "文檔頁面可訪問")
                else:
                    self.log_test("Swagger API 文檔", False, "文檔內容異常")
                    return False
            else:
                self.log_test("Swagger API 文檔", False, f"HTTP {response.status_code}")
                return False
            
            # 測試 API 端點可用性
            endpoints_to_test = [
                "/hts/subheadings",
                "/agents/status",
                "/search/external",
                "/visualization/recommendations"
            ]
            
            available_endpoints = 0
            
            for endpoint in endpoints_to_test:
                try:
                    if endpoint == "/search/external" or endpoint == "/visualization/recommendations":
                        # POST 端點
                        response = requests.post(f"{self.base_url}{endpoint}", 
                                               json={"query": "test"}, timeout=5)
                    else:
                        # GET 端點
                        response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                    
                    if response.status_code in [200, 400, 422]:  # 400/422 表示端點存在但參數錯誤
                        available_endpoints += 1
                        self.log_test(f"API 端點 {endpoint}", True, f"HTTP {response.status_code}")
                    else:
                        self.log_test(f"API 端點 {endpoint}", False, f"HTTP {response.status_code}")
                        
                except requests.exceptions.RequestException as e:
                    self.log_test(f"API 端點 {endpoint}", False, str(e))
            
            if available_endpoints >= 3:
                self.log_test("API 文檔和端點", True, f"{available_endpoints}/4 端點可用")
                return True
            else:
                self.log_test("API 文檔和端點", False, f"只有 {available_endpoints}/4 端點可用")
                return False
                
        except requests.exceptions.ConnectionError:
            self.log_test("API 文檔", False, "無法連接到服務器")
            return False
        except Exception as e:
            self.log_test("API 文檔", False, str(e))
            return False
    
    def run_individual_tests(self):
        """運行個別功能測試"""
        print("🧪 開始個別功能測試...")
        print("=" * 60)
        
        tests = [
            ("外部搜尋 API", self.test_external_search_api),
            ("HTS 資料導入", self.test_hts_data_import),
            ("資料視覺化", self.test_visualization_features),
            ("AI 回應優化", self.test_ai_response_optimization),
            ("多代理架構", self.test_multi_agent_architecture),
            ("API 文檔", self.test_api_documentation)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                results[test_name] = test_func()
                time.sleep(1)  # 避免過快請求
            except Exception as e:
                self.log_test(test_name, False, f"測試異常: {e}")
                results[test_name] = False
        
        return results
    
    def generate_test_report(self, individual_results, integration_result=None):
        """生成測試報告"""
        print("\n" + "=" * 60)
        print("📋 測試結果總結")
        print("=" * 60)
        
        total_tests = len(individual_results)
        passed_tests = sum(1 for result in individual_results.values() if result)
        
        print(f"\n個別功能測試結果:")
        for test_name, result in individual_results.items():
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"   {status} {test_name}")
        
        if integration_result is not None:
            print(f"\n整合測試結果:")
            status = "✅ 通過" if integration_result else "❌ 失敗"
            print(f"   {status} 系統整合測試")
        
        print(f"\n📊 統計:")
        print(f"   個別測試通過率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        
        if integration_result is not None:
            overall_success = passed_tests >= 5 and integration_result
            print(f"   整體系統狀態: {'🎉 優秀' if overall_success else '⚠️ 需要改進'}")
        
        print(f"   測試執行時間: {(datetime.now() - self.start_time).total_seconds():.1f} 秒")
        
        return passed_tests >= 5  # 至少5個功能通過

def main():
    """主測試函數"""
    print("🌟 TARIFFED 系統綜合功能測試")
    print("=" * 60)
    
    tester = TariffedTester()
    
    # 第一階段：個別功能測試
    print("第一階段：個別功能測試")
    individual_results = tester.run_individual_tests()
    
    # 生成報告
    success = tester.generate_test_report(individual_results)
    
    if success:
        print("\n🎊 個別功能測試完成！系統各模組運行正常。")
        print("準備進行整合測試...")
    else:
        print("\n⚠️ 部分功能測試失敗，請檢查錯誤信息。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
