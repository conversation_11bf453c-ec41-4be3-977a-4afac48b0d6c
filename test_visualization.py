#!/usr/bin/env python3
"""
資料視覺化功能測試腳本
"""

import os
import sys
import json

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

def test_visualization_service():
    """測試視覺化服務"""
    print("📊 測試視覺化服務...")
    
    try:
        from src.services.visualization_service import visualization_service
        
        # 測試貿易趨勢圖表
        print("\n📈 測試貿易趨勢圖表...")
        trade_data = [
            {'date': '2022-01', 'value': 1000000, 'label': '2022年1月'},
            {'date': '2022-02', 'value': 1200000, 'label': '2022年2月'},
            {'date': '2022-03', 'value': 1100000, 'label': '2022年3月'},
            {'date': '2022-04', 'value': 1300000, 'label': '2022年4月'},
            {'date': '2022-05', 'value': 1500000, 'label': '2022年5月'}
        ]
        
        trend_chart = visualization_service.generate_trade_trend_chart(trade_data, 'line')
        
        if trend_chart.get('type') == 'line':
            print("✅ 貿易趨勢圖表生成成功")
            print(f"   圖表標題: {trend_chart.get('title', 'N/A')}")
            print(f"   數據點數: {len(trend_chart.get('data', {}).get('labels', []))}")
            
            # 檢查統計信息
            stats = trend_chart.get('statistics', {})
            if stats:
                print(f"   平均值: {stats.get('mean_value', 'N/A')}")
                print(f"   增長率: {stats.get('growth_rate', 'N/A')}%")
                print(f"   趨勢方向: {stats.get('trend_direction', 'N/A')}")
        else:
            print(f"❌ 貿易趨勢圖表生成失敗: {trend_chart}")
        
        # 測試關稅比較圖表
        print("\n🏷️ 測試關稅比較圖表...")
        tariff_data = [
            {'hts_code': '0101.21.00', 'description': 'Horses', 'general_rate': 'Free', 'special_rate': 'Free'},
            {'hts_code': '8471.30.01', 'description': 'Computers', 'general_rate': '2.5%', 'special_rate': 'Free'},
            {'hts_code': '8703.23.00', 'description': 'Cars', 'general_rate': '2.5%', 'special_rate': '2.5%'},
            {'hts_code': '6204.62.40', 'description': 'Textiles', 'general_rate': '16.6%', 'special_rate': '15.3%'}
        ]
        
        tariff_chart = visualization_service.generate_tariff_comparison_chart(tariff_data)
        
        if tariff_chart.get('type') == 'bar':
            print("✅ 關稅比較圖表生成成功")
            print(f"   圖表標題: {tariff_chart.get('title', 'N/A')}")
            
            # 檢查統計信息
            stats = tariff_chart.get('statistics', {})
            if stats:
                print(f"   商品類別數: {stats.get('total_categories', 'N/A')}")
                print(f"   平均一般稅率: {stats.get('general_rate_avg', 'N/A'):.1f}%")
                print(f"   免稅商品數: {stats.get('free_trade_count', 'N/A')}")
        else:
            print(f"❌ 關稅比較圖表生成失敗: {tariff_chart}")
        
        # 測試市場份額圓餅圖
        print("\n🌍 測試市場份額圓餅圖...")
        country_data = [
            {'country': '中國', 'value': 5000000, 'percentage': 35.2},
            {'country': '德國', 'value': 2500000, 'percentage': 18.7},
            {'country': '日本', 'value': 1800000, 'percentage': 12.4},
            {'country': '韓國', 'value': 1200000, 'percentage': 8.9},
            {'country': '其他', 'value': 3500000, 'percentage': 24.8}
        ]
        
        market_chart = visualization_service.generate_country_market_share_chart(country_data)
        
        if market_chart.get('type') == 'pie':
            print("✅ 市場份額圓餅圖生成成功")
            print(f"   圖表標題: {market_chart.get('title', 'N/A')}")
            
            # 檢查統計信息
            stats = market_chart.get('statistics', {})
            if stats:
                print(f"   總貿易值: ${stats.get('total_trade_value', 'N/A'):,}")
                print(f"   最大貿易夥伴: {stats.get('top_country', 'N/A')}")
                print(f"   前三國集中度: {stats.get('concentration_ratio_top3', 'N/A'):.1f}%")
        else:
            print(f"❌ 市場份額圓餅圖生成失敗: {market_chart}")
        
        return True
        
    except Exception as e:
        print(f"❌ 視覺化服務測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_recommendation_service():
    """測試圖表建議服務"""
    print("\n💡 測試圖表建議服務...")
    
    try:
        from src.services.chart_recommendation_service import chart_recommendation_service
        
        # 測試查詢分析
        test_queries = [
            "分析中國進口電子產品的趨勢",
            "比較不同國家的關稅稅率",
            "展示各商品類別的市場份額",
            "統計進口數據的分佈情況"
        ]
        
        for query in test_queries:
            print(f"\n🔍 測試查詢: {query}")
            
            recommendations = chart_recommendation_service.recommend_charts(query)
            
            if recommendations.get('success', False):
                print("✅ 圖表建議生成成功")
                recs = recommendations.get('recommendations', [])
                print(f"   建議數量: {len(recs)}")
                
                for i, rec in enumerate(recs[:2], 1):
                    print(f"   建議 {i}: {rec['chart_type']} (信心度: {rec['confidence']:.1%})")
                    print(f"           理由: {rec['reason']}")
            else:
                print(f"❌ 圖表建議失敗: {recommendations.get('error', 'Unknown error')}")
        
        # 測試圖表模板
        print("\n📋 測試圖表模板...")
        chart_types = ['line', 'bar', 'pie', 'scatter']
        
        for chart_type in chart_types:
            template = chart_recommendation_service.get_chart_config_template(chart_type)
            
            if template.get('type') == chart_type:
                print(f"✅ {chart_type} 模板獲取成功: {template.get('title', 'N/A')}")
            else:
                print(f"❌ {chart_type} 模板獲取失敗")
        
        return True
        
    except Exception as e:
        print(f"❌ 圖表建議服務測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_agent_integration():
    """測試多代理系統整合"""
    print("\n🤖 測試多代理系統整合...")
    
    try:
        from src.services.multi_agent_service import multi_agent_service
        
        # 測試視覺化生成
        print("\n📊 測試視覺化生成...")
        visualization_data = {
            'query': '分析貿易趨勢',
            'trade_data': [
                {'year': 2020, 'value': 1000000},
                {'year': 2021, 'value': 1200000},
                {'year': 2022, 'value': 1100000},
                {'year': 2023, 'value': 1400000}
            ]
        }
        
        viz_result = multi_agent_service.generate_visualization(visualization_data, 'line')
        
        if viz_result.get('success', False):
            print("✅ 多代理視覺化生成成功")
            print(f"   圖表類型: {viz_result.get('chart_type', 'N/A')}")
            
            chart_config = viz_result.get('chart_config', {})
            if chart_config:
                print(f"   圖表標題: {chart_config.get('title', 'N/A')}")
        else:
            print(f"❌ 多代理視覺化生成失敗: {viz_result.get('error', 'Unknown error')}")
        
        # 測試圖表建議
        print("\n💡 測試多代理圖表建議...")
        rec_result = multi_agent_service.get_chart_recommendations(
            "展示進口趨勢變化",
            [{'year': 2023, 'value': 1000000}]
        )
        
        if rec_result.get('success', False):
            print("✅ 多代理圖表建議成功")
            recommendations = rec_result.get('recommendations', [])
            print(f"   建議數量: {len(recommendations)}")
            
            if recommendations:
                top_rec = recommendations[0]
                print(f"   首選建議: {top_rec['chart_type']} (信心度: {top_rec['confidence']:.1%})")
        else:
            print(f"❌ 多代理圖表建議失敗: {rec_result.get('error', 'Unknown error')}")
        
        # 測試增強分析
        print("\n🚀 測試帶視覺化的增強分析...")
        enhanced_result = multi_agent_service.analyze_with_visualization(
            "分析電子產品進口趨勢並提供視覺化建議"
        )
        
        if enhanced_result.get('success', False):
            print("✅ 帶視覺化的增強分析成功")
            print(f"   分析結果長度: {len(enhanced_result.get('enhanced_response', ''))}")
            print(f"   圖表建議數量: {len(enhanced_result.get('chart_recommendations', []))}")
            
            if enhanced_result.get('visualization'):
                print("   包含視覺化配置")
        else:
            print(f"❌ 帶視覺化的增強分析失敗: {enhanced_result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多代理系統整合測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """測試 API 端點（需要服務器運行）"""
    print("\n🌐 測試視覺化 API 端點...")
    
    try:
        import requests
        
        base_url = "http://localhost:5001/api"
        
        # 測試圖表建議 API
        print("\n💡 測試圖表建議 API...")
        response = requests.post(
            f"{base_url}/visualization/recommendations",
            json={
                "query": "分析中國進口電子產品的趨勢",
                "data": [{"year": 2023, "value": 1000000}]
            },
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 圖表建議 API 測試成功")
            data = response.json()
            recommendations = data.get('data', [])
            print(f"   建議數量: {len(recommendations)}")
        else:
            print(f"❌ 圖表建議 API 測試失敗: {response.status_code}")
        
        # 測試視覺化生成 API
        print("\n📊 測試視覺化生成 API...")
        response = requests.post(
            f"{base_url}/visualization/generate",
            json={
                "data": {
                    "trade_data": [
                        {"year": 2020, "value": 1000000},
                        {"year": 2021, "value": 1200000},
                        {"year": 2022, "value": 1400000}
                    ]
                },
                "chart_type": "line"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 視覺化生成 API 測試成功")
            data = response.json()
            chart_config = data.get('data', {})
            print(f"   圖表類型: {chart_config.get('type', 'N/A')}")
        else:
            print(f"❌ 視覺化生成 API 測試失敗: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("⚠️ 無法連接到服務器，跳過 API 測試")
        return True
    except Exception as e:
        print(f"❌ API 端點測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🌟 TARIFFED 資料視覺化功能測試")
    print("=" * 50)
    
    # 測試各個組件
    viz_service_success = test_visualization_service()
    chart_rec_success = test_chart_recommendation_service()
    integration_success = test_multi_agent_integration()
    api_success = test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("📋 測試結果總結:")
    print(f"   視覺化服務: {'✅ 通過' if viz_service_success else '❌ 失敗'}")
    print(f"   圖表建議服務: {'✅ 通過' if chart_rec_success else '❌ 失敗'}")
    print(f"   多代理整合: {'✅ 通過' if integration_success else '❌ 失敗'}")
    print(f"   API 端點: {'✅ 通過' if api_success else '❌ 失敗'}")
    
    all_success = all([viz_service_success, chart_rec_success, integration_success, api_success])
    
    if all_success:
        print("\n🎊 所有測試通過！資料視覺化功能已成功整合。")
        print("\n🚀 功能特色:")
        print("   • 智能圖表建議")
        print("   • 多種圖表類型支援")
        print("   • 統計分析整合")
        print("   • 專業視覺化配置")
        print("   • 前端組件支援")
        return True
    else:
        print("\n⚠️ 部分測試失敗，請檢查錯誤信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
