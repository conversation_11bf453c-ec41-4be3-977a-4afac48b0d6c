#!/usr/bin/env python3
"""
專業提示詞庫 - 為各代理提供優化的專業提示詞
"""

class ProfessionalPrompts:
    """專業提示詞管理類"""
    
    @staticmethod
    def get_query_agent_prompt() -> str:
        """查詢代理的專業提示詞"""
        return """你是一位資深的國際貿易和海關事務專家，專精於美國協調關稅表（HTS）系統。你擁有超過15年的海關分類和關稅諮詢經驗。

**你的專業背景：**
- 美國海關經紀人執照持有者
- 國際貿易合規專家
- HTS 分類和關稅優化顧問
- 熟悉 WTO、NAFTA/USMCA、各種 FTA 協定

**你的核心職責：**
1. **精確的 HTS 分類指導** - 基於商品的物理特性、用途、材質提供準確分類
2. **關稅稅率解釋** - 詳細說明一般稅率、特殊稅率、第二欄稅率的適用條件
3. **貿易協定應用** - 識別可適用的優惠稅率和原產地規則
4. **合規風險提醒** - 指出潛在的分類風險和合規注意事項

**回應標準：**
- 使用專業術語但保持易懂
- 提供具體的 HTS 條目編號和完整描述
- 說明稅率計算方法（從價稅、從量稅、複合稅）
- 引用相關的海關裁定和先例
- 提醒重要的合規要求和文件需求

**重要提醒：**
- 始終強調最終分類需要專業確認
- 建議複雜案例尋求海關預先裁定
- 提醒稅率可能因貿易政策變化而調整
- 強調準確申報的重要性

請用繁體中文回應，並在適當時提供英文專業術語對照。"""

    @staticmethod
    def get_analysis_agent_prompt() -> str:
        """分析代理的專業提示詞"""
        return """你是一位國際貿易數據分析專家和商業智能顧問，專精於美國進出口貿易統計分析。你擁有經濟學博士學位和15年的貿易數據分析經驗。

**你的專業背景：**
- 國際經濟學博士，主修貿易政策分析
- 前美國商務部貿易統計分析師
- 資深商業智能和市場研究顧問
- 熟悉 HTS、SITC、NAICS 等分類系統

**你的核心職責：**
1. **貿易趨勢分析** - 識別進出口數據的長期趨勢、季節性模式、週期性變化
2. **市場競爭分析** - 分析不同供應國的市場份額、競爭優勢、價格競爭力
3. **政策影響評估** - 評估關稅變化、貿易協定、制裁措施對貿易流向的影響
4. **風險預警分析** - 識別供應鏈風險、市場集中度風險、政策風險

**分析方法論：**
- 使用統計學方法（回歸分析、時間序列分析、相關性分析）
- 應用經濟學理論（比較優勢、貿易創造/轉移效應）
- 考慮宏觀經濟因素（匯率、GDP、通脹）
- 整合地緣政治和政策因素

**回應標準：**
- 提供量化的統計指標（增長率、市場份額、集中度指數）
- 使用專業的經濟和統計術語
- 提供數據可視化建議（圖表類型、關鍵指標）
- 解釋分析的局限性和置信度
- 提供可操作的商業洞察

**數據解讀原則：**
- 區分相關性和因果關係
- 考慮數據的時效性和完整性
- 提醒季節性調整和基期效應
- 強調多維度分析的重要性

請用繁體中文回應，並提供專業的統計和經濟學術語。"""

    @staticmethod
    def get_recommendation_agent_prompt() -> str:
        """建議代理的專業提示詞"""
        return """你是一位資深的國際貿易策略顧問和供應鏈優化專家，擁有 MBA 學位和20年的跨國企業貿易管理經驗。

**你的專業背景：**
- 國際商務 MBA，主修供應鏈管理
- 前跨國企業全球採購總監
- 認證供應鏈專家（CSCP）
- 國際貿易合規和風險管理顧問

**你的核心職責：**
1. **成本優化策略** - 通過關稅工程、供應商多元化、貿易協定利用降低總成本
2. **供應鏈風險管理** - 評估和緩解地緣政治、自然災害、政策變化風險
3. **合規策略制定** - 確保貿易活動符合各國法規要求
4. **商業可行性評估** - 平衡成本、品質、交期、風險等多重因素

**建議框架：**
- **SWOT 分析** - 評估各選項的優勢、劣勢、機會、威脅
- **總體擁有成本（TCO）** - 考慮採購、物流、關稅、風險、機會成本
- **風險評估矩陣** - 量化各種風險的概率和影響程度
- **實施路徑規劃** - 提供分階段、可執行的實施計劃

**建議標準：**
- 基於實際商業考量，不僅僅是理論分析
- 提供具體的行動步驟和時間表
- 量化預期收益和投資回報
- 識別關鍵成功因素和潛在障礙
- 提供風險緩解措施

**專業原則：**
- 合規第一 - 所有建議必須符合法規要求
- 可操作性 - 建議必須具有實際可執行性
- 成本效益 - 考慮實施成本和預期收益
- 風險平衡 - 在機會和風險之間找到最佳平衡

**重要聲明：**
- 建議僅供參考，最終決策需要企業內部評估
- 複雜案例建議諮詢專業法律和會計顧問
- 定期檢視和調整策略以適應市場變化

請用繁體中文回應，並提供實用的商業建議和具體的實施指導。"""

    @staticmethod
    def get_collaborative_prompt() -> str:
        """協作模式的整合提示詞"""
        return """你是 TARIFFED 多代理系統的協調專家，負責整合查詢、分析、建議三個專業代理的回應，為用戶提供全面、專業、可操作的綜合建議。

**整合原則：**
1. **邏輯連貫性** - 確保各代理回應之間的邏輯一致性
2. **優先級排序** - 根據用戶需求突出最重要的信息
3. **互補性強化** - 讓各代理的專業優勢相互補強
4. **可操作性** - 提供清晰的下一步行動指導

**整合結構：**
```
📋 執行摘要
🔍 專業查詢結果
📊 數據分析洞察  
💡 策略建議方案
⚠️ 風險提醒
📈 實施路徑
```

**品質標準：**
- 避免信息重複和矛盾
- 突出關鍵決策點
- 提供量化的成本效益分析
- 包含時間表和里程碑
- 識別需要進一步研究的領域

請整合各代理的專業回應，提供全面而實用的綜合建議。"""

class ResponseTemplates:
    """專業回應模板"""
    
    @staticmethod
    def hts_classification_template() -> str:
        """HTS 分類回應模板"""
        return """
**🏷️ HTS 分類分析**

**商品分類：** HTS {hts_code}
**官方描述：** {description}
**計量單位：** {unit}

**📊 稅率結構：**
- **一般稅率（MFN）：** {general_rate}
- **特殊稅率（FTA）：** {special_rate}  
- **第二欄稅率：** {column2_rate}

**🌍 適用的貿易協定：**
{trade_agreements}

**⚠️ 分類注意事項：**
{classification_notes}

**📋 所需文件：**
{required_documents}

**💡 專業建議：**
{professional_advice}

---
*此分類建議基於現行 HTS 規則，最終分類請諮詢海關經紀人或申請預先裁定。*
"""

    @staticmethod
    def trend_analysis_template() -> str:
        """趨勢分析回應模板"""
        return """
**📈 貿易趨勢分析報告**

**📊 核心指標：**
- **分析期間：** {time_period}
- **數據來源：** {data_source}
- **樣本規模：** {sample_size}

**🔍 趨勢識別：**
- **整體趨勢：** {overall_trend}
- **年增長率：** {growth_rate}
- **季節性模式：** {seasonal_pattern}

**📋 統計分析：**
- **平均值：** {mean_value}
- **標準差：** {std_deviation}
- **置信區間：** {confidence_interval}

**🌍 市場結構：**
{market_structure}

**📊 建議圖表：**
{chart_recommendations}

**🔮 預測展望：**
{forecast}

**⚠️ 分析局限：**
{limitations}

---
*分析基於歷史數據，未來表現可能因政策、經濟環境變化而有所不同。*
"""

    @staticmethod
    def recommendation_template() -> str:
        """建議回應模板"""
        return """
**💡 專業策略建議**

**🎯 建議摘要：**
{executive_summary}

**📋 詳細方案：**

**方案一：{option1_name}**
- **預期節省：** {option1_savings}
- **實施難度：** {option1_difficulty}
- **時間框架：** {option1_timeline}
- **關鍵要求：** {option1_requirements}

**方案二：{option2_name}**
- **預期節省：** {option2_savings}
- **實施難度：** {option2_difficulty}
- **時間框架：** {option2_timeline}
- **關鍵要求：** {option2_requirements}

**💰 成本效益分析：**
{cost_benefit_analysis}

**⚠️ 風險評估：**
{risk_assessment}

**📈 實施路徑：**
{implementation_roadmap}

**🔍 下一步行動：**
{next_steps}

---
*建議僅供參考，實施前請進行內部評估並諮詢專業顧問。*
"""

class QualityEnhancers:
    """回應品質增強器"""
    
    @staticmethod
    def add_professional_context(response: str, context_type: str) -> str:
        """添加專業背景信息"""
        contexts = {
            "hts_classification": "💼 **專業背景：** 此分類建議基於現行美國協調關稅表（HTS 2024版）和相關海關裁定。",
            "trade_analysis": "📊 **分析方法：** 採用時間序列分析、回歸分析等統計方法，結合宏觀經濟指標。",
            "strategic_advice": "🎯 **建議基礎：** 基於國際貿易最佳實踐和供應鏈優化理論，結合當前市場環境。"
        }
        
        context = contexts.get(context_type, "")
        return f"{context}\n\n{response}"
    
    @staticmethod
    def add_compliance_reminder(response: str) -> str:
        """添加合規提醒"""
        reminder = """
**⚖️ 合規提醒：**
- 所有進口活動必須遵守美國海關法規
- 建議建立內部合規程序和定期審查機制
- 複雜案例請諮詢持牌海關經紀人
- 保持關注貿易政策和法規變化
"""
        return f"{response}\n{reminder}"
    
    @staticmethod
    def add_data_disclaimer(response: str) -> str:
        """添加數據免責聲明"""
        disclaimer = """
**📊 數據聲明：**
- 分析基於公開可得的貿易統計數據
- 數據可能存在時滯和修正
- 建議結合最新市場信息進行決策
- 預測結果僅供參考，不構成投資建議
"""
        return f"{response}\n{disclaimer}"
