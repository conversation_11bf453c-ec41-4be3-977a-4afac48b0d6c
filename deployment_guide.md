# TARIFFED 部署指南

## 系統需求

### 軟體需求
- Python 3.11 或更高版本
- Node.js 20.x 或更高版本
- npm 或 pnpm 套件管理器
- Git (用於版本控制)

### 硬體需求
- 最小記憶體: 2GB RAM
- 建議記憶體: 4GB RAM 或更多
- 磁碟空間: 至少 1GB 可用空間
- 網路: 穩定的網際網路連接 (用於 AI API 調用)

## 環境變數設定

在部署前，請確保設定以下環境變數：

```bash
# OpenAI API 設定 (必需)
export OPENAI_API_KEY="your-openai-api-key"
export OPENAI_API_BASE="https://api.openai.com/v1"

# Flask 設定 (可選)
export FLASK_ENV="production"
export FLASK_DEBUG="False"
```

## 快速部署步驟

### 1. 下載專案檔案
```bash
# 假設您已經有專案檔案
cd /path/to/tariffed-project
```

### 2. 後端設定
```bash
cd tariffed-backend

# 建立虛擬環境
python3 -m venv venv
source venv/bin/activate

# 安裝依賴
pip install -r requirements.txt

# 初始化資料庫 (如果需要)
python src/data/sample_data.py
```

### 3. 前端建置 (如果需要重新建置)
```bash
cd ../tariffed-frontend

# 安裝依賴
npm install
# 或使用 pnpm
pnpm install

# 建置前端
npm run build
# 或使用 pnpm
pnpm run build

# 複製建置檔案到後端
cp -r dist/* ../tariffed-backend/src/static/
```

### 4. 啟動應用
```bash
cd ../tariffed-backend
source venv/bin/activate
python run_integrated.py
```

應用將在 `http://localhost:5001` 上運行。

## 生產環境部署

### 使用 Gunicorn (推薦)

1. 安裝 Gunicorn:
```bash
pip install gunicorn
```

2. 建立 Gunicorn 配置檔案 `gunicorn.conf.py`:
```python
bind = "0.0.0.0:5001"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
```

3. 啟動應用:
```bash
gunicorn -c gunicorn.conf.py run_integrated:app
```

### 使用 Nginx 反向代理 (可選)

1. 安裝 Nginx
2. 建立 Nginx 配置檔案 `/etc/nginx/sites-available/tariffed`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /path/to/tariffed-backend/src/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

3. 啟用站點:
```bash
sudo ln -s /etc/nginx/sites-available/tariffed /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Docker 部署

### 建立 Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 複製後端檔案
COPY tariffed-backend/ .

# 安裝 Python 依賴
RUN pip install --no-cache-dir -r requirements.txt

# 建立資料庫目錄
RUN mkdir -p src/database

# 初始化範例資料
RUN python src/data/sample_data.py

# 暴露端口
EXPOSE 5001

# 啟動應用
CMD ["python", "run_integrated.py"]
```

### 建立 docker-compose.yml
```yaml
version: '3.8'

services:
  tariffed:
    build: .
    ports:
      - "5001:5001"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_API_BASE=${OPENAI_API_BASE}
    volumes:
      - ./data:/app/src/database
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - tariffed
    restart: unless-stopped
```

### 部署命令
```bash
# 建置和啟動
docker-compose up -d

# 查看日誌
docker-compose logs -f

# 停止服務
docker-compose down
```

## 資料庫遷移 (PostgreSQL)

如果需要遷移到 PostgreSQL：

1. 安裝 PostgreSQL 和相關套件:
```bash
pip install psycopg2-binary
```

2. 修改 `run_integrated.py` 中的資料庫配置:
```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'postgresql://username:password@localhost/tariffed'
```

3. 建立 PostgreSQL 資料庫:
```sql
CREATE DATABASE tariffed;
CREATE USER tariffed_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE tariffed TO tariffed_user;
```

## 監控和日誌

### 設定日誌
在 `run_integrated.py` 中添加日誌配置:
```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/tariffed.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('TARIFFED startup')
```

### 健康檢查端點
添加健康檢查 API:
```python
@app.route('/health')
def health_check():
    return {'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()}
```

## 安全性考量

### 1. 環境變數保護
- 使用 `.env` 檔案管理敏感資訊
- 不要將 API 金鑰提交到版本控制

### 2. HTTPS 設定
- 在生產環境中使用 HTTPS
- 可以使用 Let's Encrypt 獲取免費 SSL 證書

### 3. 防火牆設定
- 只開放必要的端口 (80, 443)
- 限制資料庫訪問

### 4. 定期更新
- 定期更新依賴套件
- 監控安全漏洞

## 故障排除

### 常見問題

1. **OpenAI API 錯誤**
   - 檢查 API 金鑰是否正確設定
   - 確認 API 配額是否足夠

2. **資料庫連接錯誤**
   - 檢查資料庫檔案權限
   - 確認資料庫路徑正確

3. **前端資源載入失敗**
   - 確認靜態檔案已正確複製
   - 檢查檔案權限

4. **端口衝突**
   - 使用 `netstat -tulpn | grep :5001` 檢查端口使用
   - 修改配置使用不同端口

### 日誌檢查
```bash
# 檢查應用日誌
tail -f logs/tariffed.log

# 檢查系統日誌
sudo journalctl -u tariffed -f
```

## 效能優化

### 1. 快取設定
- 使用 Redis 快取 API 回應
- 設定靜態檔案快取

### 2. 資料庫優化
- 添加適當的索引
- 定期清理舊資料

### 3. 負載平衡
- 使用多個 Gunicorn worker
- 考慮使用負載平衡器

這個部署指南提供了從開發到生產環境的完整部署流程，確保 TARIFFED 系統能夠穩定運行。

