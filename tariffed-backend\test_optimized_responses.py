#!/usr/bin/env python3
"""
測試優化後的 AI 回應系統
"""

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

def test_optimized_responses():
    """測試優化後的 AI 回應"""
    print("🚀 測試優化後的 AI 回應系統...")
    
    try:
        # 測試專業提示詞
        print("\n📝 測試專業提示詞...")
        from src.prompts.professional_prompts import ProfessionalPrompts, ResponseTemplates, QualityEnhancers
        
        # 測試各代理的提示詞
        query_prompt = ProfessionalPrompts.get_query_agent_prompt()
        analysis_prompt = ProfessionalPrompts.get_analysis_agent_prompt()
        recommendation_prompt = ProfessionalPrompts.get_recommendation_agent_prompt()
        
        print("✅ 查詢代理提示詞長度:", len(query_prompt))
        print("✅ 分析代理提示詞長度:", len(analysis_prompt))
        print("✅ 建議代理提示詞長度:", len(recommendation_prompt))
        
        # 測試回應模板
        print("\n🎨 測試回應模板...")
        hts_template = ResponseTemplates.hts_classification_template()
        trend_template = ResponseTemplates.trend_analysis_template()
        recommendation_template = ResponseTemplates.recommendation_template()
        
        print("✅ HTS 分類模板可用")
        print("✅ 趨勢分析模板可用")
        print("✅ 建議模板可用")
        
        # 測試品質增強器
        print("\n⭐ 測試品質增強器...")
        sample_response = "這是一個測試回應。"
        
        enhanced_response = QualityEnhancers.add_professional_context(sample_response, "hts_classification")
        print("✅ 專業背景增強器可用")
        
        enhanced_response = QualityEnhancers.add_compliance_reminder(sample_response)
        print("✅ 合規提醒增強器可用")
        
        enhanced_response = QualityEnhancers.add_data_disclaimer(sample_response)
        print("✅ 數據聲明增強器可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 專業提示詞測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_responses():
    """測試代理回應"""
    print("\n🤖 測試優化後的代理回應...")
    
    try:
        from src.agents.query_agent import QueryAgent
        from src.agents.analysis_agent import AnalysisAgent
        from src.agents.recommendation_agent import RecommendationAgent
        
        # 測試查詢代理
        print("\n🔍 測試查詢代理優化回應...")
        query_agent = QueryAgent()
        
        # 測試 HTS 查詢
        query_result = query_agent.process_query("什麼是 HTS 0101.21.00？")
        if query_result.get('success', False):
            print("✅ 查詢代理回應優化成功")
            print(f"   回應長度: {len(query_result.get('response', ''))}")
            print(f"   包含專業術語: {'HTS' in query_result.get('response', '')}")
            print(f"   包含合規提醒: {'合規' in query_result.get('response', '')}")
        else:
            print(f"❌ 查詢代理測試失敗: {query_result.get('error', 'Unknown')}")
        
        # 測試分析代理
        print("\n📊 測試分析代理優化回應...")
        analysis_agent = AnalysisAgent()
        
        analysis_result = analysis_agent.process_query("分析中國進口電子產品的趨勢")
        if analysis_result.get('success', False):
            print("✅ 分析代理回應優化成功")
            print(f"   回應長度: {len(analysis_result.get('response', ''))}")
            print(f"   包含統計數據: {'%' in analysis_result.get('response', '')}")
            print(f"   包含圖表建議: {'圖表' in analysis_result.get('response', '')}")
        else:
            print(f"❌ 分析代理測試失敗: {analysis_result.get('error', 'Unknown')}")
        
        # 測試建議代理
        print("\n💡 測試建議代理優化回應...")
        recommendation_agent = RecommendationAgent()
        
        recommendation_result = recommendation_agent.process_query("建議降低進口成本的方法")
        if recommendation_result.get('success', False):
            print("✅ 建議代理回應優化成功")
            print(f"   回應長度: {len(recommendation_result.get('response', ''))}")
            print(f"   包含具體建議: {'策略' in recommendation_result.get('response', '')}")
            print(f"   包含實施計劃: {'實施' in recommendation_result.get('response', '')}")
        else:
            print(f"❌ 建議代理測試失敗: {recommendation_result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 代理回應測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_response_quality():
    """測試回應品質"""
    print("\n⭐ 測試回應品質指標...")
    
    try:
        from src.services.multi_agent_service import multi_agent_service
        
        # 測試查詢
        test_queries = [
            {
                'query': '查找馬匹的 HTS 條目',
                'expected_agent': 'query_agent',
                'quality_indicators': ['HTS', '稅率', '合規', '專業']
            },
            {
                'query': '分析進口趨勢數據',
                'expected_agent': 'analysis_agent', 
                'quality_indicators': ['趨勢', '統計', '分析', '圖表']
            },
            {
                'query': '建議成本優化方案',
                'expected_agent': 'recommendation_agent',
                'quality_indicators': ['策略', '實施', '風險', '收益']
            }
        ]
        
        quality_scores = []
        
        for test_case in test_queries:
            print(f"\n測試查詢: {test_case['query']}")
            
            result = multi_agent_service.process_query(test_case['query'])
            
            if result.get('success', False):
                response = result.get('response', '')
                agent_used = result.get('agent_used', '')
                
                # 檢查代理路由
                correct_agent = agent_used == test_case['expected_agent']
                print(f"   代理路由: {'✅' if correct_agent else '❌'} ({agent_used})")
                
                # 檢查品質指標
                quality_indicators_found = 0
                for indicator in test_case['quality_indicators']:
                    if indicator in response:
                        quality_indicators_found += 1
                
                quality_score = quality_indicators_found / len(test_case['quality_indicators'])
                quality_scores.append(quality_score)
                
                print(f"   品質指標: {quality_indicators_found}/{len(test_case['quality_indicators'])} ({quality_score:.1%})")
                print(f"   回應長度: {len(response)} 字符")
                
                # 檢查專業性
                professional_terms = ['建議', '分析', '評估', '策略', '風險', '合規']
                professional_count = sum(1 for term in professional_terms if term in response)
                print(f"   專業術語: {professional_count} 個")
                
            else:
                print(f"   ❌ 查詢失敗: {result.get('error', 'Unknown')}")
                quality_scores.append(0.0)
        
        # 計算總體品質分數
        overall_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        print(f"\n📊 總體品質分數: {overall_quality:.1%}")
        
        if overall_quality >= 0.8:
            print("🎉 回應品質優秀！")
        elif overall_quality >= 0.6:
            print("👍 回應品質良好")
        else:
            print("⚠️ 回應品質需要改進")
        
        return overall_quality >= 0.6
        
    except Exception as e:
        print(f"❌ 品質測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🌟 TARIFFED AI 回應優化測試")
    print("=" * 50)
    
    # 測試專業提示詞
    prompts_success = test_optimized_responses()
    
    # 測試代理回應
    agents_success = test_agent_responses()
    
    # 測試回應品質
    quality_success = test_response_quality()
    
    print("\n" + "=" * 50)
    print("📋 測試結果總結:")
    print(f"   專業提示詞: {'✅ 通過' if prompts_success else '❌ 失敗'}")
    print(f"   代理回應: {'✅ 通過' if agents_success else '❌ 失敗'}")
    print(f"   回應品質: {'✅ 通過' if quality_success else '❌ 失敗'}")
    
    if prompts_success and agents_success and quality_success:
        print("\n🎊 所有測試通過！AI 回應已成功優化。")
        print("\n🚀 優化效果:")
        print("   • 更專業的術語和表達")
        print("   • 結構化的回應格式")
        print("   • 詳細的分析和建議")
        print("   • 完整的合規提醒")
        print("   • 可操作的實施指導")
        return True
    else:
        print("\n⚠️ 部分測試失敗，請檢查錯誤信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
