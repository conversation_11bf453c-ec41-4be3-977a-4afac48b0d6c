#!/usr/bin/env python3
"""
基礎代理類 - 所有專門代理的父類
"""

import os
import json
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BaseAgent(ABC):
    """基礎代理抽象類"""
    
    def __init__(self, name: str, description: str, model: str = "gpt-3.5-turbo"):
        self.name = name
        self.description = description
        self.model = model
        self.conversation_history = []
        self.tools = []
        self.created_at = datetime.now()
        
        # 從環境變量獲取 API 密鑰
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            logger.warning(f"⚠️ {self.name}: OpenAI API 密鑰未設置，將使用模擬模式")
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """獲取系統提示詞 - 每個代理必須實現"""
        pass
    
    @abstractmethod
    def process_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理查詢 - 每個代理必須實現"""
        pass
    
    def add_tool(self, tool_name: str, tool_function):
        """添加工具到代理"""
        self.tools.append({
            'name': tool_name,
            'function': tool_function
        })
        logger.info(f"🔧 {self.name}: 添加工具 {tool_name}")
    
    def log_interaction(self, query: str, response: str, metadata: Dict = None):
        """記錄交互歷史"""
        interaction = {
            'timestamp': datetime.now().isoformat(),
            'query': query,
            'response': response,
            'metadata': metadata or {}
        }
        self.conversation_history.append(interaction)
        
        # 保持歷史記錄在合理範圍內
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-50:]
    
    def get_agent_info(self) -> Dict[str, Any]:
        """獲取代理信息"""
        return {
            'name': self.name,
            'description': self.description,
            'model': self.model,
            'tools_count': len(self.tools),
            'interactions_count': len(self.conversation_history),
            'created_at': self.created_at.isoformat(),
            'status': 'active' if self.api_key else 'mock_mode'
        }
    
    def simulate_ai_response(self, query: str, context: Dict[str, Any] = None) -> str:
        """模擬 AI 回應（當沒有 API 密鑰時使用）"""
        return f"[模擬回應] {self.name} 正在處理您的查詢：「{query}」。這是一個模擬回應，實際部署時會使用真實的 AI 模型。"

class AgentCoordinator:
    """代理協調器 - 管理多個代理的協作"""
    
    def __init__(self):
        self.agents = {}
        self.interaction_log = []
        
    def register_agent(self, agent: BaseAgent):
        """註冊代理"""
        self.agents[agent.name] = agent
        logger.info(f"📝 註冊代理: {agent.name}")
    
    def get_agent(self, agent_name: str) -> Optional[BaseAgent]:
        """獲取指定代理"""
        return self.agents.get(agent_name)
    
    def list_agents(self) -> List[Dict[str, Any]]:
        """列出所有代理"""
        return [agent.get_agent_info() for agent in self.agents.values()]
    
    def route_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """智能路由查詢到合適的代理"""
        query_lower = query.lower()
        
        # 簡單的路由邏輯
        if any(keyword in query_lower for keyword in ['搜尋', '查找', '什麼是', 'hts', '條目']):
            agent_name = 'query_agent'
        elif any(keyword in query_lower for keyword in ['分析', '比較', '趨勢', '統計', '數據']):
            agent_name = 'analysis_agent'
        elif any(keyword in query_lower for keyword in ['建議', '推薦', '替代', '選擇', '應該']):
            agent_name = 'recommendation_agent'
        else:
            # 默認使用查詢代理
            agent_name = 'query_agent'
        
        agent = self.get_agent(agent_name)
        if not agent:
            return {
                'success': False,
                'error': f'代理 {agent_name} 不存在',
                'available_agents': list(self.agents.keys())
            }
        
        # 執行查詢
        try:
            result = agent.process_query(query, context)
            
            # 記錄交互
            self.interaction_log.append({
                'timestamp': datetime.now().isoformat(),
                'query': query,
                'agent_used': agent_name,
                'success': True,
                'context': context
            })
            
            return {
                'success': True,
                'agent_used': agent_name,
                'result': result
            }
            
        except Exception as e:
            logger.error(f"❌ 代理 {agent_name} 處理查詢時出錯: {e}")
            
            self.interaction_log.append({
                'timestamp': datetime.now().isoformat(),
                'query': query,
                'agent_used': agent_name,
                'success': False,
                'error': str(e),
                'context': context
            })
            
            return {
                'success': False,
                'error': f'代理處理查詢時出錯: {e}',
                'agent_used': agent_name
            }
    
    def collaborative_query(self, query: str, agents: List[str], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """多代理協作查詢"""
        results = {}
        
        for agent_name in agents:
            agent = self.get_agent(agent_name)
            if agent:
                try:
                    result = agent.process_query(query, context)
                    results[agent_name] = {
                        'success': True,
                        'result': result
                    }
                except Exception as e:
                    results[agent_name] = {
                        'success': False,
                        'error': str(e)
                    }
            else:
                results[agent_name] = {
                    'success': False,
                    'error': f'代理 {agent_name} 不存在'
                }
        
        return {
            'query': query,
            'collaborative_results': results,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """獲取系統狀態"""
        return {
            'total_agents': len(self.agents),
            'agents': self.list_agents(),
            'total_interactions': len(self.interaction_log),
            'recent_interactions': self.interaction_log[-10:] if self.interaction_log else [],
            'system_health': 'healthy' if self.agents else 'no_agents'
        }

# 全局代理協調器實例
agent_coordinator = AgentCoordinator()
