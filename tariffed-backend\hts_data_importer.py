#!/usr/bin/env python3
"""
HTS 資料導入工具
從 USITC 官方來源導入真實的 HTS 資料
"""

import os
import sys
import csv
import json
import requests
import sqlite3
from datetime import datetime
from typing import List, Dict, Optional

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

from src.models.hts import db, HTSSection, HTSChapter, HTSHeading, HTSSubheading, Country, TradeData

class HTSDataImporter:
    def __init__(self, db_path: str = None):
        """初始化 HTS 資料導入器"""
        self.db_path = db_path or os.path.join(os.path.dirname(__file__), 'src', 'database', 'app.db')
        self.base_url = "https://hts.usitc.gov/reststop"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'TARIFFED-HTS-Importer/1.0',
            'Accept': 'application/json'
        })
    
    def fetch_hts_data_from_api(self, chapter_start: str = "01", chapter_end: str = "99") -> List[Dict]:
        """從 USITC API 獲取 HTS 資料"""
        try:
            # 嘗試使用 USITC REST API
            url = f"{self.base_url}/exportList"
            params = {
                'from': f"{chapter_start}00",
                'to': f"{chapter_end}99",
                'format': 'json'
            }
            
            print(f"正在從 USITC API 獲取 HTS 資料...")
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功獲取 {len(data)} 條 HTS 記錄")
                return data
            else:
                print(f"❌ API 請求失敗: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ API 請求錯誤: {e}")
            return []
    
    def load_sample_hts_data(self) -> List[Dict]:
        """載入範例 HTS 資料（當 API 不可用時使用）"""
        print("📦 載入範例 HTS 資料...")
        
        sample_data = [
            # Section I: Live Animals; Animal Products
            {
                "section_id": 1,
                "section_title": "Live Animals; Animal Products",
                "chapter_id": 1,
                "chapter_title": "Live Animals",
                "heading_id": "0101",
                "heading_description": "Live horses, asses, mules and hinnies",
                "subheading_id": "0101.21.00",
                "description": "Horses: Pure bred breeding animals",
                "unit_of_quantity": "No.",
                "general_rate": "Free",
                "special_rate": "Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
                "column_2_rate": "Free"
            },
            {
                "section_id": 1,
                "section_title": "Live Animals; Animal Products",
                "chapter_id": 1,
                "chapter_title": "Live Animals",
                "heading_id": "0101",
                "heading_description": "Live horses, asses, mules and hinnies",
                "subheading_id": "0101.29.00",
                "description": "Horses: Other",
                "unit_of_quantity": "No.",
                "general_rate": "Free",
                "special_rate": "Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
                "column_2_rate": "Free"
            },
            {
                "section_id": 1,
                "section_title": "Live Animals; Animal Products",
                "chapter_id": 2,
                "chapter_title": "Meat and Edible Meat Offal",
                "heading_id": "0201",
                "heading_description": "Meat of bovine animals, fresh or chilled",
                "subheading_id": "0201.10.05",
                "description": "Carcasses and half-carcasses: High quality beef cuts",
                "unit_of_quantity": "kg",
                "general_rate": "4.4¢/kg",
                "special_rate": "Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
                "column_2_rate": "30.8¢/kg"
            },
            # Section II: Vegetable Products
            {
                "section_id": 2,
                "section_title": "Vegetable Products",
                "chapter_id": 7,
                "chapter_title": "Edible Vegetables and Certain Roots and Tubers",
                "heading_id": "0701",
                "heading_description": "Potatoes, fresh or chilled",
                "subheading_id": "0701.10.00",
                "description": "Seed",
                "unit_of_quantity": "kg",
                "general_rate": "0.5¢/kg",
                "special_rate": "Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
                "column_2_rate": "2.8¢/kg"
            },
            {
                "section_id": 2,
                "section_title": "Vegetable Products",
                "chapter_id": 8,
                "chapter_title": "Edible Fruit and Nuts; Peel of Citrus Fruit or Melons",
                "heading_id": "0803",
                "heading_description": "Bananas, including plantains, fresh or dried",
                "subheading_id": "0803.10.00",
                "description": "Plantains",
                "unit_of_quantity": "kg",
                "general_rate": "Free",
                "special_rate": "Free",
                "column_2_rate": "Free"
            },
            # Section XVI: Machinery and Mechanical Appliances
            {
                "section_id": 16,
                "section_title": "Machinery and Mechanical Appliances; Electrical Equipment",
                "chapter_id": 84,
                "chapter_title": "Nuclear Reactors, Boilers, Machinery and Mechanical Appliances",
                "heading_id": "8471",
                "heading_description": "Automatic data processing machines and units thereof",
                "subheading_id": "8471.30.01",
                "description": "Portable automatic data processing machines, weighing not more than 10 kg",
                "unit_of_quantity": "No.",
                "general_rate": "Free",
                "special_rate": "Free",
                "column_2_rate": "35%"
            },
            # Section XVII: Vehicles, Aircraft, Vessels
            {
                "section_id": 17,
                "section_title": "Vehicles, Aircraft, Vessels and Associated Transport Equipment",
                "chapter_id": 87,
                "chapter_title": "Vehicles Other than Railway or Tramway Rolling Stock",
                "heading_id": "8703",
                "heading_description": "Motor cars and other motor vehicles principally designed for the transport of persons",
                "subheading_id": "8703.21.00",
                "description": "Other vehicles, with spark-ignition internal combustion reciprocating piston engine: Of a cylinder capacity not exceeding 1,000 cc",
                "unit_of_quantity": "No.",
                "general_rate": "2.5%",
                "special_rate": "Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
                "column_2_rate": "10%"
            }
        ]
        
        print(f"✅ 載入了 {len(sample_data)} 條範例 HTS 記錄")
        return sample_data
    
    def load_country_data(self) -> List[Dict]:
        """載入國家資料"""
        print("🌍 載入國家資料...")
        
        countries = [
            {"id": "AD", "name": "Andorra"},
            {"id": "AE", "name": "United Arab Emirates"},
            {"id": "AF", "name": "Afghanistan"},
            {"id": "AG", "name": "Antigua and Barbuda"},
            {"id": "AI", "name": "Anguilla"},
            {"id": "AL", "name": "Albania"},
            {"id": "AM", "name": "Armenia"},
            {"id": "AO", "name": "Angola"},
            {"id": "AQ", "name": "Antarctica"},
            {"id": "AR", "name": "Argentina"},
            {"id": "AS", "name": "American Samoa"},
            {"id": "AT", "name": "Austria"},
            {"id": "AU", "name": "Australia"},
            {"id": "AW", "name": "Aruba"},
            {"id": "AX", "name": "Aland Islands"},
            {"id": "AZ", "name": "Azerbaijan"},
            {"id": "BA", "name": "Bosnia and Herzegovina"},
            {"id": "BB", "name": "Barbados"},
            {"id": "BD", "name": "Bangladesh"},
            {"id": "BE", "name": "Belgium"},
            {"id": "BF", "name": "Burkina Faso"},
            {"id": "BG", "name": "Bulgaria"},
            {"id": "BH", "name": "Bahrain"},
            {"id": "BI", "name": "Burundi"},
            {"id": "BJ", "name": "Benin"},
            {"id": "BL", "name": "Saint Barthelemy"},
            {"id": "BM", "name": "Bermuda"},
            {"id": "BN", "name": "Brunei Darussalam"},
            {"id": "BO", "name": "Bolivia"},
            {"id": "BQ", "name": "Bonaire, Sint Eustatius and Saba"},
            {"id": "BR", "name": "Brazil"},
            {"id": "BS", "name": "Bahamas"},
            {"id": "BT", "name": "Bhutan"},
            {"id": "BV", "name": "Bouvet Island"},
            {"id": "BW", "name": "Botswana"},
            {"id": "BY", "name": "Belarus"},
            {"id": "BZ", "name": "Belize"},
            {"id": "CA", "name": "Canada"},
            {"id": "CC", "name": "Cocos (Keeling) Islands"},
            {"id": "CD", "name": "Congo, Democratic Republic of the"},
            {"id": "CF", "name": "Central African Republic"},
            {"id": "CG", "name": "Congo"},
            {"id": "CH", "name": "Switzerland"},
            {"id": "CI", "name": "Cote d'Ivoire"},
            {"id": "CK", "name": "Cook Islands"},
            {"id": "CL", "name": "Chile"},
            {"id": "CM", "name": "Cameroon"},
            {"id": "CN", "name": "China"},
            {"id": "CO", "name": "Colombia"},
            {"id": "CR", "name": "Costa Rica"},
            {"id": "CU", "name": "Cuba"},
            {"id": "CV", "name": "Cape Verde"},
            {"id": "CW", "name": "Curacao"},
            {"id": "CX", "name": "Christmas Island"},
            {"id": "CY", "name": "Cyprus"},
            {"id": "CZ", "name": "Czech Republic"},
            {"id": "DE", "name": "Germany"},
            {"id": "DJ", "name": "Djibouti"},
            {"id": "DK", "name": "Denmark"},
            {"id": "DM", "name": "Dominica"},
            {"id": "DO", "name": "Dominican Republic"},
            {"id": "DZ", "name": "Algeria"},
            {"id": "EC", "name": "Ecuador"},
            {"id": "EE", "name": "Estonia"},
            {"id": "EG", "name": "Egypt"},
            {"id": "EH", "name": "Western Sahara"},
            {"id": "ER", "name": "Eritrea"},
            {"id": "ES", "name": "Spain"},
            {"id": "ET", "name": "Ethiopia"},
            {"id": "FI", "name": "Finland"},
            {"id": "FJ", "name": "Fiji"},
            {"id": "FK", "name": "Falkland Islands (Malvinas)"},
            {"id": "FM", "name": "Micronesia, Federated States of"},
            {"id": "FO", "name": "Faroe Islands"},
            {"id": "FR", "name": "France"},
            {"id": "GA", "name": "Gabon"},
            {"id": "GB", "name": "United Kingdom"},
            {"id": "GD", "name": "Grenada"},
            {"id": "GE", "name": "Georgia"},
            {"id": "GF", "name": "French Guiana"},
            {"id": "GG", "name": "Guernsey"},
            {"id": "GH", "name": "Ghana"},
            {"id": "GI", "name": "Gibraltar"},
            {"id": "GL", "name": "Greenland"},
            {"id": "GM", "name": "Gambia"},
            {"id": "GN", "name": "Guinea"},
            {"id": "GP", "name": "Guadeloupe"},
            {"id": "GQ", "name": "Equatorial Guinea"},
            {"id": "GR", "name": "Greece"},
            {"id": "GS", "name": "South Georgia and the South Sandwich Islands"},
            {"id": "GT", "name": "Guatemala"},
            {"id": "GU", "name": "Guam"},
            {"id": "GW", "name": "Guinea-Bissau"},
            {"id": "GY", "name": "Guyana"},
            {"id": "HK", "name": "Hong Kong"},
            {"id": "HM", "name": "Heard Island and McDonald Islands"},
            {"id": "HN", "name": "Honduras"},
            {"id": "HR", "name": "Croatia"},
            {"id": "HT", "name": "Haiti"},
            {"id": "HU", "name": "Hungary"},
            {"id": "ID", "name": "Indonesia"},
            {"id": "IE", "name": "Ireland"},
            {"id": "IL", "name": "Israel"},
            {"id": "IM", "name": "Isle of Man"},
            {"id": "IN", "name": "India"},
            {"id": "IO", "name": "British Indian Ocean Territory"},
            {"id": "IQ", "name": "Iraq"},
            {"id": "IR", "name": "Iran, Islamic Republic of"},
            {"id": "IS", "name": "Iceland"},
            {"id": "IT", "name": "Italy"},
            {"id": "JE", "name": "Jersey"},
            {"id": "JM", "name": "Jamaica"},
            {"id": "JO", "name": "Jordan"},
            {"id": "JP", "name": "Japan"},
            {"id": "KE", "name": "Kenya"},
            {"id": "KG", "name": "Kyrgyzstan"},
            {"id": "KH", "name": "Cambodia"},
            {"id": "KI", "name": "Kiribati"},
            {"id": "KM", "name": "Comoros"},
            {"id": "KN", "name": "Saint Kitts and Nevis"},
            {"id": "KP", "name": "Korea, Democratic People's Republic of"},
            {"id": "KR", "name": "Korea, Republic of"},
            {"id": "KW", "name": "Kuwait"},
            {"id": "KY", "name": "Cayman Islands"},
            {"id": "KZ", "name": "Kazakhstan"},
            {"id": "LA", "name": "Lao People's Democratic Republic"},
            {"id": "LB", "name": "Lebanon"},
            {"id": "LC", "name": "Saint Lucia"},
            {"id": "LI", "name": "Liechtenstein"},
            {"id": "LK", "name": "Sri Lanka"},
            {"id": "LR", "name": "Liberia"},
            {"id": "LS", "name": "Lesotho"},
            {"id": "LT", "name": "Lithuania"},
            {"id": "LU", "name": "Luxembourg"},
            {"id": "LV", "name": "Latvia"},
            {"id": "LY", "name": "Libya"},
            {"id": "MA", "name": "Morocco"},
            {"id": "MC", "name": "Monaco"},
            {"id": "MD", "name": "Moldova, Republic of"},
            {"id": "ME", "name": "Montenegro"},
            {"id": "MF", "name": "Saint Martin (French part)"},
            {"id": "MG", "name": "Madagascar"},
            {"id": "MH", "name": "Marshall Islands"},
            {"id": "MK", "name": "Macedonia, the former Yugoslav Republic of"},
            {"id": "ML", "name": "Mali"},
            {"id": "MM", "name": "Myanmar"},
            {"id": "MN", "name": "Mongolia"},
            {"id": "MO", "name": "Macao"},
            {"id": "MP", "name": "Northern Mariana Islands"},
            {"id": "MQ", "name": "Martinique"},
            {"id": "MR", "name": "Mauritania"},
            {"id": "MS", "name": "Montserrat"},
            {"id": "MT", "name": "Malta"},
            {"id": "MU", "name": "Mauritius"},
            {"id": "MV", "name": "Maldives"},
            {"id": "MW", "name": "Malawi"},
            {"id": "MX", "name": "Mexico"},
            {"id": "MY", "name": "Malaysia"},
            {"id": "MZ", "name": "Mozambique"},
            {"id": "NA", "name": "Namibia"},
            {"id": "NC", "name": "New Caledonia"},
            {"id": "NE", "name": "Niger"},
            {"id": "NF", "name": "Norfolk Island"},
            {"id": "NG", "name": "Nigeria"},
            {"id": "NI", "name": "Nicaragua"},
            {"id": "NL", "name": "Netherlands"},
            {"id": "NO", "name": "Norway"},
            {"id": "NP", "name": "Nepal"},
            {"id": "NR", "name": "Nauru"},
            {"id": "NU", "name": "Niue"},
            {"id": "NZ", "name": "New Zealand"},
            {"id": "OM", "name": "Oman"},
            {"id": "PA", "name": "Panama"},
            {"id": "PE", "name": "Peru"},
            {"id": "PF", "name": "French Polynesia"},
            {"id": "PG", "name": "Papua New Guinea"},
            {"id": "PH", "name": "Philippines"},
            {"id": "PK", "name": "Pakistan"},
            {"id": "PL", "name": "Poland"},
            {"id": "PM", "name": "Saint Pierre and Miquelon"},
            {"id": "PN", "name": "Pitcairn"},
            {"id": "PR", "name": "Puerto Rico"},
            {"id": "PS", "name": "Palestinian Territory, Occupied"},
            {"id": "PT", "name": "Portugal"},
            {"id": "PW", "name": "Palau"},
            {"id": "PY", "name": "Paraguay"},
            {"id": "QA", "name": "Qatar"},
            {"id": "RE", "name": "Reunion"},
            {"id": "RO", "name": "Romania"},
            {"id": "RS", "name": "Serbia"},
            {"id": "RU", "name": "Russian Federation"},
            {"id": "RW", "name": "Rwanda"},
            {"id": "SA", "name": "Saudi Arabia"},
            {"id": "SB", "name": "Solomon Islands"},
            {"id": "SC", "name": "Seychelles"},
            {"id": "SD", "name": "Sudan"},
            {"id": "SE", "name": "Sweden"},
            {"id": "SG", "name": "Singapore"},
            {"id": "SH", "name": "Saint Helena, Ascension and Tristan da Cunha"},
            {"id": "SI", "name": "Slovenia"},
            {"id": "SJ", "name": "Svalbard and Jan Mayen"},
            {"id": "SK", "name": "Slovakia"},
            {"id": "SL", "name": "Sierra Leone"},
            {"id": "SM", "name": "San Marino"},
            {"id": "SN", "name": "Senegal"},
            {"id": "SO", "name": "Somalia"},
            {"id": "SR", "name": "Suriname"},
            {"id": "SS", "name": "South Sudan"},
            {"id": "ST", "name": "Sao Tome and Principe"},
            {"id": "SV", "name": "El Salvador"},
            {"id": "SX", "name": "Sint Maarten (Dutch part)"},
            {"id": "SY", "name": "Syrian Arab Republic"},
            {"id": "SZ", "name": "Swaziland"},
            {"id": "TC", "name": "Turks and Caicos Islands"},
            {"id": "TD", "name": "Chad"},
            {"id": "TF", "name": "French Southern Territories"},
            {"id": "TG", "name": "Togo"},
            {"id": "TH", "name": "Thailand"},
            {"id": "TJ", "name": "Tajikistan"},
            {"id": "TK", "name": "Tokelau"},
            {"id": "TL", "name": "Timor-Leste"},
            {"id": "TM", "name": "Turkmenistan"},
            {"id": "TN", "name": "Tunisia"},
            {"id": "TO", "name": "Tonga"},
            {"id": "TR", "name": "Turkey"},
            {"id": "TT", "name": "Trinidad and Tobago"},
            {"id": "TV", "name": "Tuvalu"},
            {"id": "TW", "name": "Taiwan, Province of China"},
            {"id": "TZ", "name": "Tanzania, United Republic of"},
            {"id": "UA", "name": "Ukraine"},
            {"id": "UG", "name": "Uganda"},
            {"id": "UM", "name": "United States Minor Outlying Islands"},
            {"id": "US", "name": "United States"},
            {"id": "UY", "name": "Uruguay"},
            {"id": "UZ", "name": "Uzbekistan"},
            {"id": "VA", "name": "Holy See (Vatican City State)"},
            {"id": "VC", "name": "Saint Vincent and the Grenadines"},
            {"id": "VE", "name": "Venezuela, Bolivarian Republic of"},
            {"id": "VG", "name": "Virgin Islands, British"},
            {"id": "VI", "name": "Virgin Islands, U.S."},
            {"id": "VN", "name": "Viet Nam"},
            {"id": "VU", "name": "Vanuatu"},
            {"id": "WF", "name": "Wallis and Futuna"},
            {"id": "WS", "name": "Samoa"},
            {"id": "YE", "name": "Yemen"},
            {"id": "YT", "name": "Mayotte"},
            {"id": "ZA", "name": "South Africa"},
            {"id": "ZM", "name": "Zambia"},
            {"id": "ZW", "name": "Zimbabwe"}
        ]
        
        print(f"✅ 載入了 {len(countries)} 個國家")
        return countries

    def import_to_database(self, hts_data: List[Dict], countries: List[Dict]) -> bool:
        """將資料導入到資料庫"""
        try:
            from flask import Flask

            # 創建 Flask 應用上下文
            app = Flask(__name__)
            app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{self.db_path}'
            app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

            db.init_app(app)

            with app.app_context():
                # 創建所有表
                db.create_all()

                print("🗄️ 開始導入資料到資料庫...")

                # 導入國家資料
                print("📍 導入國家資料...")
                for country_data in countries:
                    existing_country = Country.query.filter_by(id=country_data['id']).first()
                    if not existing_country:
                        country = Country(
                            id=country_data['id'],
                            name=country_data['name']
                        )
                        db.session.add(country)

                # 導入 HTS 資料
                print("📋 導入 HTS 資料...")
                sections_added = set()
                chapters_added = set()
                headings_added = set()

                for item in hts_data:
                    # 導入 Section
                    section_id = item['section_id']
                    if section_id not in sections_added:
                        existing_section = HTSSection.query.filter_by(id=section_id).first()
                        if not existing_section:
                            section = HTSSection(
                                id=section_id,
                                title=item['section_title']
                            )
                            db.session.add(section)
                        sections_added.add(section_id)

                    # 導入 Chapter
                    chapter_id = item['chapter_id']
                    if chapter_id not in chapters_added:
                        existing_chapter = HTSChapter.query.filter_by(id=chapter_id).first()
                        if not existing_chapter:
                            chapter = HTSChapter(
                                id=chapter_id,
                                title=item['chapter_title'],
                                section_id=section_id
                            )
                            db.session.add(chapter)
                        chapters_added.add(chapter_id)

                    # 導入 Heading
                    heading_id = item['heading_id']
                    if heading_id not in headings_added:
                        existing_heading = HTSHeading.query.filter_by(id=heading_id).first()
                        if not existing_heading:
                            heading = HTSHeading(
                                id=heading_id,
                                description=item['heading_description'],
                                chapter_id=chapter_id
                            )
                            db.session.add(heading)
                        headings_added.add(heading_id)

                    # 導入 Subheading
                    existing_subheading = HTSSubheading.query.filter_by(id=item['subheading_id']).first()
                    if not existing_subheading:
                        subheading = HTSSubheading(
                            id=item['subheading_id'],
                            description=item['description'],
                            unit_of_quantity=item['unit_of_quantity'],
                            general_rate=item['general_rate'],
                            special_rate=item['special_rate'],
                            column_2_rate=item['column_2_rate'],
                            heading_id=heading_id
                        )
                        db.session.add(subheading)

                # 提交所有更改
                db.session.commit()

                # 統計導入結果
                section_count = HTSSection.query.count()
                chapter_count = HTSChapter.query.count()
                heading_count = HTSHeading.query.count()
                subheading_count = HTSSubheading.query.count()
                country_count = Country.query.count()

                print(f"✅ 資料導入完成！")
                print(f"   - Sections: {section_count}")
                print(f"   - Chapters: {chapter_count}")
                print(f"   - Headings: {heading_count}")
                print(f"   - Subheadings: {subheading_count}")
                print(f"   - Countries: {country_count}")

                return True

        except Exception as e:
            print(f"❌ 資料庫導入錯誤: {e}")
            return False

    def generate_sample_trade_data(self) -> bool:
        """生成範例貿易資料"""
        try:
            from flask import Flask
            import random

            app = Flask(__name__)
            app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{self.db_path}'
            app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

            db.init_app(app)

            with app.app_context():
                print("📊 生成範例貿易資料...")

                # 獲取一些 HTS 條目和國家
                subheadings = HTSSubheading.query.limit(10).all()
                countries = Country.query.filter(Country.id.in_(['CN', 'CA', 'MX', 'JP', 'DE', 'GB', 'FR', 'IT', 'BR', 'KR'])).all()

                if not subheadings or not countries:
                    print("❌ 沒有找到 HTS 條目或國家資料，請先導入基礎資料")
                    return False

                # 生成 2022-2024 年的貿易資料
                for year in [2022, 2023, 2024]:
                    for subheading in subheadings:
                        for country in countries:
                            # 檢查是否已存在
                            existing = TradeData.query.filter_by(
                                hts_subheading_id=subheading.id,
                                country_id=country.id,
                                year=year
                            ).first()

                            if not existing:
                                # 生成隨機貿易資料
                                import_value = random.uniform(1000000, 500000000)  # 1M - 500M USD
                                import_quantity = random.uniform(1000, 100000)    # 1K - 100K units

                                trade_data = TradeData(
                                    hts_subheading_id=subheading.id,
                                    country_id=country.id,
                                    year=year,
                                    import_value=import_value,
                                    import_quantity=import_quantity
                                )
                                db.session.add(trade_data)

                db.session.commit()

                trade_count = TradeData.query.count()
                print(f"✅ 生成了 {trade_count} 條貿易資料記錄")

                return True

        except Exception as e:
            print(f"❌ 生成貿易資料錯誤: {e}")
            return False

    def run_import(self):
        """執行完整的資料導入流程"""
        print("🚀 開始 HTS 資料導入流程...")
        print("=" * 50)

        # 1. 嘗試從 API 獲取資料
        hts_data = self.fetch_hts_data_from_api()

        # 2. 如果 API 失敗，使用範例資料
        if not hts_data:
            print("⚠️ API 獲取失敗，使用範例資料")
            hts_data = self.load_sample_hts_data()

        # 3. 載入國家資料
        countries = self.load_country_data()

        # 4. 導入到資料庫
        if self.import_to_database(hts_data, countries):
            print("✅ HTS 基礎資料導入成功")

            # 5. 生成範例貿易資料
            if self.generate_sample_trade_data():
                print("✅ 範例貿易資料生成成功")
            else:
                print("⚠️ 範例貿易資料生成失敗")
        else:
            print("❌ 資料導入失敗")
            return False

        print("=" * 50)
        print("🎉 HTS 資料導入完成！")
        return True

def main():
    """主函數"""
    print("🌟 TARIFFED HTS 資料導入工具")
    print("從 USITC 官方來源導入真實的 HTS 資料")
    print()

    importer = HTSDataImporter()
    success = importer.run_import()

    if success:
        print("\n✅ 所有資料導入完成！您現在可以使用完整的 HTS 資料庫了。")
        print("🔗 啟動您的 TARIFFED 應用來測試新資料：")
        print("   python start_server.py")
    else:
        print("\n❌ 資料導入失敗，請檢查錯誤信息並重試。")

if __name__ == "__main__":
    main()
