#!/usr/bin/env python3

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS
from flasgger import Swagger, swag_from

def create_swagger_app():
    app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'src', 'static'))
    app.config['SECRET_KEY'] = 'tariffed-secret-key-2025'
    
    # Enable CORS for all routes
    CORS(app)
    
    # Swagger configuration
    swagger_config = {
        "headers": [],
        "specs": [
            {
                "endpoint": 'apispec_1',
                "route": '/apispec_1.json',
                "rule_filter": lambda rule: True,
                "model_filter": lambda tag: True,
            }
        ],
        "static_url_path": "/flasgger_static",
        "swagger_ui": True,
        "specs_route": "/api/docs/"
    }

    swagger_template = {
        "swagger": "2.0",
        "info": {
            "title": "TARIFFED API",
            "description": "美國關稅查詢系統 API 文檔 - 基於 AI 的關稅查詢和分析系統",
            "version": "1.0.0",
            "contact": {
                "name": "TARIFFED Team",
                "email": "<EMAIL>"
            },
            "license": {
                "name": "MIT",
                "url": "https://opensource.org/licenses/MIT"
            }
        },
        "host": "localhost:5001",
        "basePath": "/api",
        "schemes": ["http", "https"],
        "consumes": ["application/json"],
        "produces": ["application/json"],
        "tags": [
            {
                "name": "查詢",
                "description": "AI 自然語言查詢相關 API"
            },
            {
                "name": "HTS",
                "description": "HTS 條目搜尋和查詢相關 API"
            },
            {
                "name": "貿易數據",
                "description": "貿易統計數據相關 API"
            },
            {
                "name": "系統",
                "description": "系統狀態和測試相關 API"
            }
        ]
    }

    swagger = Swagger(app, config=swagger_config, template=swagger_template)
    
    # Mock API endpoints with Swagger documentation
    
    @app.route('/api/query', methods=['POST'])
    @swag_from({
        'tags': ['查詢'],
        'summary': 'AI 自然語言查詢',
        'description': '處理使用者的自然語言查詢，返回 AI 分析結果',
        'parameters': [{
            'name': 'body',
            'in': 'body',
            'required': True,
            'schema': {
                'type': 'object',
                'required': ['query'],
                'properties': {
                    'query': {
                        'type': 'string',
                        'description': '使用者的自然語言查詢',
                        'example': '中國進口的馬匹關稅是多少？'
                    }
                }
            }
        }],
        'responses': {
            200: {
                'description': '查詢成功',
                'schema': {
                    'type': 'object',
                    'properties': {
                        'status': {'type': 'string', 'example': 'success'},
                        'data': {'type': 'string', 'example': '根據 HTS 條目 0101.21.00，中國進口的純種繁殖馬匹關稅為免稅...'}
                    }
                }
            },
            400: {
                'description': '請求參數錯誤',
                'schema': {
                    'type': 'object',
                    'properties': {
                        'status': {'type': 'string', 'example': 'error'},
                        'message': {'type': 'string', 'example': '請提供查詢內容'}
                    }
                }
            }
        }
    })
    def query_tariff():
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({'status': 'error', 'message': '請提供查詢內容'}), 400
        
        # Mock response
        return jsonify({
            'status': 'success',
            'data': f'AI 分析結果：您查詢的是「{data["query"]}」。這是一個模擬回應，實際系統會使用 AI 代理處理查詢。'
        })
    
    @app.route('/api/hts/search', methods=['GET'])
    @swag_from({
        'tags': ['HTS'],
        'summary': 'HTS 條目搜尋',
        'description': '根據關鍵字搜尋 HTS 條目',
        'parameters': [{
            'name': 'keyword',
            'in': 'query',
            'type': 'string',
            'required': True,
            'description': '搜尋關鍵字',
            'example': 'horse'
        }],
        'responses': {
            200: {
                'description': '搜尋成功',
                'schema': {
                    'type': 'object',
                    'properties': {
                        'status': {'type': 'string', 'example': 'success'},
                        'data': {
                            'type': 'array',
                            'items': {
                                'type': 'object',
                                'properties': {
                                    'id': {'type': 'string', 'example': '0101.21.00'},
                                    'description': {'type': 'string', 'example': 'Horses: Pure bred breeding animals'},
                                    'unit_of_quantity': {'type': 'string', 'example': 'No.'},
                                    'general_rate': {'type': 'string', 'example': 'Free'},
                                    'special_rate': {'type': 'string', 'example': 'Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)'},
                                    'column_2_rate': {'type': 'string', 'example': 'Free'}
                                }
                            }
                        }
                    }
                }
            }
        }
    })
    def search_hts():
        keyword = request.args.get('keyword', '')
        if not keyword:
            return jsonify({'status': 'error', 'message': '請提供搜尋關鍵字'}), 400
        
        # Mock response
        return jsonify({
            'status': 'success',
            'data': [{
                'id': '0101.21.00',
                'description': f'Mock result for "{keyword}": Horses: Pure bred breeding animals',
                'unit_of_quantity': 'No.',
                'general_rate': 'Free',
                'special_rate': 'Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)',
                'column_2_rate': 'Free'
            }]
        })
    
    @app.route('/api/hts/<hts_id>', methods=['GET'])
    @swag_from({
        'tags': ['HTS'],
        'summary': 'HTS 條目詳細資訊',
        'description': '獲取特定 HTS 條目的詳細資訊，包含層級結構',
        'parameters': [{
            'name': 'hts_id',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'HTS 條目 ID',
            'example': '0101.21.00'
        }],
        'responses': {
            200: {
                'description': '獲取成功',
                'schema': {
                    'type': 'object',
                    'properties': {
                        'status': {'type': 'string', 'example': 'success'},
                        'data': {
                            'type': 'object',
                            'properties': {
                                'id': {'type': 'string', 'example': '0101.21.00'},
                                'description': {'type': 'string', 'example': 'Horses: Pure bred breeding animals'},
                                'unit_of_quantity': {'type': 'string', 'example': 'No.'},
                                'general_rate': {'type': 'string', 'example': 'Free'},
                                'special_rate': {'type': 'string', 'example': 'Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)'},
                                'column_2_rate': {'type': 'string', 'example': 'Free'},
                                'heading': {
                                    'type': 'object',
                                    'properties': {
                                        'id': {'type': 'string', 'example': '0101'},
                                        'description': {'type': 'string', 'example': 'Live horses, asses, mules and hinnies'}
                                    }
                                },
                                'chapter': {
                                    'type': 'object',
                                    'properties': {
                                        'id': {'type': 'integer', 'example': 1},
                                        'title': {'type': 'string', 'example': 'Live Animals'}
                                    }
                                },
                                'section': {
                                    'type': 'object',
                                    'properties': {
                                        'id': {'type': 'integer', 'example': 1},
                                        'title': {'type': 'string', 'example': 'Live Animals; Animal Products'}
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    })
    def get_hts_detail(hts_id):
        # Mock response
        return jsonify({
            'status': 'success',
            'data': {
                'id': hts_id,
                'description': f'Mock HTS detail for {hts_id}: Horses: Pure bred breeding animals',
                'unit_of_quantity': 'No.',
                'general_rate': 'Free',
                'special_rate': 'Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)',
                'column_2_rate': 'Free',
                'heading': {
                    'id': '0101',
                    'description': 'Live horses, asses, mules and hinnies'
                },
                'chapter': {
                    'id': 1,
                    'title': 'Live Animals'
                },
                'section': {
                    'id': 1,
                    'title': 'Live Animals; Animal Products'
                }
            }
        })
    
    @app.route('/test')
    @swag_from({
        'tags': ['系統'],
        'summary': '系統測試端點',
        'responses': {
            200: {
                'description': '系統正常運行',
                'schema': {
                    'type': 'object',
                    'properties': {
                        'status': {'type': 'string', 'example': 'success'},
                        'message': {'type': 'string', 'example': 'TARIFFED Server is running!'}
                    }
                }
            }
        }
    })
    def test():
        return jsonify({'status': 'success', 'message': 'TARIFFED Server with Swagger is running!'})
    
    @app.route('/', defaults={'path': ''})
    @app.route('/<path:path>')
    def serve(path):
        static_folder_path = app.static_folder
        if static_folder_path is None:
            return "Static folder not configured", 404

        if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
            return send_from_directory(static_folder_path, path)
        else:
            index_path = os.path.join(static_folder_path, 'index.html')
            if os.path.exists(index_path):
                return send_from_directory(static_folder_path, 'index.html')
            else:
                return jsonify({
                    'status': 'info',
                    'message': 'TARIFFED Backend Server with Swagger API Documentation',
                    'version': '1.0.0',
                    'endpoints': {
                        'swagger_ui': '/api/docs/',
                        'api_spec': '/apispec_1.json',
                        'test': '/test'
                    }
                })

    return app

if __name__ == '__main__':
    print("Starting TARIFFED Backend Server with Swagger API Documentation...")
    print("Server will be available at: http://localhost:5001")
    print("Swagger UI: http://localhost:5001/api/docs/")
    print("API Specification: http://localhost:5001/apispec_1.json")
    print("Test endpoint: http://localhost:5001/test")
    
    app = create_swagger_app()
    app.run(host='0.0.0.0', port=5001, debug=True)
