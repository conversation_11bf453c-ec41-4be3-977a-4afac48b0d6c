# TARIFFED 環境變量配置文件
# 複製此文件為 .env 並填入實際的 API 密鑰

# === AI 服務配置 ===
# OpenAI API 密鑰（用於 AI 代理）
OPENAI_API_KEY=your_openai_api_key_here

# === 外部搜尋服務配置 ===
# Tavily 搜尋 API 密鑰
TAVILY_API_KEY=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

# Google Custom Search API 配置（可選）
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here

# === 資料庫配置 ===
# SQLite 資料庫路徑
DATABASE_URL=sqlite:///src/database/app.db

# === 服務配置 ===
# Flask 應用配置
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your_secret_key_here

# 服務端口
PORT=5001

# === 日誌配置 ===
LOG_LEVEL=INFO
LOG_FILE=logs/tariffed.log

# === CORS 配置 ===
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# === 快取配置 ===
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# === 外部 API 配置 ===
# API 請求超時時間（秒）
API_TIMEOUT=30

# API 請求重試次數
API_RETRY_COUNT=3

# === 安全配置 ===
# JWT 密鑰（如果使用身份驗證）
JWT_SECRET_KEY=your_jwt_secret_key_here

# API 速率限制
RATE_LIMIT_PER_MINUTE=60

# === 功能開關 ===
# 啟用外部搜尋功能
ENABLE_EXTERNAL_SEARCH=true

# 啟用多代理協作
ENABLE_MULTI_AGENT=true

# 啟用搜尋結果快取
ENABLE_SEARCH_CACHE=true

# === 監控配置 ===
# 啟用性能監控
ENABLE_MONITORING=false

# 監控服務 URL
MONITORING_URL=http://localhost:8080
