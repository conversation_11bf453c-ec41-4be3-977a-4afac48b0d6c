from flask import Blueprint, request, jsonify
from flasgger import swag_from
from src.models.hts import db, HTSSubheading, HTSHeading, HTSChapter, HTSSection, Country, TradeData
from src.services.ai_agent import TariffAIAgent
from src.services.multi_agent_service import multi_agent_service
import logging

tariff_bp = Blueprint('tariff', __name__)

# Initialize AI Agent
ai_agent = TariffAIAgent()

@tariff_bp.route('/query', methods=['POST'])
def query_tariff():
    """
    AI 自然語言查詢
    處理使用者的自然語言查詢，返回 AI 分析結果
    ---
    tags:
      - 查詢
    parameters:
      - name: body
        in: body
        required: true
        description: 查詢請求
        schema:
          type: object
          required:
            - query
          properties:
            query:
              type: string
              description: 使用者的自然語言查詢
              example: "中國進口的馬匹關稅是多少？"
    responses:
      200:
        description: 查詢成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: string
              description: AI 分析結果
              example: "根據 HTS 條目 0101.21.00，中國進口的純種繁殖馬匹關稅為免稅..."
      400:
        description: 請求參數錯誤
        schema:
          type: object
          properties:
            status:
              type: string
              example: "error"
            message:
              type: string
              example: "請提供查詢內容"
      500:
        description: 服務器內部錯誤
        schema:
          type: object
          properties:
            status:
              type: string
              example: "error"
            message:
              type: string
              example: "查詢處理時發生錯誤"
    """
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({
                'status': 'error',
                'message': '請提供查詢內容'
            }), 400
        
        query = data['query']
        context = data.get('context', {})

        # 檢查是否要求協作模式
        collaborative = data.get('collaborative', False)
        specific_agent = data.get('agent', None)

        if collaborative:
            # 使用多代理協作模式
            agents = data.get('agents', None)
            result = multi_agent_service.collaborative_query(query, agents, context)
        elif specific_agent:
            # 使用特定代理
            result = multi_agent_service.query_specific_agent(specific_agent, query, context)
        else:
            # 使用智能路由（默認模式）
            result = multi_agent_service.process_query(query, context)

        if result.get('success', False):
            return jsonify({
                'status': 'success',
                'data': result.get('response', ''),
                'metadata': {
                    'agent_used': result.get('agent_used', 'unknown'),
                    'query_type': result.get('metadata', {}).get('query_type', 'unknown'),
                    'timestamp': result.get('timestamp'),
                    'collaborative_mode': result.get('collaborative_mode', False)
                },
                'additional_data': result.get('data', []),
                'suggestions': result.get('suggestions', []),
                'chart_suggestions': result.get('chart_suggestions', [])
            })
        else:
            return jsonify({
                'status': 'error',
                'message': result.get('error', '處理查詢時發生未知錯誤'),
                'data': result.get('response', '')
            }), 500

    except Exception as e:
        logging.error(f"Query processing error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'查詢處理時發生錯誤: {str(e)}'
        }), 500

@tariff_bp.route('/hts/search', methods=['GET'])
def search_hts():
    """
    HTS 條目搜尋
    根據關鍵字搜尋 HTS 條目
    ---
    tags:
      - HTS
    parameters:
      - name: keyword
        in: query
        type: string
        required: true
        description: 搜尋關鍵字
        example: "horse"
    responses:
      200:
        description: 搜尋成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: HTS 條目 ID
                    example: "0101.21.00"
                  description:
                    type: string
                    description: 商品描述
                    example: "Horses: Pure bred breeding animals"
                  unit_of_quantity:
                    type: string
                    description: 計量單位
                    example: "No."
                  general_rate:
                    type: string
                    description: 一般稅率
                    example: "Free"
                  special_rate:
                    type: string
                    description: 特殊稅率
                    example: "Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)"
                  column_2_rate:
                    type: string
                    description: 第二欄稅率
                    example: "Free"
      400:
        description: 請求參數錯誤
        schema:
          type: object
          properties:
            status:
              type: string
              example: "error"
            message:
              type: string
              example: "請提供搜尋關鍵字"
      500:
        description: 服務器內部錯誤
        schema:
          type: object
          properties:
            status:
              type: string
              example: "error"
            message:
              type: string
              example: "搜尋時發生錯誤"
    """
    try:
        keyword = request.args.get('keyword', '')
        if not keyword:
            return jsonify({
                'status': 'error',
                'message': '請提供搜尋關鍵字'
            }), 400
        
        # 搜尋HTS副標題
        results = HTSSubheading.query.filter(
            HTSSubheading.description.contains(keyword)
        ).limit(20).all()
        
        data = []
        for item in results:
            data.append({
                'id': item.subheading_id,
                'description': item.description,
                'unit_of_quantity': item.unit_of_quantity,
                'general_rate': item.general_rate,
                'special_rate': item.special_rate,
                'column_2_rate': item.column_2_rate
            })
        
        return jsonify({
            'status': 'success',
            'data': data
        })
        
    except Exception as e:
        logging.error(f"HTS search error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '搜尋時發生錯誤'
        }), 500

@tariff_bp.route('/hts/<hts_id>', methods=['GET'])
def get_hts_detail(hts_id):
    """
    HTS 條目詳細資訊
    獲取特定 HTS 條目的詳細資訊，包含層級結構
    ---
    tags:
      - HTS
    parameters:
      - name: hts_id
        in: path
        type: string
        required: true
        description: HTS 條目 ID
        example: "0101.21.00"
    responses:
      200:
        description: 獲取成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: object
              properties:
                id:
                  type: string
                  description: HTS 條目 ID
                  example: "0101.21.00"
                description:
                  type: string
                  description: 商品描述
                  example: "Horses: Pure bred breeding animals"
                unit_of_quantity:
                  type: string
                  description: 計量單位
                  example: "No."
                general_rate:
                  type: string
                  description: 一般稅率
                  example: "Free"
                special_rate:
                  type: string
                  description: 特殊稅率
                  example: "Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)"
                column_2_rate:
                  type: string
                  description: 第二欄稅率
                  example: "Free"
                heading:
                  type: object
                  properties:
                    id:
                      type: string
                      example: "0101"
                    description:
                      type: string
                      example: "Live horses, asses, mules and hinnies"
                chapter:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 1
                    title:
                      type: string
                      example: "Live Animals"
                section:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 1
                    title:
                      type: string
                      example: "Live Animals; Animal Products"
      404:
        description: 找不到指定的 HTS 條目
        schema:
          type: object
          properties:
            status:
              type: string
              example: "error"
            message:
              type: string
              example: "找不到指定的HTS條目"
      500:
        description: 服務器內部錯誤
        schema:
          type: object
          properties:
            status:
              type: string
              example: "error"
            message:
              type: string
              example: "獲取詳細資訊時發生錯誤"
    """
    try:
        hts_item = HTSSubheading.query.get(hts_id)
        if not hts_item:
            return jsonify({
                'status': 'error',
                'message': '找不到指定的HTS條目'
            }), 404
        
        data = {
            'id': hts_item.subheading_id,
            'description': hts_item.description,
            'unit_of_quantity': hts_item.unit_of_quantity,
            'general_rate': hts_item.general_rate,
            'special_rate': hts_item.special_rate,
            'column_2_rate': hts_item.column_2_rate,
            'heading': {
                'id': hts_item.heading.heading_id,
                'description': hts_item.heading.description
            },
            'chapter': {
                'id': hts_item.heading.chapter.chapter_id,
                'title': hts_item.heading.chapter.title
            },
            'section': {
                'id': hts_item.heading.chapter.section.section_id,
                'title': hts_item.heading.chapter.section.title
            }
        }
        
        return jsonify({
            'status': 'success',
            'data': data
        })
        
    except Exception as e:
        logging.error(f"HTS detail error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '獲取詳細資訊時發生錯誤'
        }), 500

@tariff_bp.route('/countries', methods=['GET'])
def get_countries():
    """
    國家列表
    獲取所有國家列表
    ---
    tags:
      - 貿易數據
    responses:
      200:
        description: 獲取成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 國家代碼
                    example: "CN"
                  name:
                    type: string
                    description: 國家名稱
                    example: "China"
      500:
        description: 服務器內部錯誤
        schema:
          type: object
          properties:
            status:
              type: string
              example: "error"
            message:
              type: string
              example: "獲取國家列表時發生錯誤"
    """
    try:
        countries = Country.query.all()
        data = []
        for country in countries:
            data.append({
                'id': country.country_id,
                'name': country.country_name
            })
        
        return jsonify({
            'status': 'success',
            'data': data
        })
        
    except Exception as e:
        logging.error(f"Countries list error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '獲取國家列表時發生錯誤'
        }), 500

@tariff_bp.route('/trade-data/<hts_id>', methods=['GET'])
def get_trade_data(hts_id):
    """
    貿易數據查詢
    獲取特定 HTS 條目的貿易數據
    ---
    tags:
      - 貿易數據
    parameters:
      - name: hts_id
        in: path
        type: string
        required: true
        description: HTS 條目 ID
        example: "0101.21.00"
      - name: year
        in: query
        type: integer
        required: false
        description: 年份篩選
        example: 2023
      - name: country_id
        in: query
        type: string
        required: false
        description: 國家代碼篩選
        example: "CN"
    responses:
      200:
        description: 獲取成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: array
              items:
                type: object
                properties:
                  country:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 國家代碼
                        example: "CN"
                      name:
                        type: string
                        description: 國家名稱
                        example: "China"
                  year:
                    type: integer
                    description: 年份
                    example: 2023
                  import_value:
                    type: number
                    description: 進口金額（美元）
                    example: 1500000.50
                  import_quantity:
                    type: number
                    description: 進口數量
                    example: 150
      500:
        description: 服務器內部錯誤
        schema:
          type: object
          properties:
            status:
              type: string
              example: "error"
            message:
              type: string
              example: "獲取貿易數據時發生錯誤"
    """
    try:
        year = request.args.get('year', type=int)
        country_id = request.args.get('country_id')
        
        query = TradeData.query.filter_by(subheading_id=hts_id)
        
        if year:
            query = query.filter_by(year=year)
        if country_id:
            query = query.filter_by(country_id=country_id)
        
        trade_data = query.all()
        
        data = []
        for item in trade_data:
            data.append({
                'country': {
                    'id': item.country.country_id,
                    'name': item.country.country_name
                },
                'year': item.year,
                'import_value': float(item.import_value) if item.import_value else None,
                'import_quantity': float(item.import_quantity) if item.import_quantity else None
            })
        
        return jsonify({
            'status': 'success',
            'data': data
        })
        
    except Exception as e:
        logging.error(f"Trade data error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '獲取貿易數據時發生錯誤'
        }), 500

@tariff_bp.route('/agents/status', methods=['GET'])
def get_agents_status():
    """
    獲取多代理系統狀態
    ---
    tags:
      - 系統
    responses:
      200:
        description: 獲取成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: object
              properties:
                total_agents:
                  type: integer
                  example: 3
                agents:
                  type: array
                  items:
                    type: object
                    properties:
                      name:
                        type: string
                        example: "query_agent"
                      description:
                        type: string
                        example: "專門處理 HTS 條目查詢和基礎信息檢索"
                      status:
                        type: string
                        example: "active"
    """
    try:
        status = multi_agent_service.get_agent_status()

        return jsonify({
            'status': 'success',
            'data': status
        })

    except Exception as e:
        logging.error(f"Get agents status error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'獲取代理狀態時發生錯誤: {str(e)}'
        }), 500

@tariff_bp.route('/agents/collaborative', methods=['POST'])
def collaborative_query():
    """
    多代理協作查詢
    ---
    tags:
      - 查詢
    parameters:
      - name: body
        in: body
        required: true
        description: 協作查詢請求
        schema:
          type: object
          required:
            - query
          properties:
            query:
              type: string
              description: 使用者的查詢
              example: "分析中國進口電子產品的趨勢並提供採購建議"
            agents:
              type: array
              items:
                type: string
              description: 指定的代理列表
              example: ["query_agent", "analysis_agent", "recommendation_agent"]
            context:
              type: object
              description: 查詢上下文
    responses:
      200:
        description: 協作查詢成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: string
              description: 整合的協作回應
            metadata:
              type: object
              properties:
                collaborative_mode:
                  type: boolean
                  example: true
                agents_used:
                  type: array
                  items:
                    type: string
                  example: ["query_agent", "analysis_agent", "recommendation_agent"]
    """
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({
                'status': 'error',
                'message': '請提供查詢內容'
            }), 400

        query = data['query']
        agents = data.get('agents', None)
        context = data.get('context', {})

        result = multi_agent_service.collaborative_query(query, agents, context)

        if result.get('success', False):
            return jsonify({
                'status': 'success',
                'data': result.get('response', ''),
                'metadata': {
                    'collaborative_mode': True,
                    'agents_used': result.get('agents_used', []),
                    'timestamp': result.get('timestamp'),
                    'individual_results': result.get('individual_results', {}),
                    'failed_agents': result.get('failed_agents', [])
                }
            })
        else:
            return jsonify({
                'status': 'error',
                'message': result.get('error', '協作查詢時發生未知錯誤'),
                'data': result.get('response', '')
            }), 500

    except Exception as e:
        logging.error(f"Collaborative query error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'協作查詢時發生錯誤: {str(e)}'
        }), 500

@tariff_bp.route('/search/external', methods=['POST'])
def external_search():
    """
    外部搜尋 API
    ---
    tags:
      - 搜尋
    parameters:
      - name: body
        in: body
        required: true
        description: 外部搜尋請求
        schema:
          type: object
          required:
            - query
          properties:
            query:
              type: string
              description: 搜尋查詢
              example: "最新的中美貿易政策變化"
            search_type:
              type: string
              description: 搜尋類型
              enum: ["general", "tariff", "trade_news"]
              example: "trade_news"
            source:
              type: string
              description: 搜尋來源
              enum: ["auto", "tavily", "google"]
              example: "auto"
            max_results:
              type: integer
              description: 最大結果數量
              example: 5
    responses:
      200:
        description: 搜尋成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: string
              description: 格式化的搜尋結果
            metadata:
              type: object
              properties:
                source:
                  type: string
                  example: "tavily"
                credibility_score:
                  type: number
                  example: 0.85
                search_type:
                  type: string
                  example: "trade_news"
    """
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({
                'status': 'error',
                'message': '請提供搜尋查詢'
            }), 400

        query = data['query']
        search_type = data.get('search_type', 'general')
        source = data.get('source', 'auto')
        max_results = data.get('max_results', 5)

        # 執行外部搜尋
        result = multi_agent_service.search_external(
            query=query,
            search_type=search_type,
            source=source,
            max_results=max_results
        )

        if result.get('success', False):
            return jsonify({
                'status': 'success',
                'data': result.get('response', ''),
                'metadata': {
                    'source': result.get('source', 'unknown'),
                    'search_type': result.get('search_type', 'general'),
                    'credibility_score': result.get('credibility_score', 0.0),
                    'timestamp': result.get('timestamp'),
                    'results_count': len(result.get('raw_results', {}).get('results', []))
                },
                'raw_results': result.get('raw_results', {})
            })
        else:
            return jsonify({
                'status': 'error',
                'message': result.get('error', '外部搜尋失敗'),
                'query': query
            }), 500

    except Exception as e:
        logging.error(f"External search error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'外部搜尋時發生錯誤: {str(e)}'
        }), 500

@tariff_bp.route('/query/enhanced', methods=['POST'])
def enhanced_query():
    """
    增強查詢 API - 結合內部代理和外部搜尋
    ---
    tags:
      - 查詢
    parameters:
      - name: body
        in: body
        required: true
        description: 增強查詢請求
        schema:
          type: object
          required:
            - query
          properties:
            query:
              type: string
              description: 使用者的查詢
              example: "最新的電子產品進口關稅政策"
            context:
              type: object
              description: 查詢上下文
    responses:
      200:
        description: 增強查詢成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: string
              description: 整合的查詢回應
            metadata:
              type: object
              properties:
                integration_mode:
                  type: boolean
                  example: true
                internal_agent:
                  type: string
                  example: "query_agent"
                external_source:
                  type: string
                  example: "tavily"
                credibility_score:
                  type: number
                  example: 0.85
    """
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({
                'status': 'error',
                'message': '請提供查詢內容'
            }), 400

        query = data['query']
        context = data.get('context', {})

        # 執行增強查詢
        result = multi_agent_service.enhanced_query_with_search(query, context)

        if result.get('success', False):
            return jsonify({
                'status': 'success',
                'data': result.get('response', ''),
                'metadata': {
                    'integration_mode': result.get('integration_mode', False),
                    'internal_agent': result.get('internal_result', {}).get('agent_used', 'unknown'),
                    'external_source': result.get('external_result', {}).get('source', None),
                    'credibility_score': result.get('credibility_score', 0.0),
                    'timestamp': result.get('timestamp')
                },
                'internal_result': result.get('internal_result', {}),
                'external_result': result.get('external_result', {})
            })
        else:
            return jsonify({
                'status': 'error',
                'message': result.get('error', '增強查詢失敗'),
                'query': query
            }), 500

    except Exception as e:
        logging.error(f"Enhanced query error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'增強查詢時發生錯誤: {str(e)}'
        }), 500

@tariff_bp.route('/visualization/generate', methods=['POST'])
def generate_visualization():
    """
    生成數據視覺化
    ---
    tags:
      - 視覺化
    parameters:
      - name: body
        in: body
        required: true
        description: 視覺化生成請求
        schema:
          type: object
          required:
            - data
          properties:
            data:
              type: object
              description: 要視覺化的數據
              example: {"trade_data": [{"year": 2023, "value": 1000000}]}
            chart_type:
              type: string
              description: 指定的圖表類型
              enum: ["line", "bar", "pie", "area", "scatter", "histogram"]
              example: "line"
    responses:
      200:
        description: 視覺化生成成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: object
              description: 圖表配置
            metadata:
              type: object
              properties:
                chart_type:
                  type: string
                  example: "line"
                data_summary:
                  type: object
    """
    try:
        data = request.get_json()
        if not data or 'data' not in data:
            return jsonify({
                'status': 'error',
                'message': '請提供要視覺化的數據'
            }), 400

        chart_type = data.get('chart_type', None)
        visualization_data = data['data']

        # 生成視覺化
        result = multi_agent_service.generate_visualization(visualization_data, chart_type)

        if result.get('success', False):
            return jsonify({
                'status': 'success',
                'data': result.get('chart_config', {}),
                'metadata': {
                    'chart_type': result.get('chart_type', 'unknown'),
                    'data_summary': result.get('data_summary', {}),
                    'timestamp': result.get('timestamp')
                }
            })
        else:
            return jsonify({
                'status': 'error',
                'message': result.get('error', '視覺化生成失敗'),
                'chart_type': chart_type
            }), 500

    except Exception as e:
        logging.error(f"Generate visualization error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'生成視覺化時發生錯誤: {str(e)}'
        }), 500

@tariff_bp.route('/visualization/recommendations', methods=['POST'])
def get_chart_recommendations():
    """
    獲取圖表建議
    ---
    tags:
      - 視覺化
    parameters:
      - name: body
        in: body
        required: true
        description: 圖表建議請求
        schema:
          type: object
          required:
            - query
          properties:
            query:
              type: string
              description: 用戶查詢
              example: "分析中國進口電子產品的趨勢"
            data:
              type: array
              description: 數據樣本
              items:
                type: object
    responses:
      200:
        description: 圖表建議成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: array
              description: 圖表建議列表
              items:
                type: object
                properties:
                  chart_type:
                    type: string
                    example: "line"
                  confidence:
                    type: number
                    example: 0.85
                  reason:
                    type: string
                    example: "適合展示時間序列趨勢"
    """
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({
                'status': 'error',
                'message': '請提供查詢內容'
            }), 400

        query = data['query']
        sample_data = data.get('data', [])

        # 獲取圖表建議
        result = multi_agent_service.get_chart_recommendations(query, sample_data)

        if result.get('success', False):
            return jsonify({
                'status': 'success',
                'data': result.get('recommendations', []),
                'metadata': {
                    'query': query,
                    'analysis': result.get('analysis', {}),
                    'timestamp': result.get('timestamp')
                }
            })
        else:
            return jsonify({
                'status': 'error',
                'message': result.get('error', '獲取圖表建議失敗'),
                'query': query
            }), 500

    except Exception as e:
        logging.error(f"Get chart recommendations error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'獲取圖表建議時發生錯誤: {str(e)}'
        }), 500

@tariff_bp.route('/visualization/dashboard', methods=['POST'])
def generate_dashboard():
    """
    生成統計儀表板
    ---
    tags:
      - 視覺化
    parameters:
      - name: body
        in: body
        required: true
        description: 儀表板生成請求
        schema:
          type: object
          required:
            - data_sources
          properties:
            data_sources:
              type: object
              description: 各種數據源
              example: {"trade_data": [], "tariff_data": [], "country_data": []}
    responses:
      200:
        description: 儀表板生成成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: object
              description: 儀表板配置
              properties:
                title:
                  type: string
                  example: "TARIFFED 貿易數據統計儀表板"
                charts:
                  type: array
                  description: 圖表配置列表
                kpis:
                  type: array
                  description: KPI 指標列表
    """
    try:
        data = request.get_json()
        if not data or 'data_sources' not in data:
            return jsonify({
                'status': 'error',
                'message': '請提供數據源'
            }), 400

        data_sources = data['data_sources']

        # 生成統計儀表板
        result = multi_agent_service.generate_statistical_dashboard(data_sources)

        if result.get('success', False):
            return jsonify({
                'status': 'success',
                'data': result.get('dashboard', {}),
                'metadata': {
                    'timestamp': result.get('timestamp')
                }
            })
        else:
            return jsonify({
                'status': 'error',
                'message': result.get('error', '儀表板生成失敗')
            }), 500

    except Exception as e:
        logging.error(f"Generate dashboard error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'生成儀表板時發生錯誤: {str(e)}'
        }), 500

@tariff_bp.route('/analysis/enhanced', methods=['POST'])
def enhanced_analysis_with_visualization():
    """
    帶視覺化的增強分析
    ---
    tags:
      - 分析
    parameters:
      - name: body
        in: body
        required: true
        description: 增強分析請求
        schema:
          type: object
          required:
            - query
          properties:
            query:
              type: string
              description: 用戶查詢
              example: "分析中國進口電子產品的趨勢並提供視覺化建議"
            context:
              type: object
              description: 查詢上下文
    responses:
      200:
        description: 增強分析成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: string
              description: 增強的分析回應
            metadata:
              type: object
              properties:
                chart_recommendations:
                  type: array
                  description: 圖表建議
                visualization:
                  type: object
                  description: 視覺化配置
    """
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({
                'status': 'error',
                'message': '請提供查詢內容'
            }), 400

        query = data['query']
        context = data.get('context', {})

        # 執行帶視覺化的增強分析
        result = multi_agent_service.analyze_with_visualization(query, context)

        if result.get('success', False):
            return jsonify({
                'status': 'success',
                'data': result.get('enhanced_response', ''),
                'metadata': {
                    'analysis': result.get('analysis', {}),
                    'chart_recommendations': result.get('chart_recommendations', []),
                    'visualization': result.get('visualization', {}),
                    'timestamp': result.get('timestamp')
                }
            })
        else:
            return jsonify({
                'status': 'error',
                'message': result.get('error', '增強分析失敗'),
                'query': query
            }), 500

    except Exception as e:
        logging.error(f"Enhanced analysis with visualization error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'增強分析時發生錯誤: {str(e)}'
        }), 500

@tariff_bp.route('/hts/subheadings', methods=['GET'])
def get_hts_subheadings():
    """
    獲取 HTS 子標題建議 (兼容端點)
    ---
    tags:
      - HTS 查詢
    parameters:
      - name: search
        in: query
        type: string
        required: true
        description: 搜尋關鍵字
        example: "horse"
      - name: limit
        in: query
        type: integer
        default: 10
        description: 返回結果數量限制
        example: 5
    responses:
      200:
        description: HTS 建議列表
        schema:
          type: object
          properties:
            status:
              type: string
              example: "success"
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    example: "0101.21.00"
                  description:
                    type: string
                    example: "Horses: Pure bred breeding animals"
    """
    try:
        search_term = request.args.get('search', '')
        limit = int(request.args.get('limit', 10))

        if not search_term:
            return jsonify({
                'status': 'error',
                'message': '請提供搜尋關鍵字'
            }), 400

        # 使用 AI 代理獲取 HTS 建議
        from src.services.ai_agent import TariffAIAgent
        ai_agent = TariffAIAgent()

        suggestions = ai_agent.get_hts_suggestions(search_term, limit=limit)

        return jsonify({
            'status': 'success',
            'data': suggestions,
            'metadata': {
                'search_term': search_term,
                'result_count': len(suggestions),
                'limit': limit
            }
        })

    except Exception as e:
        logging.error(f"Get HTS subheadings error: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'獲取 HTS 建議時發生錯誤: {str(e)}'
        }), 500

