#!/usr/bin/env python3
"""
TARIFFED 功能測試腳本
"""

import sys
import os
sys.path.insert(0, '.')

def test_external_search():
    """測試外部搜尋功能"""
    print("🔍 測試外部搜尋 API...")
    try:
        from src.services.external_search_service import external_search_service
        result = external_search_service.search('US tariff rates 2024', max_results=3)
        
        if result.get('success'):
            data = result.get('data', {})
            results = data.get('results', [])
            print(f"✅ 外部搜尋 API 測試通過 - 找到 {len(results)} 個結果")
            return True
        else:
            print(f"❌ 外部搜尋 API 測試失敗: {result.get('error', 'Unknown')}")
            return False
    except Exception as e:
        print(f"❌ 外部搜尋 API 測試異常: {e}")
        return False

def test_hts_data():
    """測試 HTS 資料"""
    print("\n🏷️ 測試 HTS 資料導入...")
    try:
        from src.services.ai_agent import TariffAIAgent
        ai_agent = TariffAIAgent()
        suggestions = ai_agent.get_hts_suggestions("horse", limit=3)
        
        if suggestions and len(suggestions) > 0:
            print(f"✅ HTS 資料測試通過 - 找到 {len(suggestions)} 個建議")
            return True
        else:
            print("❌ HTS 資料測試失敗 - 沒有找到建議")
            return False
    except Exception as e:
        print(f"❌ HTS 資料測試異常: {e}")
        return False

def test_visualization():
    """測試視覺化功能"""
    print("\n📊 測試資料視覺化...")
    try:
        from src.services.visualization_service import visualization_service
        from src.services.chart_recommendation_service import chart_recommendation_service
        
        # 測試圖表生成
        trade_data = [
            {'date': '2022-01', 'value': 1000000},
            {'date': '2022-02', 'value': 1200000},
            {'date': '2022-03', 'value': 1400000}
        ]
        
        chart = visualization_service.generate_trade_trend_chart(trade_data, 'line')
        
        if chart.get('type') == 'line':
            print("✅ 圖表生成測試通過")
        else:
            print("❌ 圖表生成測試失敗")
            return False
        
        # 測試圖表建議
        recommendations = chart_recommendation_service.recommend_charts("分析進口趨勢")
        
        if recommendations.get('success') and recommendations.get('recommendations'):
            print("✅ 圖表建議測試通過")
            return True
        else:
            print("❌ 圖表建議測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ 視覺化測試異常: {e}")
        return False

def test_ai_optimization():
    """測試 AI 優化"""
    print("\n🤖 測試 AI 回應優化...")
    try:
        from src.prompts.professional_prompts import ProfessionalPrompts, ResponseTemplates
        
        # 測試提示詞
        query_prompt = ProfessionalPrompts.get_query_agent_prompt()
        
        if len(query_prompt) > 500:
            print("✅ AI 提示詞優化測試通過")
        else:
            print("❌ AI 提示詞優化測試失敗")
            return False
        
        # 測試模板
        hts_template = ResponseTemplates.hts_classification_template()
        
        if "{hts_code}" in hts_template:
            print("✅ 回應模板測試通過")
            return True
        else:
            print("❌ 回應模板測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ AI 優化測試異常: {e}")
        return False

def test_multi_agent():
    """測試多代理架構"""
    print("\n🤖 測試多代理架構...")
    try:
        from src.services.multi_agent_service import multi_agent_service
        
        result = multi_agent_service.process_query("查找馬匹的 HTS 條目")
        
        if result.get('success'):
            agent_used = result.get('agent_used', '')
            print(f"✅ 多代理架構測試通過 - 使用代理: {agent_used}")
            return True
        else:
            print(f"❌ 多代理架構測試失敗: {result.get('error', 'Unknown')}")
            return False
            
    except Exception as e:
        print(f"❌ 多代理架構測試異常: {e}")
        return False

def test_api_docs():
    """測試 API 文檔"""
    print("\n📚 測試 API 文檔...")
    try:
        import requests
        response = requests.get("http://localhost:5001/api/docs/", timeout=10)
        
        if response.status_code == 200:
            print("✅ API 文檔測試通過")
            return True
        else:
            print(f"❌ API 文檔測試失敗: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ API 文檔測試失敗: 無法連接服務器")
        return False
    except Exception as e:
        print(f"❌ API 文檔測試異常: {e}")
        return False

def main():
    """主測試函數"""
    print("🌟 TARIFFED 系統功能測試")
    print("=" * 50)
    
    tests = [
        ("外部搜尋 API", test_external_search),
        ("HTS 資料導入", test_hts_data),
        ("資料視覺化", test_visualization),
        ("AI 回應優化", test_ai_optimization),
        ("多代理架構", test_multi_agent),
        ("API 文檔", test_api_docs)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    print("\n" + "=" * 50)
    print("📋 測試結果總結:")
    
    passed = 0
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 通過率: {passed}/{len(tests)} ({passed/len(tests)*100:.1f}%)")
    
    if passed >= 5:
        print("\n🎉 大部分功能測試通過！系統運行良好。")
        return True
    else:
        print("\n⚠️ 部分功能測試失敗，需要檢查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
