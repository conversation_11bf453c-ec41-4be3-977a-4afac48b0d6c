"""
範例資料導入腳本
用於在開發階段填充資料庫
"""

import os
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.models.hts import db, HTSSection, HTSChapter, HTSHeading, HTSSubheading, Country, TradeData
from flask import Flask

def create_sample_data():
    """
    建立範例資料
    """
    
    # 建立範例章節
    section1 = HTSSection(section_id=1, title="Live Animals; Animal Products")
    section2 = HTSSection(section_id=2, title="Vegetable Products")
    
    db.session.add_all([section1, section2])
    
    # 建立範例章
    chapter1 = HTSChapter(chapter_id=1, section_id=1, title="Live animals")
    chapter2 = HTSChapter(chapter_id=2, section_id=1, title="Meat and edible meat offal")
    chapter3 = HTSChapter(chapter_id=6, section_id=2, title="Live trees and other plants")
    
    db.session.add_all([chapter1, chapter2, chapter3])
    
    # 建立範例標題
    heading1 = HTSHeading(heading_id="0101", chapter_id=1, description="Live horses, asses, mules and hinnies")
    heading2 = HTSHeading(heading_id="0201", chapter_id=2, description="Meat of bovine animals, fresh or chilled")
    heading3 = HTSHeading(heading_id="0602", chapter_id=6, description="Other live plants")
    
    db.session.add_all([heading1, heading2, heading3])
    
    # 建立範例副標題
    subheadings = [
        HTSSubheading(
            subheading_id="0101.21.00",
            heading_id="0101",
            description="Horses: Pure bred breeding animals",
            unit_of_quantity="No.",
            general_rate="Free",
            special_rate="Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
            column_2_rate="Free"
        ),
        HTSSubheading(
            subheading_id="0201.10.05",
            heading_id="0201",
            description="Beef, fresh or chilled: Carcasses and half-carcasses of bovine animals",
            unit_of_quantity="kg",
            general_rate="4.4¢/kg",
            special_rate="Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
            column_2_rate="30.8¢/kg"
        ),
        HTSSubheading(
            subheading_id="0602.10.00",
            heading_id="0602",
            description="Unrooted cuttings and slips",
            unit_of_quantity="No.",
            general_rate="4.8%",
            special_rate="Free (A+,AU,BH,CA,CL,CO,D,E,IL,JO,KR,MA,MX,OM,P,PA,PE,S,SG)",
            column_2_rate="25%"
        )
    ]
    
    db.session.add_all(subheadings)
    
    # 建立範例國家
    countries = [
        Country(country_id="US", country_name="United States"),
        Country(country_id="CN", country_name="China"),
        Country(country_id="CA", country_name="Canada"),
        Country(country_id="MX", country_name="Mexico"),
        Country(country_id="JP", country_name="Japan"),
        Country(country_id="DE", country_name="Germany"),
        Country(country_id="UK", country_name="United Kingdom"),
        Country(country_id="FR", country_name="France"),
        Country(country_id="IT", country_name="Italy"),
        Country(country_id="BR", country_name="Brazil")
    ]
    
    db.session.add_all(countries)
    
    # 建立範例貿易數據
    trade_data = [
        TradeData(
            subheading_id="0101.21.00",
            country_id="CA",
            year=2023,
            import_value=1500000.00,
            import_quantity=250
        ),
        TradeData(
            subheading_id="0201.10.05",
            country_id="CA",
            year=2023,
            import_value=45000000.00,
            import_quantity=12500000
        ),
        TradeData(
            subheading_id="0201.10.05",
            country_id="MX",
            year=2023,
            import_value=28000000.00,
            import_quantity=8200000
        ),
        TradeData(
            subheading_id="0602.10.00",
            country_id="CA",
            year=2023,
            import_value=2800000.00,
            import_quantity=1500000
        )
    ]
    
    db.session.add_all(trade_data)
    
    # 提交所有變更
    db.session.commit()
    print("範例資料已成功導入！")

def main():
    """
    主函數
    """
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), '..', 'database', 'app.db')}"
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    
    with app.app_context():
        # 建立所有表格
        db.create_all()
        
        # 檢查是否已有資料
        if HTSSection.query.first() is None:
            create_sample_data()
        else:
            print("資料庫已包含資料，跳過範例資料導入。")

if __name__ == "__main__":
    main()

