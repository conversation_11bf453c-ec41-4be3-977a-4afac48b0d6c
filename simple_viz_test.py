#!/usr/bin/env python3
"""
簡單的視覺化功能測試
"""

import sys
import os
sys.path.insert(0, '.')

def test_visualization():
    """測試視覺化功能"""
    print("📊 測試視覺化服務...")
    
    try:
        from src.services.visualization_service import visualization_service
        from src.services.chart_recommendation_service import chart_recommendation_service
        
        # 測試貿易趨勢圖表
        print("\n📈 測試貿易趨勢圖表...")
        trade_data = [
            {'date': '2022-01', 'value': 1000000},
            {'date': '2022-02', 'value': 1200000},
            {'date': '2022-03', 'value': 1400000}
        ]
        
        chart = visualization_service.generate_trade_trend_chart(trade_data, 'line')
        print(f"✅ 貿易趨勢圖表生成: {chart.get('type', 'unknown')}")
        
        if chart.get('statistics'):
            stats = chart['statistics']
            print(f"   平均值: {stats.get('mean_value', 'N/A')}")
            print(f"   增長率: {stats.get('growth_rate', 'N/A')}%")
        
        # 測試關稅比較圖表
        print("\n🏷️ 測試關稅比較圖表...")
        tariff_data = [
            {'hts_code': '0101.21.00', 'general_rate': 'Free', 'special_rate': 'Free'},
            {'hts_code': '8471.30.01', 'general_rate': '2.5%', 'special_rate': 'Free'}
        ]
        
        tariff_chart = visualization_service.generate_tariff_comparison_chart(tariff_data)
        print(f"✅ 關稅比較圖表生成: {tariff_chart.get('type', 'unknown')}")
        
        # 測試圖表建議
        print("\n💡 測試圖表建議...")
        recommendations = chart_recommendation_service.recommend_charts('分析進口趨勢')
        
        if recommendations.get('success'):
            recs = recommendations.get('recommendations', [])
            print(f"✅ 圖表建議生成: {len(recs)} 個建議")
            
            if recs:
                top_rec = recs[0]
                print(f"   首選: {top_rec['chart_type']} (信心度: {top_rec['confidence']:.1%})")
        else:
            print("❌ 圖表建議失敗")
        
        # 測試多代理整合
        print("\n🤖 測試多代理整合...")
        from src.services.multi_agent_service import multi_agent_service
        
        viz_data = {
            'query': '分析貿易趨勢',
            'trade_data': trade_data
        }
        
        viz_result = multi_agent_service.generate_visualization(viz_data, 'line')
        
        if viz_result.get('success'):
            print("✅ 多代理視覺化生成成功")
            print(f"   圖表類型: {viz_result.get('chart_type', 'N/A')}")
        else:
            print(f"❌ 多代理視覺化失敗: {viz_result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🌟 TARIFFED 視覺化功能簡單測試")
    print("=" * 40)
    
    success = test_visualization()
    
    if success:
        print("\n🎉 視覺化功能測試成功！")
        print("\n✅ 已完成功能:")
        print("   • 貿易趨勢圖表生成")
        print("   • 關稅比較圖表生成")
        print("   • 智能圖表建議")
        print("   • 多代理系統整合")
        print("   • 統計分析計算")
    else:
        print("\n⚠️ 測試失敗，請檢查錯誤信息")
    
    print("\n📊 視覺化功能已就緒！")
