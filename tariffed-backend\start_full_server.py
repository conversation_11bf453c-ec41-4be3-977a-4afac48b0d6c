#!/usr/bin/env python3

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

from flask import Flask, send_from_directory, jsonify
from flask_cors import CORS
from flasgger import Swagger

def create_full_app():
    app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'src', 'static'))
    app.config['SECRET_KEY'] = 'tariffed-secret-key-2025'
    
    # Enable CORS for all routes
    CORS(app)
    
    # Swagger configuration
    swagger_config = {
        "headers": [],
        "specs": [
            {
                "endpoint": 'apispec_1',
                "route": '/apispec_1.json',
                "rule_filter": lambda rule: True,
                "model_filter": lambda tag: True,
            }
        ],
        "static_url_path": "/flasgger_static",
        "swagger_ui": True,
        "specs_route": "/api/docs/"
    }

    swagger_template = {
        "swagger": "2.0",
        "info": {
            "title": "TARIFFED API",
            "description": "美國關稅查詢系統 API 文檔 - 基於 AI 的關稅查詢和分析系統",
            "version": "1.0.0",
            "contact": {
                "name": "TARIFFED Team",
                "email": "<EMAIL>"
            },
            "license": {
                "name": "MIT",
                "url": "https://opensource.org/licenses/MIT"
            }
        },
        "host": "localhost:5001",
        "basePath": "/api",
        "schemes": ["http", "https"],
        "consumes": ["application/json"],
        "produces": ["application/json"],
        "tags": [
            {
                "name": "查詢",
                "description": "AI 自然語言查詢相關 API"
            },
            {
                "name": "HTS",
                "description": "HTS 條目搜尋和查詢相關 API"
            },
            {
                "name": "貿易數據",
                "description": "貿易統計數據相關 API"
            },
            {
                "name": "系統",
                "description": "系統狀態和測試相關 API"
            }
        ]
    }

    swagger = Swagger(app, config=swagger_config, template=swagger_template)
    
    # Import and register blueprints
    try:
        from src.models.hts import db
        from src.routes.tariff import tariff_bp
        
        # Database configuration
        app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'src', 'database', 'app.db')}"
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        db.init_app(app)
        
        with app.app_context():
            db.create_all()
        
        app.register_blueprint(tariff_bp, url_prefix='/api')
        print("✓ Full API with database loaded successfully")
        
    except Exception as e:
        print(f"⚠ Warning: Could not load full API: {e}")
        print("Running in basic mode...")
    
    @app.route('/test')
    def test():
        """
        系統測試端點
        ---
        tags:
          - 系統
        responses:
          200:
            description: 系統正常運行
            schema:
              type: object
              properties:
                status:
                  type: string
                  example: "success"
                message:
                  type: string
                  example: "TARIFFED Server is running!"
        """
        return jsonify({'status': 'success', 'message': 'TARIFFED Server is running!'})
    
    @app.route('/api/test')
    def api_test():
        """
        API 測試端點
        ---
        tags:
          - 系統
        responses:
          200:
            description: API 正常運行
            schema:
              type: object
              properties:
                status:
                  type: string
                  example: "success"
                message:
                  type: string
                  example: "API is working!"
        """
        return jsonify({'status': 'success', 'message': 'API is working!'})
    
    @app.route('/', defaults={'path': ''})
    @app.route('/<path:path>')
    def serve(path):
        static_folder_path = app.static_folder
        if static_folder_path is None:
            return "Static folder not configured", 404

        if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
            return send_from_directory(static_folder_path, path)
        else:
            index_path = os.path.join(static_folder_path, 'index.html')
            if os.path.exists(index_path):
                return send_from_directory(static_folder_path, 'index.html')
            else:
                return jsonify({
                    'status': 'info',
                    'message': 'TARIFFED Backend Server',
                    'version': '1.0.0',
                    'endpoints': {
                        'api_docs': '/api/docs/',
                        'test': '/test',
                        'api_test': '/api/test'
                    }
                })

    return app

if __name__ == '__main__':
    print("Starting TARIFFED Full Backend Server with Swagger API Documentation...")
    print("Server will be available at: http://localhost:5001")
    print("API Documentation: http://localhost:5001/api/docs/")
    print("Test endpoint: http://localhost:5001/test")
    print("API test endpoint: http://localhost:5001/api/test")
    
    app = create_full_app()
    app.run(host='0.0.0.0', port=5001, debug=True)
