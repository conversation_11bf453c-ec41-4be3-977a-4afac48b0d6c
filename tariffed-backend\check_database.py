#!/usr/bin/env python3
"""
檢查資料庫狀態
"""

import os
import sqlite3

def check_database():
    db_path = os.path.join('src', 'database', 'app.db')
    
    print("🔍 檢查資料庫狀態...")
    print(f"資料庫路徑: {db_path}")
    
    if not os.path.exists(db_path):
        print("❌ 資料庫文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 找到 {len(tables)} 個表:")
        for table in tables:
            table_name = table[0]
            print(f"  - {table_name}")
            
            if not table_name.startswith('sqlite_'):
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"    記錄數: {count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 資料庫檢查錯誤: {e}")
        return False

if __name__ == "__main__":
    os.chdir('tariffed-backend')
    check_database()
