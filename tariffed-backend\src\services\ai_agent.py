import os
from openai import OpenAI
import sqlite3
import logging
import json

class TariffAIAgent:
    def __init__(self):
        """
        初始化AI代理
        """
        self.db_path = os.path.join(os.path.dirname(__file__), '..', 'database', 'app.db')
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """
        初始化OpenRouter客戶端
        """
        try:
            self.client = OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key="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",
            )
            logging.info("OpenRouter client initialized successfully")
        except Exception as e:
            logging.error(f"Failed to initialize OpenRouter client: {str(e)}")
            self.client = None
    
    def _get_database_context(self, user_query: str) -> str:
        """
        根據用戶查詢獲取相關的資料庫資訊
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 簡單的關鍵字匹配來決定查詢哪些表
            query_lower = user_query.lower()
            context = ""
            
            # 如果查詢涉及HTS或商品，獲取一些樣本數據
            if any(keyword in query_lower for keyword in ['hts', '商品', '關稅', 'tariff', 'product']):
                cursor.execute("""
                    SELECT h.subheading_id, h.description, h.general_rate, h.special_rate 
                    FROM hts_subheadings h 
                    LIMIT 5
                """)
                hts_data = cursor.fetchall()
                if hts_data:
                    context += "\n資料庫中的HTS範例資料:\n"
                    for row in hts_data:
                        context += f"- {row[0]}: {row[1]} (一般稅率: {row[2]}, 特殊稅率: {row[3]})\n"
            
            # 如果查詢涉及國家，獲取國家列表
            if any(keyword in query_lower for keyword in ['國家', 'country', '進口', 'import']):
                cursor.execute("SELECT country_id, country_name FROM countries LIMIT 10")
                country_data = cursor.fetchall()
                if country_data:
                    context += "\n資料庫中的國家資料:\n"
                    for row in country_data:
                        context += f"- {row[0]}: {row[1]}\n"
            
            conn.close()
            return context
            
        except Exception as e:
            logging.error(f"Database context error: {str(e)}")
            return ""
    
    def process_query(self, user_query: str) -> str:
        """
        處理使用者查詢
        
        Args:
            user_query (str): 使用者的自然語言查詢
            
        Returns:
            str: AI代理的回應
        """
        try:
            if not self.client:
                return "AI服務暫時無法使用，請稍後再試。"
            
            # 獲取資料庫上下文資訊
            db_context = self._get_database_context(user_query)
            
            # 構建系統提示
            system_prompt = """你是一個專門處理美國關稅和國際貿易問題的AI助手。你的任務是幫助使用者理解：
1. 美國協調關稅表(HTS)的結構和內容
2. 不同國家的關稅率和貿易政策
3. 關稅變化對商品價格和貿易的影響
4. 替代商品和國內生產選項

請用繁體中文回答，並提供準確、有用的資訊。

當提供數據時，請儘可能使用表格格式，特別是當回答關於主要進口國、貿易數據或關稅率比較時。
表格應該包含清晰的標題和數值，格式如下：

| 排名 | 國家 | 進口總值（美元） | 主要商品類別 | 進口佔比（%） |
|------|------|------------------|--------------|--------------|
            
            if db_context:
                system_prompt += f"\n\n以下是相關的資料庫資訊，你可以參考這些資料來回答問題：{db_context}"
            
            # 使用OpenRouter API
            completion = self.client.chat.completions.create(
                extra_body={},
                model="openai/gpt-oss-20b",
                messages=[
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": user_query
                    }
                ],
                temperature=0.1,
                max_tokens=1000
            )
            
            return completion.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Query processing error: {str(e)}")
            return "處理查詢時發生錯誤，請稍後再試。"
    

    
    def get_hts_suggestions(self, keyword: str, limit: int = 5) -> list:
        """
        根據關鍵字獲取HTS建議
        
        Args:
            keyword (str): 搜尋關鍵字
            limit (int): 返回結果數量限制
            
        Returns:
            list: HTS建議列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = """
            SELECT subheading_id, description
            FROM hts_subheadings
            WHERE description LIKE ?
            LIMIT ?
            """

            cursor.execute(query, (f'%{keyword}%', limit))
            results = cursor.fetchall()
            conn.close()

            return [{'id': row[0], 'description': row[1]} for row in results]

        except Exception as e:
            logging.error(f"HTS suggestions error: {str(e)}")
            return []

