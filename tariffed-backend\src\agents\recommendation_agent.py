#!/usr/bin/env python3
"""
建議代理 - 專門提供貿易策略建議、替代商品推薦和優化方案
"""

import os
import sys
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.agents.base_agent import BaseAgent
from src.models.hts import HTSSubheading, Country
from src.prompts.professional_prompts import ProfessionalPrompts, ResponseTemplates, QualityEnhancers
import logging

logger = logging.getLogger(__name__)

class RecommendationAgent(BaseAgent):
    """建議代理 - 專門提供貿易策略建議、替代商品推薦和優化方案"""
    
    def __init__(self):
        super().__init__(
            name="recommendation_agent",
            description="專門提供貿易策略建議、替代商品推薦、成本優化方案和合規建議",
            model="gpt-4"
        )
        
        # 添加專門的建議工具
        self.add_tool("suggest_alternatives", self._suggest_alternatives)
        self.add_tool("optimize_tariff_costs", self._optimize_tariff_costs)
        self.add_tool("recommend_suppliers", self._recommend_suppliers)
        self.add_tool("compliance_guidance", self._compliance_guidance)
        self.add_tool("trade_strategy_advice", self._trade_strategy_advice)
    
    def get_system_prompt(self) -> str:
        """獲取建議代理的專業系統提示詞"""
        return ProfessionalPrompts.get_recommendation_agent_prompt()
    
    def process_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理建議查詢請求"""
        try:
            # 分析查詢類型
            recommendation_type = self._analyze_query_type(query)
            
            # 根據建議類型執行不同的處理邏輯
            if recommendation_type == "alternative_products":
                result = self._handle_alternative_products(query, context)
            elif recommendation_type == "cost_optimization":
                result = self._handle_cost_optimization(query, context)
            elif recommendation_type == "supplier_recommendation":
                result = self._handle_supplier_recommendation(query, context)
            elif recommendation_type == "compliance_guidance":
                result = self._handle_compliance_guidance(query, context)
            elif recommendation_type == "trade_strategy":
                result = self._handle_trade_strategy(query, context)
            else:
                result = self._handle_general_recommendation(query, context)
            
            # 記錄交互
            self.log_interaction(query, result.get('response', ''), {
                'recommendation_type': recommendation_type,
                'context': context
            })
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 建議代理處理錯誤: {e}")
            return {
                'success': False,
                'error': str(e),
                'response': f"抱歉，處理您的建議請求時發生錯誤：{e}"
            }
    
    def _analyze_query_type(self, query: str) -> str:
        """分析查詢類型"""
        query_lower = query.lower()
        
        if any(keyword in query_lower for keyword in ['替代', '代替', '其他選擇', '替換']):
            return "alternative_products"
        elif any(keyword in query_lower for keyword in ['降低成本', '節省', '優化', '便宜']):
            return "cost_optimization"
        elif any(keyword in query_lower for keyword in ['供應商', '來源國', '哪個國家', '推薦國家']):
            return "supplier_recommendation"
        elif any(keyword in query_lower for keyword in ['合規', '法規', '要求', '規定']):
            return "compliance_guidance"
        elif any(keyword in query_lower for keyword in ['策略', '計劃', '方案', '建議']):
            return "trade_strategy"
        else:
            return "general_recommendation"
    
    def _handle_alternative_products(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理替代商品建議"""
        # 獲取當前商品信息
        current_product = self._extract_product_info(query, context)
        
        # 尋找替代商品
        alternatives = self._suggest_alternatives(current_product)
        
        if alternatives:
            response = self._format_alternatives_response(alternatives, query)
            return {
                'success': True,
                'response': response,
                'recommendation_type': 'alternative_products',
                'data': alternatives,
                'action_items': self._generate_alternative_action_items(alternatives)
            }
        else:
            return {
                'success': True,
                'response': f"根據您的查詢「{query}」，目前沒有找到明顯的替代商品。建議諮詢專業的貿易顧問以獲得更詳細的替代方案分析。",
                'recommendation_type': 'alternative_products',
                'data': []
            }
    
    def _handle_cost_optimization(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理成本優化建議"""
        # 分析當前成本結構
        cost_analysis = self._analyze_current_costs(query, context)
        
        # 生成優化建議
        optimization_strategies = self._optimize_tariff_costs(cost_analysis)
        
        response = self._format_cost_optimization_response(optimization_strategies, query)
        
        return {
            'success': True,
            'response': response,
            'recommendation_type': 'cost_optimization',
            'data': optimization_strategies,
            'potential_savings': self._calculate_potential_savings(optimization_strategies)
        }
    
    def _handle_supplier_recommendation(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理供應商推薦"""
        # 分析商品需求
        product_requirements = self._extract_product_requirements(query, context)
        
        # 推薦供應商國家
        supplier_recommendations = self._recommend_suppliers(product_requirements)
        
        response = self._format_supplier_recommendations_response(supplier_recommendations, query)
        
        return {
            'success': True,
            'response': response,
            'recommendation_type': 'supplier_recommendation',
            'data': supplier_recommendations,
            'evaluation_criteria': self._get_supplier_evaluation_criteria()
        }
    
    def _handle_compliance_guidance(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理合規指導"""
        # 識別合規需求
        compliance_requirements = self._identify_compliance_requirements(query, context)
        
        # 提供合規指導
        guidance = self._compliance_guidance(compliance_requirements)
        
        response = self._format_compliance_guidance_response(guidance, query)
        
        return {
            'success': True,
            'response': response,
            'recommendation_type': 'compliance_guidance',
            'data': guidance,
            'compliance_checklist': self._generate_compliance_checklist(guidance)
        }
    
    def _handle_trade_strategy(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理貿易策略建議"""
        # 分析貿易需求
        trade_requirements = self._analyze_trade_requirements(query, context)
        
        # 制定策略建議
        strategy_advice = self._trade_strategy_advice(trade_requirements)
        
        response = self._format_trade_strategy_response(strategy_advice, query)
        
        return {
            'success': True,
            'response': response,
            'recommendation_type': 'trade_strategy',
            'data': strategy_advice,
            'implementation_plan': self._create_implementation_plan(strategy_advice)
        }
    
    def _handle_general_recommendation(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """處理一般建議查詢"""
        response = f"""關於您的查詢「{query}」，我可以為您提供以下類型的專業建議：

**🔄 替代商品建議**
- 尋找稅率更低的替代商品
- 分析功能相似的產品選項
- 評估替代方案的可行性

**💰 成本優化策略**
- 關稅成本降低方案
- 供應鏈優化建議
- 貿易協定利用策略

**🌍 供應商推薦**
- 最佳供應國家分析
- 供應商風險評估
- 多元化採購策略

**📋 合規指導**
- 海關合規要求
- 進口許可和認證
- 貿易法規遵循

**📈 貿易策略**
- 綜合採購策略
- 市場進入建議
- 風險管理方案

請提供更具體的需求，我會為您制定詳細的建議方案。"""
        
        return {
            'success': True,
            'response': response,
            'recommendation_type': 'general_recommendation',
            'available_services': [
                "替代商品分析",
                "成本優化方案",
                "供應商評估",
                "合規指導",
                "貿易策略制定"
            ]
        }
    
    def _suggest_alternatives(self, product_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """建議替代商品"""
        # 模擬替代商品建議
        mock_alternatives = [
            {
                'hts_code': '8471.30.02',
                'description': 'Desktop automatic data processing machines',
                'current_tariff': '2.5%',
                'alternative_tariff': 'Free',
                'potential_savings': '2.5%',
                'similarity_score': 0.85,
                'considerations': ['功能相似', '稅率更低', '供應商較多']
            },
            {
                'hts_code': '8471.41.01',
                'description': 'Data processing machines with enhanced capabilities',
                'current_tariff': '2.5%',
                'alternative_tariff': '1.0%',
                'potential_savings': '1.5%',
                'similarity_score': 0.75,
                'considerations': ['功能增強', '稅率較低', '技術先進']
            }
        ]
        
        return mock_alternatives
    
    def _optimize_tariff_costs(self, cost_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """優化關稅成本"""
        optimization_strategies = [
            {
                'strategy': 'FTA利用',
                'description': '利用自由貿易協定獲得優惠稅率',
                'potential_savings': '15-25%',
                'implementation_difficulty': 'Medium',
                'requirements': ['原產地證明', '符合FTA規則'],
                'timeline': '2-3個月'
            },
            {
                'strategy': '商品重新分類',
                'description': '重新評估商品的HTS分類',
                'potential_savings': '5-15%',
                'implementation_difficulty': 'High',
                'requirements': ['專業評估', '海關預裁定'],
                'timeline': '3-6個月'
            },
            {
                'strategy': '供應鏈調整',
                'description': '調整供應商國家以獲得更好稅率',
                'potential_savings': '10-20%',
                'implementation_difficulty': 'Medium',
                'requirements': ['供應商評估', '品質驗證'],
                'timeline': '4-8個月'
            }
        ]
        
        return optimization_strategies
    
    def _recommend_suppliers(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """推薦供應商"""
        supplier_recommendations = [
            {
                'country': 'Canada',
                'country_code': 'CA',
                'tariff_rate': 'Free (USMCA)',
                'advantages': ['免稅', '地理位置近', '貿易協定'],
                'considerations': ['成本可能較高', '產能限制'],
                'risk_level': 'Low',
                'logistics_score': 9.0,
                'cost_competitiveness': 7.0
            },
            {
                'country': 'Mexico',
                'country_code': 'MX',
                'tariff_rate': 'Free (USMCA)',
                'advantages': ['免稅', '成本競爭力', '製造能力強'],
                'considerations': ['品質控制', '物流時間'],
                'risk_level': 'Medium',
                'logistics_score': 8.0,
                'cost_competitiveness': 9.0
            },
            {
                'country': 'Germany',
                'country_code': 'DE',
                'tariff_rate': '2.5%',
                'advantages': ['高品質', '技術先進', '可靠供應'],
                'considerations': ['成本較高', '有關稅'],
                'risk_level': 'Low',
                'logistics_score': 8.5,
                'cost_competitiveness': 6.0
            }
        ]
        
        return supplier_recommendations
    
    def _compliance_guidance(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """提供合規指導"""
        guidance = {
            'import_requirements': [
                '正確的HTS分類',
                '準確的商品價值申報',
                '必要的進口許可證',
                '原產地證明文件'
            ],
            'documentation': [
                '商業發票',
                '裝箱單',
                '提單或空運單',
                '保險單據'
            ],
            'special_requirements': [
                'FDA註冊（如適用）',
                'FCC認證（電子產品）',
                '環保合規證明',
                '安全標準認證'
            ],
            'best_practices': [
                '建立內部合規程序',
                '定期培訓相關人員',
                '保持文件記錄完整',
                '與海關經紀人合作'
            ]
        }
        
        return guidance
    
    def _trade_strategy_advice(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """提供貿易策略建議"""
        strategy = {
            'short_term_actions': [
                '評估當前供應商的關稅影響',
                '研究適用的貿易協定',
                '優化現有進口流程'
            ],
            'medium_term_strategy': [
                '多元化供應商基地',
                '建立戰略庫存',
                '投資供應鏈技術'
            ],
            'long_term_planning': [
                '建立區域供應中心',
                '考慮本地化生產',
                '發展戰略夥伴關係'
            ],
            'risk_mitigation': [
                '貿易政策變化風險',
                '匯率波動風險',
                '供應中斷風險',
                '合規風險'
            ]
        }
        
        return strategy
    
    def _format_alternatives_response(self, alternatives: List[Dict[str, Any]], query: str) -> str:
        """格式化替代商品回應"""
        response = f"**🔄 替代商品建議**\n\n根據您的查詢「{query}」，以下是推薦的替代方案：\n\n"
        
        for i, alt in enumerate(alternatives, 1):
            response += f"**選項 {i}: HTS {alt['hts_code']}**\n"
            response += f"   商品描述：{alt['description']}\n"
            response += f"   當前稅率：{alt['current_tariff']}\n"
            response += f"   替代稅率：{alt['alternative_tariff']}\n"
            response += f"   潛在節省：{alt['potential_savings']}\n"
            response += f"   相似度：{alt['similarity_score']*100:.0f}%\n"
            response += f"   考慮因素：{', '.join(alt['considerations'])}\n\n"
        
        response += "**⚠️ 重要提醒：**\n"
        response += "- 替代商品必須符合您的功能和品質要求\n"
        response += "- 建議進行小批量測試驗證\n"
        response += "- 諮詢專業人士確認HTS分類的準確性\n"
        
        return response
    
    def _format_cost_optimization_response(self, strategies: List[Dict[str, Any]], query: str) -> str:
        """格式化成本優化回應 - 使用專業模板"""
        if not strategies:
            return self._format_no_optimization_response(query)

        # 準備模板數據
        template_data = {
            'executive_summary': self._generate_executive_summary(strategies, query),
            'option1_name': strategies[0]['strategy'],
            'option1_savings': strategies[0]['potential_savings'],
            'option1_difficulty': strategies[0]['implementation_difficulty'],
            'option1_timeline': strategies[0]['timeline'],
            'option1_requirements': ', '.join(strategies[0]['requirements']),
            'option2_name': strategies[1]['strategy'] if len(strategies) > 1 else 'N/A',
            'option2_savings': strategies[1]['potential_savings'] if len(strategies) > 1 else 'N/A',
            'option2_difficulty': strategies[1]['implementation_difficulty'] if len(strategies) > 1 else 'N/A',
            'option2_timeline': strategies[1]['timeline'] if len(strategies) > 1 else 'N/A',
            'option2_requirements': ', '.join(strategies[1]['requirements']) if len(strategies) > 1 else 'N/A',
            'cost_benefit_analysis': self._generate_cost_benefit_analysis(strategies),
            'risk_assessment': self._generate_risk_assessment(strategies),
            'implementation_roadmap': self._generate_implementation_roadmap(strategies),
            'next_steps': self._generate_next_steps(strategies)
        }

        # 使用專業模板
        response = ResponseTemplates.recommendation_template().format(**template_data)

        # 添加專業背景
        response = QualityEnhancers.add_professional_context(response, "strategic_advice")

        return response
    
    def _format_supplier_recommendations_response(self, recommendations: List[Dict[str, Any]], query: str) -> str:
        """格式化供應商推薦回應"""
        response = f"**🌍 供應商推薦**\n\n根據您的查詢「{query}」，推薦以下供應國家：\n\n"
        
        for i, rec in enumerate(recommendations, 1):
            response += f"**推薦 {i}: {rec['country']} ({rec['country_code']})**\n"
            response += f"   關稅稅率：{rec['tariff_rate']}\n"
            response += f"   風險等級：{rec['risk_level']}\n"
            response += f"   物流評分：{rec['logistics_score']}/10\n"
            response += f"   成本競爭力：{rec['cost_competitiveness']}/10\n"
            response += f"   優勢：{', '.join(rec['advantages'])}\n"
            response += f"   考慮因素：{', '.join(rec['considerations'])}\n\n"
        
        response += "**🔍 評估建議：**\n"
        response += "- 進行供應商實地考察\n"
        response += "- 評估品質管理體系\n"
        response += "- 考慮政治和經濟穩定性\n"
        response += "- 分析總體擁有成本\n"
        
        return response
    
    def _format_compliance_guidance_response(self, guidance: Dict[str, Any], query: str) -> str:
        """格式化合規指導回應"""
        response = f"**📋 合規指導**\n\n根據您的查詢「{query}」，以下是合規要求：\n\n"
        
        response += "**📄 進口要求：**\n"
        for req in guidance['import_requirements']:
            response += f"- {req}\n"
        
        response += "\n**📑 必要文件：**\n"
        for doc in guidance['documentation']:
            response += f"- {doc}\n"
        
        response += "\n**⚠️ 特殊要求：**\n"
        for req in guidance['special_requirements']:
            response += f"- {req}\n"
        
        response += "\n**✅ 最佳實踐：**\n"
        for practice in guidance['best_practices']:
            response += f"- {practice}\n"
        
        return response
    
    def _format_trade_strategy_response(self, strategy: Dict[str, Any], query: str) -> str:
        """格式化貿易策略回應"""
        response = f"**📈 貿易策略建議**\n\n根據您的查詢「{query}」，建議的策略如下：\n\n"
        
        response += "**🎯 短期行動（1-6個月）：**\n"
        for action in strategy['short_term_actions']:
            response += f"- {action}\n"
        
        response += "\n**📊 中期策略（6-18個月）：**\n"
        for action in strategy['medium_term_strategy']:
            response += f"- {action}\n"
        
        response += "\n**🚀 長期規劃（18個月以上）：**\n"
        for action in strategy['long_term_planning']:
            response += f"- {action}\n"
        
        response += "\n**⚠️ 風險管理：**\n"
        for risk in strategy['risk_mitigation']:
            response += f"- {risk}\n"
        
        return response
    
    # 輔助方法
    def _extract_product_info(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """提取商品信息"""
        return {'product': 'general', 'category': 'unknown'}
    
    def _analyze_current_costs(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """分析當前成本"""
        return {'current_tariff': '2.5%', 'volume': 1000000}
    
    def _extract_product_requirements(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """提取商品需求"""
        return {'category': 'electronics', 'quality': 'high'}
    
    def _identify_compliance_requirements(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """識別合規需求"""
        return {'product_type': 'general', 'import_country': 'US'}
    
    def _analyze_trade_requirements(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """分析貿易需求"""
        return {'business_type': 'import', 'scale': 'medium'}
    
    def _generate_alternative_action_items(self, alternatives: List[Dict[str, Any]]) -> List[str]:
        """生成替代方案行動項目"""
        return ["評估替代商品功能", "進行成本效益分析", "聯繫潛在供應商"]
    
    def _calculate_potential_savings(self, strategies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """計算潛在節省"""
        return {'total_potential_savings': '20-35%', 'annual_savings': '$100,000-$200,000'}
    
    def _get_supplier_evaluation_criteria(self) -> List[str]:
        """獲取供應商評估標準"""
        return ["品質標準", "交貨能力", "價格競爭力", "技術能力", "合規記錄"]
    
    def _generate_compliance_checklist(self, guidance: Dict[str, Any]) -> List[str]:
        """生成合規檢查清單"""
        return ["確認HTS分類", "準備進口文件", "獲得必要許可", "建立合規程序"]
    
    def _create_implementation_plan(self, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """創建實施計劃"""
        return {
            'phase_1': '評估和規劃（1-2個月）',
            'phase_2': '試點實施（3-6個月）',
            'phase_3': '全面推廣（6-12個月）'
        }

    def _format_no_optimization_response(self, query: str) -> str:
        """格式化無優化方案回應"""
        return f"""**💰 成本優化分析**

根據您的查詢「{query}」，目前的成本結構已相對優化。

**🔍 當前狀況評估：**
• 現有關稅稅率已處於較低水平
• 供應鏈配置相對合理
• 貿易協定利用程度較高

**💡 持續優化建議：**
• **定期檢視** - 每季度檢視關稅政策變化
• **供應商評估** - 年度供應商績效和成本評估
• **新協定關注** - 關注新簽署的貿易協定機會
• **技術升級** - 考慮供應鏈數位化和自動化

**📞 專業諮詢：**
建議定期與貿易合規專家和供應鏈顧問會面，確保持續優化。

---
*成本優化是一個持續過程，建議建立定期檢視機制。*"""

    def _generate_executive_summary(self, strategies: List[Dict[str, Any]], query: str) -> str:
        """生成執行摘要"""
        total_strategies = len(strategies)
        max_savings = max([self._extract_savings_percentage(s['potential_savings']) for s in strategies])

        return f"""基於您的查詢「{query}」，我們識別出 {total_strategies} 個主要的成本優化機會。
通過綜合實施這些策略，預期可實現最高 {max_savings}% 的成本節省。
建議優先實施風險較低、回報較高的策略，並建立分階段實施計劃。"""

    def _extract_savings_percentage(self, savings_str: str) -> float:
        """從節省字符串中提取百分比"""
        import re
        match = re.search(r'(\d+(?:\.\d+)?)', savings_str)
        return float(match.group(1)) if match else 0.0

    def _generate_cost_benefit_analysis(self, strategies: List[Dict[str, Any]]) -> str:
        """生成成本效益分析"""
        return """**投資回報分析：**
• **短期收益（6-12個月）：** 預期節省 $50,000 - $150,000
• **中期收益（1-2年）：** 預期節省 $200,000 - $500,000
• **長期收益（2-5年）：** 預期節省 $1,000,000+

**實施成本估算：**
• **諮詢費用：** $20,000 - $50,000
• **系統升級：** $30,000 - $100,000
• **培訓成本：** $10,000 - $25,000
• **機會成本：** $15,000 - $40,000

**投資回報率（ROI）：** 預期 300% - 500%
**回收期：** 8-15 個月"""

    def _generate_risk_assessment(self, strategies: List[Dict[str, Any]]) -> str:
        """生成風險評估"""
        return """**🔴 高風險因素：**
• **政策變化風險** - 貿易政策和關稅稅率可能調整
• **供應商風險** - 新供應商的品質和交期不確定性
• **合規風險** - 新分類或程序的合規複雜性

**🟡 中等風險因素：**
• **匯率波動** - 影響實際成本節省效果
• **市場變化** - 需求波動影響採購量
• **競爭反應** - 競爭對手可能採取類似策略

**🟢 風險緩解措施：**
• 建立多元化供應商組合
• 制定應急預案和備選方案
• 定期監控政策和市場變化
• 與專業顧問保持密切合作"""

    def _generate_implementation_roadmap(self, strategies: List[Dict[str, Any]]) -> str:
        """生成實施路徑"""
        return """**第一階段（1-3個月）：評估和準備**
• 詳細的現狀分析和基線建立
• 供應商盡職調查和評估
• 內部團隊培訓和能力建設
• 風險評估和緩解計劃制定

**第二階段（4-9個月）：試點實施**
• 選擇低風險產品進行試點
• 建立新的作業流程和程序
• 監控關鍵績效指標
• 收集反饋並優化流程

**第三階段（10-18個月）：全面推廣**
• 擴展到所有適用產品類別
• 建立長期供應商關係
• 持續優化和改進
• 建立定期檢視機制"""

    def _generate_next_steps(self, strategies: List[Dict[str, Any]]) -> str:
        """生成下一步行動"""
        return """**立即行動（本週內）：**
• 與管理層討論並獲得實施授權
• 組建跨部門實施團隊
• 制定詳細的項目計劃和預算

**短期行動（1個月內）：**
• 聘請專業貿易顧問進行詳細評估
• 開始供應商市場調研
• 建立項目管理和監控機制

**中期行動（3個月內）：**
• 完成試點方案設計
• 開始與潛在供應商談判
• 建立內部培訓計劃

**關鍵成功因素：**
• 高層管理支持和資源投入
• 跨部門協作和溝通
• 專業外部顧問支持
• 持續監控和調整能力"""
