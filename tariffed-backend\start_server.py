#!/usr/bin/env python3

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

from flask import Flask, send_from_directory, jsonify
from flask_cors import CORS

def create_simple_app():
    app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'src', 'static'))
    app.config['SECRET_KEY'] = 'tariffed-secret-key-2025'
    
    # Enable CORS for all routes
    CORS(app)
    
    @app.route('/test')
    def test():
        return jsonify({'status': 'success', 'message': 'TARIFFED Server is running!'})
    
    @app.route('/api/test')
    def api_test():
        return jsonify({'status': 'success', 'message': 'API is working!'})

    @app.route('/api/docs/')
    def api_docs():
        return send_from_directory(app.static_folder, 'api-docs.html')

    @app.route('/api/query', methods=['POST'])
    def mock_query():
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({'status': 'error', 'message': '請提供查詢內容'}), 400

        query = data['query'].lower()

        # 如果查詢包含進口國相關關鍵字，返回圖表數據
        if any(keyword in query for keyword in ['進口國', '主要進口', '進口來源', '進口統計', '貿易夥伴']):
            response_text = """**美國主要進口國（按進口價值排序，數據來源：U.S. Census Bureau, 2023）**

| 排名 | 國家 | 進口總值（美元） | 主要商品類別 | 進口佔比（%） |
|------|------|------------------|--------------|--------------|
| 1 | **加拿大 (CA)** | 約 1,200 億 | 農產品、機械、汽車零件、化工品 | 12% |
| 2 | **墨西哥 (MX)** | 約 1,100 億 | 汽車、機械、農產品、電子產品 | 11% |
| 3 | **中國 (CN)** | 約 1,000 億 | 電子產品、機械、紡織、玩具 | 10% |
| 4 | **日本 (JP)** | 約 300 億 | 汽車、機械、電子產品、化學品 | 3% |
| 5 | **德國 (DE)** | 約 250 億 | 機械、汽車、化學品、醫療器械 | 2.5% |
| 6 | **英國 (UK)** | 約 200 億 | 化學品、機械、醫療器械、食品 | 2% |
| 7 | **法國 (FR)** | 約 180 億 | 化學品、機械、食品、醫療器械 | 1.8% |
| 8 | **意大利 (IT)** | 約 150 億 | 機械、化學品、食品、汽車零件 | 1.5% |
| 9 | **巴西 (BR)** | 約 120 億 | 農產品、機械、化學品、汽車零件 | 1.2% |

> **備註**
> - 以上數據為2023年全年進口總值，未扣除季節性波動。
> - 進口佔比是以美國全年進口總值（約 10,000 億美元）計算。
> - 主要商品類別以HS 6 位碼（HTS）分類為基礎，並結合美國關稅表（HTS）中對應的關稅率與進口規範。

### 為什麼這些國家是主要進口來源？

| 國家 | 主要原因 |
|------|----------|
| **加拿大** | 近距離、自由貿易協定（USMCA）降低關稅，且兩國共享大量跨境基礎設施。 |
| **墨西哥** | 同樣受USMCA保護，且美國對墨西哥的汽車零件與農產品需求高。 |
| **中國** | 全球製造中心，提供大量電子產品與機械，儘管關稅波動，但成本仍具競爭力。 |"""

            return jsonify({'status': 'success', 'data': response_text})

        # 其他查詢的模擬回應
        return jsonify({
            'status': 'success',
            'data': f'AI 分析結果：您查詢的是「{data["query"]}」。這是一個模擬回應，實際系統會使用 AI 代理處理查詢並可能包含圖表數據。'
        })
    
    @app.route('/', defaults={'path': ''})
    @app.route('/<path:path>')
    def serve(path):
        static_folder_path = app.static_folder
        if static_folder_path is None:
            return "Static folder not configured", 404

        if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
            return send_from_directory(static_folder_path, path)
        else:
            index_path = os.path.join(static_folder_path, 'index.html')
            if os.path.exists(index_path):
                return send_from_directory(static_folder_path, 'index.html')
            else:
                return jsonify({
                    'status': 'info',
                    'message': 'TARIFFED Backend Server',
                    'version': '1.0.0',
                    'endpoints': {
                        'test': '/test',
                        'api_test': '/api/test',
                        'api_docs': '/api/docs/'
                    }
                })

    return app

if __name__ == '__main__':
    print("Starting TARIFFED Backend Server...")
    print("Server will be available at: http://localhost:5001")
    print("Test endpoint: http://localhost:5001/test")
    print("API test endpoint: http://localhost:5001/api/test")
    
    app = create_simple_app()
    app.run(host='0.0.0.0', port=5001, debug=True)
