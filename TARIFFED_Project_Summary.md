# TARIFFED 專案總結報告

## 專案概述

TARIFFED 是一個基於 AI 的美國關稅查詢系統，旨在幫助使用者理解和查詢美國協調關稅表 (HTS) 相關資訊。本專案成功地重新實現了原始 Microsoft Azure 版本的功能，但使用了開源和非 Azure 的技術棧。

## 原始專案分析

### 原始技術棧 (Azure 版本)
- **AI 服務**: Azure AI Agent Service
- **搜尋服務**: Grounding with Bing Search  
- **後端框架**: .NET 9 Framework, .NET Aspire
- **前端框架**: Blazor
- **資料庫**: SQL Server 2022
- **開發工具**: Visual Studio 2022, Azure CLI
- **容器化**: Docker Desktop

### 核心功能
1. 使用 AI 代理處理自然語言查詢
2. HTS 資料庫搜尋和瀏覽
3. 關稅率和貿易數據查詢
4. 替代商品建議
5. 貿易統計分析

## 重新實現的技術架構

### 技術棧選擇
- **後端框架**: Python Flask
- **前端框架**: React + Tailwind CSS + shadcn/ui
- **AI 框架**: LangChain + OpenAI API
- **資料庫**: SQLite (可輕鬆遷移到 PostgreSQL)
- **開發工具**: Python, Node.js, npm/pnpm
- **部署**: 整合式 Flask 應用

### 系統架構

```
前端 (React)
    ↓ HTTP API 調用
後端 (Flask)
    ↓ 查詢處理
AI 代理 (LangChain + OpenAI)
    ↓ 資料檢索
資料庫 (SQLite)
```

## 實現的功能

### 1. 後端 API 服務
- **`/api/query`**: AI 自然語言查詢處理
- **`/api/hts/search`**: HTS 條目關鍵字搜尋
- **`/api/hts/{id}`**: 特定 HTS 條目詳細資訊
- **`/api/countries`**: 國家列表
- **`/api/trade-data/{id}`**: 貿易數據查詢

### 2. 前端使用者介面
- 響應式設計，支援桌面和行動裝置
- 智能查詢輸入框
- 分頁式結果展示 (AI 回應 / HTS 搜尋結果)
- 現代化 UI 設計，使用 Tailwind CSS

### 3. AI 代理功能
- 自然語言查詢理解
- 資料庫查詢生成
- 上下文感知回應
- 多語言支援 (繁體中文)

### 4. 資料庫設計
完整的 HTS 資料結構：
- `hts_sections`: HTS 章節
- `hts_chapters`: HTS 章
- `hts_headings`: HTS 標題
- `hts_subheadings`: HTS 副標題 (包含稅率資訊)
- `countries`: 國家資料
- `trade_data`: 貿易統計數據

## 專案檔案結構

```
tariffed-backend/
├── src/
│   ├── models/
│   │   └── hts.py              # 資料庫模型
│   ├── routes/
│   │   └── tariff.py           # API 路由
│   ├── services/
│   │   └── ai_agent.py         # AI 代理服務
│   ├── data/
│   │   └── sample_data.py      # 範例資料導入
│   ├── database/
│   │   └── app.db              # SQLite 資料庫
│   ├── static/                 # 前端建置檔案
│   └── main.py                 # Flask 主應用
├── run_integrated.py           # 整合運行腳本
├── requirements.txt            # Python 依賴
└── venv/                       # 虛擬環境

tariffed-frontend/
├── src/
│   ├── components/
│   │   └── ui/                 # shadcn/ui 組件
│   ├── App.jsx                 # 主應用組件
│   └── main.jsx                # 入口點
├── dist/                       # 建置輸出
├── package.json                # Node.js 依賴
└── vite.config.js              # Vite 配置
```

## 成功實現的功能演示

### 1. HTS 搜尋功能
- 使用者可以輸入關鍵字 (如 "horse") 搜尋相關的 HTS 條目
- 系統返回匹配的條目，包含：
  - HTS 代碼 (如 0101.21.00)
  - 商品描述 (如 "Horses: Pure bred breeding animals")
  - 一般稅率 (如 "Free")
  - 特殊稅率
  - 計量單位

### 2. 整合式部署
- 前端和後端整合在單一 Flask 應用中
- 前端建置檔案自動服務於 Flask 靜態目錄
- API 和前端路由無縫整合

### 3. 現代化使用者介面
- 美觀的漸層背景設計
- 卡片式佈局
- 分頁式內容組織
- 響應式設計

## 技術優勢

### 相較於原始 Azure 版本的優勢：
1. **成本效益**: 避免 Azure 服務費用，使用開源技術
2. **技術靈活性**: 可部署到任何支援 Python 的環境
3. **可擴展性**: 模組化設計，易於擴展和維護
4. **跨平台**: 不依賴特定雲端平台
5. **開發效率**: 使用熟悉的 Python 和 JavaScript 技術棧

### 保持的核心功能：
1. AI 驅動的自然語言查詢
2. 完整的 HTS 資料庫結構
3. 現代化的 Web 使用者介面
4. RESTful API 設計
5. 響應式前端設計

## 部署和運行

### 開發環境運行
```bash
# 後端
cd tariffed-backend
source venv/bin/activate
python run_integrated.py

# 前端開發 (可選)
cd tariffed-frontend
pnpm run dev --host
```

### 生產環境部署
- 使用 `run_integrated.py` 腳本運行整合應用
- 前端已建置並整合到 Flask 靜態目錄
- 可使用 Gunicorn 或 uWSGI 進行生產部署
- 支援 Docker 容器化部署

## 未來改進建議

### 短期改進
1. **資料庫遷移**: 從 SQLite 遷移到 PostgreSQL 以支援更大規模的資料
2. **真實資料導入**: 從 USITC 官方來源導入完整的 HTS 資料
3. **AI 模型優化**: 微調 LLM 以提供更準確的關稅相關回應
4. **搜尋功能增強**: 添加模糊搜尋和自動完成功能

### 長期改進
1. **多語言支援**: 擴展到更多語言
2. **資料視覺化**: 添加圖表和統計視覺化
3. **使用者認證**: 添加使用者帳戶和個人化功能
4. **API 文檔**: 使用 Swagger/OpenAPI 生成 API 文檔
5. **監控和日誌**: 添加應用監控和錯誤追蹤

## 結論

本專案成功地重新實現了 TARIFFED 關稅查詢系統，使用開源技術替代了原始的 Azure 技術棧，同時保持了核心功能和使用者體驗。系統展示了：

1. **技術可行性**: 證明了使用開源技術可以實現相同的功能
2. **成本效益**: 大幅降低了雲端服務依賴和成本
3. **開發效率**: 使用熟悉的技術棧提高了開發速度
4. **可維護性**: 模組化設計使系統易於維護和擴展

這個實現為需要類似功能但希望避免特定雲端平台鎖定的組織提供了一個可行的替代方案。

