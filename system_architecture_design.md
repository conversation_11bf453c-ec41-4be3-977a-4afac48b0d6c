# TARIFFED 專案系統架構設計

## 1. 總體架構

本專案將採用前後端分離的架構，後端提供 RESTful API 服務，前端負責使用者介面展示。AI 代理模組將作為後端服務的一部分，負責處理自然語言查詢和與資料庫互動。資料庫將用於儲存 HTS 相關數據。

```mermaid
graph TD
    User -->|Web Browser| Frontend
    Frontend -->|HTTP/HTTPS| Backend (Flask/FastAPI)
    Backend -->|API Calls| AI_Agent_Module
    Backend -->|SQL Queries| PostgreSQL
    AI_Agent_Module -->|LLM API| LLM_Service
    AI_Agent_Module -->|SQL Queries| PostgreSQL
```

## 2. 資料庫設計 (PostgreSQL)

考慮到 HTS 資料的結構和查詢需求，我們將設計以下主要表格：

### `hts_sections` (HTS 章節)
*   `section_id` (PRIMARY KEY, INT): 章節編號
*   `title` (TEXT): 章節標題

### `hts_chapters` (HTS 章)
*   `chapter_id` (PRIMARY KEY, INT): 章編號
*   `section_id` (FOREIGN KEY, INT): 所屬章節編號
*   `title` (TEXT): 章標題

### `hts_headings` (HTS 標題)
*   `heading_id` (PRIMARY KEY, TEXT): 標題編號 (例如: 0101)
*   `chapter_id` (FOREIGN KEY, INT): 所屬章編號
*   `description` (TEXT): 標題描述

### `hts_subheadings` (HTS 副標題)
*   `subheading_id` (PRIMARY KEY, TEXT): 副標題編號 (例如: 0101.11)
*   `heading_id` (FOREIGN KEY, TEXT): 所屬標題編號
*   `description` (TEXT): 副標題描述
*   `unit_of_quantity` (TEXT): 計量單位
*   `general_rate` (TEXT): 一般稅率
*   `special_rate` (TEXT): 特殊稅率
*   `column_2_rate` (TEXT): 第二欄稅率

### `countries` (國家)
*   `country_id` (PRIMARY KEY, TEXT): 國家代碼 (例如: US, CN)
*   `country_name` (TEXT): 國家名稱

### `trade_data` (貿易數據 - 簡化)
*   `id` (PRIMARY KEY, SERIAL)
*   `subheading_id` (FOREIGN KEY, TEXT): HTS 副標題編號
*   `country_id` (FOREIGN KEY, TEXT): 國家代碼
*   `year` (INT): 年份
*   `import_value` (NUMERIC): 進口金額
*   `import_quantity` (NUMERIC): 進口數量

**資料導入策略**：
*   需要找到公開的 HTS 資料來源 (例如 USITC 網站)。
*   編寫 Python 腳本解析資料，並將其插入到 PostgreSQL 資料庫中。
*   考慮資料更新頻率，可能需要定期執行資料導入腳本。

## 3. 後端 API 設計 (Flask/FastAPI)

後端將提供以下 RESTful API 接口：

*   **`/api/query` (POST)**
    *   **描述**：接收使用者自然語言查詢，並返回 AI 代理處理後的結果。
    *   **請求體**：`{ 


    "query": "string" }`
    *   **回應體**：`{ "status": "success", "data": "string" }` 或 `{ "status": "error", "message": "string" }`

*   **`/api/hts/search` (GET)**
    *   **描述**：根據關鍵字搜尋 HTS 條目。
    *   **查詢參數**：`keyword` (string)
    *   **回應體**：`{ "status": "success", "data": [ { "id": "string", "description": "string", ... } ] }`

*   **`/api/hts/{hts_id}` (GET)**
    *   **描述**：獲取特定 HTS 條目的詳細資訊。
    *   **回應體**：`{ "status": "success", "data": { "id": "string", "description": "string", ... } }`

## 4. AI 代理模組設計 (LangChain/LlamaIndex)

AI 代理模組將是後端的核心組件，負責解析使用者查詢、與 LLM 互動、執行工具（例如資料庫查詢）並生成回應。

*   **查詢解析**：使用 LLM 或規則引擎解析使用者查詢意圖，提取關鍵實體（例如國家、商品、年份）。
*   **工具使用**：
    *   **資料庫查詢工具**：將自然語言查詢轉換為 SQL 查詢，從 PostgreSQL 資料庫中檢索 HTS 和貿易數據。
    *   **外部搜尋工具**：如果需要外部資訊（例如替代商品），可以整合 Google Custom Search API 或其他搜尋服務。
*   **回應生成**：將檢索到的資訊和 LLM 的生成能力結合，生成清晰、準確且有用的回應。

## 5. 前端設計 (React)

前端應用將提供直觀的使用者介面，包括：

*   **查詢輸入框**：讓使用者輸入自然語言查詢。
*   **結果展示區**：以清晰易讀的方式展示 AI 代理的回應，可能包括表格、圖表或純文字。
*   **HTS 瀏覽功能**：允許使用者瀏覽 HTS 結構和詳細資訊。
*   **響應式設計**：確保應用在不同設備上都能良好運行。

## 6. 開發環境與工具

*   **後端**：Python 3.x, Flask/FastAPI, SQLAlchemy (ORM), psycopg2 (PostgreSQL 驅動), LangChain/LlamaIndex, OpenAI (或其他 LLM SDK)
*   **前端**：Node.js, npm/yarn, React, Create React App (或 Vite), Axios (HTTP 客戶端)
*   **資料庫**：PostgreSQL
*   **版本控制**：Git
*   **容器化**：Docker (用於部署)

## 7. 部署考量

*   **後端**：可以使用 Gunicorn/uWSGI 配合 Nginx 部署 Flask/FastAPI 應用，或直接使用 Docker 容器部署。
*   **前端**：靜態檔案可以部署到任何靜態網站託管服務，或與後端一起部署。
*   **資料庫**：可以部署在獨立的伺服器或雲端資料庫服務上。

## 下一步計畫

*   **資料收集**：尋找並下載 HTS 資料。
*   **資料庫實現**：建立 PostgreSQL 資料庫並設計表格。
*   **後端開發**：實現 API 接口和 AI 代理模組。
*   **前端開發**：構建使用者介面並與後端整合。

