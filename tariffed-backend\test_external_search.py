#!/usr/bin/env python3
"""
外部搜尋功能測試腳本
"""

import os
import sys
import time

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(__file__))

def test_tavily_service():
    """測試 Tavily 搜尋服務"""
    print("🔍 測試 Tavily 搜尋服務...")
    
    try:
        from src.services.external_search_service import TavilySearchService
        
        tavily = TavilySearchService()
        
        # 測試基本搜尋
        print("\n📋 測試基本搜尋...")
        result = tavily.search("US tariff rates 2024")
        
        if result.get('success', False):
            print("✅ Tavily 基本搜尋成功")
            data = result.get('data', {})
            print(f"   找到 {len(data.get('results', []))} 個結果")
            print(f"   答案: {data.get('answer', 'N/A')[:100]}...")
        else:
            print(f"❌ Tavily 基本搜尋失敗: {result.get('error', 'Unknown error')}")
        
        # 測試關稅相關搜尋
        print("\n🏷️ 測試關稅相關搜尋...")
        result = tavily.search(
            "HTS classification electronics import tariff",
            search_depth='advanced',
            max_results=3
        )
        
        if result.get('success', False):
            print("✅ Tavily 關稅搜尋成功")
        else:
            print(f"❌ Tavily 關稅搜尋失敗: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tavily 服務測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_result_processor():
    """測試搜尋結果處理器"""
    print("\n🔧 測試搜尋結果處理器...")
    
    try:
        from src.services.search_result_processor import SearchResultProcessor
        
        processor = SearchResultProcessor()
        
        # 模擬 Tavily 結果
        mock_tavily_data = {
            'answer': 'US tariff rates vary by product category and country of origin.',
            'query': 'US tariff rates 2024',
            'results': [
                {
                    'title': 'US Tariff Database - USITC',
                    'url': 'https://hts.usitc.gov/',
                    'content': 'The United States International Trade Commission provides comprehensive tariff information for all HTS codes.',
                    'score': 0.95
                },
                {
                    'title': 'CBP Tariff Information',
                    'url': 'https://www.cbp.gov/trade/tariff',
                    'content': 'US Customs and Border Protection official tariff rates and classifications.',
                    'score': 0.90
                }
            ]
        }
        
        # 處理結果
        processed = processor.process_tavily_results(mock_tavily_data)
        
        if processed.get('results'):
            print("✅ 搜尋結果處理成功")
            print(f"   處理了 {len(processed['results'])} 個結果")
            print(f"   可信度分數: {processed.get('credibility_score', 0):.2f}")
            print(f"   摘要: {processed.get('summary', 'N/A')[:100]}...")
        else:
            print("❌ 搜尋結果處理失敗")
        
        # 測試格式化顯示
        formatted = processor.format_for_display(processed)
        print(f"\n📄 格式化結果長度: {len(formatted)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜尋結果處理器測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_external_search_service():
    """測試外部搜尋服務"""
    print("\n🌐 測試外部搜尋服務...")
    
    try:
        from src.services.external_search_service import external_search_service
        
        # 測試一般搜尋
        print("\n🔍 測試一般搜尋...")
        result = external_search_service.search("US China trade war tariffs")
        
        if result.get('success', False):
            print("✅ 一般搜尋成功")
            print(f"   使用來源: {result.get('source', 'unknown')}")
        else:
            print(f"❌ 一般搜尋失敗: {result.get('error', 'Unknown error')}")
        
        # 測試關稅專門搜尋
        print("\n🏷️ 測試關稅專門搜尋...")
        result = external_search_service.search_tariff_related("electronics import duty")
        
        if result.get('success', False):
            print("✅ 關稅專門搜尋成功")
        else:
            print(f"❌ 關稅專門搜尋失敗: {result.get('error', 'Unknown error')}")
        
        # 測試貿易新聞搜尋
        print("\n📰 測試貿易新聞搜尋...")
        result = external_search_service.search_trade_news("latest trade policy changes")
        
        if result.get('success', False):
            print("✅ 貿易新聞搜尋成功")
        else:
            print(f"❌ 貿易新聞搜尋失敗: {result.get('error', 'Unknown error')}")
        
        # 測試搜尋統計
        stats = external_search_service.get_search_stats()
        print(f"\n📊 搜尋統計:")
        print(f"   總搜尋次數: {stats['total_searches']}")
        print(f"   成功率: {stats['success_rate']:.1%}")
        print(f"   使用的來源: {stats['sources_used']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 外部搜尋服務測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_agent_integration():
    """測試多代理系統整合"""
    print("\n🤖 測試多代理系統整合...")
    
    try:
        from src.services.multi_agent_service import multi_agent_service
        
        # 測試外部搜尋功能
        print("\n🔍 測試多代理外部搜尋...")
        result = multi_agent_service.search_external(
            "latest US tariff policy changes",
            search_type="trade_news"
        )
        
        if result.get('success', False):
            print("✅ 多代理外部搜尋成功")
            print(f"   搜尋來源: {result.get('source', 'unknown')}")
            print(f"   可信度分數: {result.get('credibility_score', 0):.2f}")
            print(f"   回應長度: {len(result.get('response', ''))}")
        else:
            print(f"❌ 多代理外部搜尋失敗: {result.get('error', 'Unknown error')}")
        
        # 測試增強查詢
        print("\n🚀 測試增強查詢...")
        result = multi_agent_service.enhanced_query_with_search(
            "最新的電子產品進口關稅政策"
        )
        
        if result.get('success', False):
            print("✅ 增強查詢成功")
            print(f"   整合模式: {result.get('integration_mode', False)}")
            print(f"   回應長度: {len(result.get('response', ''))}")
        else:
            print(f"❌ 增強查詢失敗: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多代理系統整合測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """測試 API 端點（需要服務器運行）"""
    print("\n🌐 測試 API 端點...")
    
    try:
        import requests
        
        base_url = "http://localhost:5001/api"
        
        # 測試外部搜尋 API
        print("\n🔍 測試外部搜尋 API...")
        response = requests.post(
            f"{base_url}/search/external",
            json={
                "query": "US tariff rates electronics",
                "search_type": "tariff",
                "max_results": 3
            },
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 外部搜尋 API 測試成功")
            data = response.json()
            print(f"   狀態: {data.get('status', 'unknown')}")
        else:
            print(f"❌ 外部搜尋 API 測試失敗: {response.status_code}")
        
        # 測試增強查詢 API
        print("\n🚀 測試增強查詢 API...")
        response = requests.post(
            f"{base_url}/query/enhanced",
            json={
                "query": "最新的中美貿易政策變化"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 增強查詢 API 測試成功")
            data = response.json()
            print(f"   狀態: {data.get('status', 'unknown')}")
            print(f"   整合模式: {data.get('metadata', {}).get('integration_mode', False)}")
        else:
            print(f"❌ 增強查詢 API 測試失敗: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("⚠️ 無法連接到服務器，跳過 API 測試")
        return True
    except Exception as e:
        print(f"❌ API 端點測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🌟 TARIFFED 外部搜尋功能測試")
    print("=" * 50)
    
    # 測試各個組件
    tavily_success = test_tavily_service()
    time.sleep(1)  # 避免 API 限制
    
    processor_success = test_search_result_processor()
    time.sleep(1)
    
    service_success = test_external_search_service()
    time.sleep(1)
    
    integration_success = test_multi_agent_integration()
    time.sleep(1)
    
    api_success = test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("📋 測試結果總結:")
    print(f"   Tavily 服務: {'✅ 通過' if tavily_success else '❌ 失敗'}")
    print(f"   結果處理器: {'✅ 通過' if processor_success else '❌ 失敗'}")
    print(f"   搜尋服務: {'✅ 通過' if service_success else '❌ 失敗'}")
    print(f"   多代理整合: {'✅ 通過' if integration_success else '❌ 失敗'}")
    print(f"   API 端點: {'✅ 通過' if api_success else '❌ 失敗'}")
    
    all_success = all([tavily_success, processor_success, service_success, integration_success, api_success])
    
    if all_success:
        print("\n🎊 所有測試通過！外部搜尋功能已成功整合。")
        print("\n🚀 功能特色:")
        print("   • Tavily API 整合")
        print("   • 智能結果處理")
        print("   • 多來源搜尋支援")
        print("   • 可信度評分")
        print("   • 專業格式化顯示")
        print("   • 多代理系統整合")
        return True
    else:
        print("\n⚠️ 部分測試失敗，請檢查錯誤信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
